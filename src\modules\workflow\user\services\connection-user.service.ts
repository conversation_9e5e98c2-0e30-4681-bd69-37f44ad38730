import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { Connection } from '../../entities/connection.entity';
import { ConnectionRepository } from '../../repositories/connection.repository';
import { NodeRepository } from '../../repositories/node.repository';
import { WorkflowRepository } from '../../repositories/workflow.repository';
import { CreateConnectionDto } from '../../dto';
import { ConnectionResponseDto } from '../../dto/response/connection-response.dto';
import { WORKFLOW_ERROR_CODES } from '../../exceptions/workflow-error.code';

/**
 * Service xử lý business logic cho Connection - User
 */
@Injectable()
export class ConnectionUserService {
  constructor(
    private readonly connectionRepository: ConnectionRepository,
    private readonly nodeRepository: NodeRepository,
    private readonly workflowRepository: WorkflowRepository,
  ) {}

  /**
   * Tạo connection mới cho user
   */
  async createConnection(
    workflowId: string, 
    createConnectionDto: CreateConnectionDto, 
    userId: number
  ): Promise<ConnectionResponseDto> {
    // 1. Kiểm tra workflow tồn tại và thuộc về user
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Kiểm tra source node tồn tại và thuộc workflow
    const sourceNode = await this.nodeRepository.findByIdAndWorkflow(
      createConnectionDto.sourceNodeId,
      workflowId
    );
    if (!sourceNode) {
      throw new AppException(WORKFLOW_ERROR_CODES.SOURCE_NODE_NOT_FOUND);
    }

    // 3. Kiểm tra target node tồn tại và thuộc workflow
    const targetNode = await this.nodeRepository.findByIdAndWorkflow(
      createConnectionDto.targetNodeId,
      workflowId
    );
    if (!targetNode) {
      throw new AppException(WORKFLOW_ERROR_CODES.TARGET_NODE_NOT_FOUND);
    }

    // 4. Kiểm tra không tự kết nối với chính mình
    if (createConnectionDto.sourceNodeId === createConnectionDto.targetNodeId) {
      throw new AppException(WORKFLOW_ERROR_CODES.SELF_CONNECTION_NOT_ALLOWED);
    }

    // 5. Kiểm tra connection đã tồn tại chưa
    const connectionExists = await this.connectionRepository.exists(
      createConnectionDto.sourceNodeId,
      createConnectionDto.targetNodeId,
      createConnectionDto.sourceHandle,
      createConnectionDto.targetHandle
    );
    if (connectionExists) {
      throw new AppException(WORKFLOW_ERROR_CODES.CONNECTION_ALREADY_EXISTS);
    }

    // 6. Kiểm tra có tạo circular dependency không
    const wouldCreateCycle = await this.connectionRepository.wouldCreateCycle(
      createConnectionDto.sourceNodeId,
      createConnectionDto.targetNodeId
    );
    if (wouldCreateCycle) {
      throw new AppException(WORKFLOW_ERROR_CODES.CIRCULAR_DEPENDENCY_DETECTED);
    }

    // 7. Tạo connection
    const connectionData: Partial<Connection> = {
      workflowId,
      sourceNodeId: createConnectionDto.sourceNodeId,
      targetNodeId: createConnectionDto.targetNodeId,
      sourceHandle: createConnectionDto.sourceHandle,
      sourceHandleIndex: createConnectionDto.sourceHandleIndex || 0,
      targetHandle: createConnectionDto.targetHandle,
      targetHandleIndex: createConnectionDto.targetHandleIndex || 0,
    };

    const connection = await this.connectionRepository.create(connectionData);

    // 8. Cập nhật workflow updated_at
    await this.workflowRepository.updateTimestamp(workflowId);

    return this.mapToResponseDto(connection);
  }

  /**
   * Lấy tất cả connections trong workflow cho user
   */
  async getConnectionsByWorkflow(workflowId: string, userId: number): Promise<ConnectionResponseDto[]> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Lấy tất cả connections
    const connections = await this.connectionRepository.findByWorkflowId(workflowId);

    return connections.map(connection => this.mapToResponseDto(connection));
  }

  /**
   * Lấy connections của một node cho user
   */
  async getConnectionsByNode(
    workflowId: string, 
    nodeId: string, 
    userId: number
  ): Promise<{
    incoming: ConnectionResponseDto[];
    outgoing: ConnectionResponseDto[];
  }> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Kiểm tra node tồn tại
    const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
    if (!node) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
    }

    // 3. Lấy connections
    const incomingConnections = await this.connectionRepository.findByTargetNode(nodeId);
    const outgoingConnections = await this.connectionRepository.findBySourceNode(nodeId);

    return {
      incoming: incomingConnections.map(conn => this.mapToResponseDto(conn)),
      outgoing: outgoingConnections.map(conn => this.mapToResponseDto(conn)),
    };
  }

  /**
   * Xóa connection cho user
   */
  async deleteConnection(workflowId: string, connectionId: number, userId: number): Promise<void> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Kiểm tra connection tồn tại và thuộc workflow
    const connection = await this.connectionRepository.findByIdAndWorkflow(connectionId, workflowId);
    if (!connection) {
      throw new AppException(WORKFLOW_ERROR_CODES.CONNECTION_NOT_FOUND);
    }

    // 3. Xóa connection
    await this.connectionRepository.delete(connectionId);

    // 4. Cập nhật workflow updated_at
    await this.workflowRepository.updateTimestamp(workflowId);
  }

  /**
   * Validate workflow ownership cho user
   */
  private async validateWorkflowOwnership(workflowId: string, userId: number): Promise<void> {
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
    }

    if (workflow.userId !== userId) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_ACCESS_DENIED);
    }
  }

  /**
   * Map Connection entity sang ConnectionResponseDto
   */
  private mapToResponseDto(connection: Connection): ConnectionResponseDto {
    return {
      id: connection.id,
      sourceNodeId: connection.sourceNodeId,
      targetNodeId: connection.targetNodeId,
      sourceHandle: connection.sourceHandle,
      sourceHandleIndex: connection.sourceHandleIndex,
      targetHandle: connection.targetHandle,
      targetHandleIndex: connection.targetHandleIndex,
    };
  }
}
