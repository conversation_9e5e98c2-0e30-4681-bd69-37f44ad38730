import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IWorkflowSettings } from '../../interfaces';

/**
 * DTO cho response workflow
 */
export class WorkflowResponseDto {
  @ApiProperty({
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên workflow',
    example: 'Workflow xử lý đơn hàng tự động',
  })
  @Expose()
  name: string;


  @ApiProperty({
    description: 'Trạng thái hoạt động của workflow',
    example: true,
  })
  @Expose()
  isActive: boolean;


  @ApiPropertyOptional({
    description: 'Cấu hình workflow',
    example: {
      notification_type: 'email',
      timeout: 3600,
      retry_policy: {
        enabled: true,
        max_retries: 3,
        delay: 1000
      },
      variables: {
        api_key: 'secret_key',
        base_url: 'https://api.example.com'
      }
    },
    nullable: true,
  })
  @Expose()
  settings: IWorkflowSettings | null;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp milliseconds)',
    example: 1703123456789,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (Unix timestamp milliseconds)',
    example: 1703123456789,
  })
  @Expose()
  updatedAt: number;
}
