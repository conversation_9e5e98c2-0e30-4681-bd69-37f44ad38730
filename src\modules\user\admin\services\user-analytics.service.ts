import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AuthVerificationLog } from '../../entities/auth-verification-log.entity';
import { DeviceInfo } from '../../entities/device-info.entity';
import { User } from '../../entities/user.entity';
import { AuthStatusEnum } from '../../enums/auth-status.enum';
import { AuthMethodEnum } from '../../enums/auth-method.enum';
import {
  LoginHistoryQueryDto,
  DeviceAnalyticsQueryDto,
  LoginHistoryResponseDto,
  DeviceInfoResponseDto,
  UserAnalyticsOverviewDto,
  LoginTimeSeriesDataDto,
  DeviceTimeSeriesDataDto,
  AuthMethodStatsDto,
  BrowserStatsDto,
  OperatingSystemStatsDto,
  LoginChartQueryDto,
  LoginChartResponseDto,
  TimeUnit,
  PlatformEnum,
  UserTypeStatsDto,
} from '../dto/user-analytics.dto';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý thống kê người dùng cho admin
 */
@Injectable()
export class UserAnalyticsService {
  constructor(
    @InjectRepository(AuthVerificationLog)
    private readonly authLogRepository: Repository<AuthVerificationLog>,
    @InjectRepository(DeviceInfo)
    private readonly deviceInfoRepository: Repository<DeviceInfo>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Lấy lịch sử đăng nhập với phân trang
   */
  async getLoginHistory(
    query: LoginHistoryQueryDto,
  ): Promise<PaginatedResult<LoginHistoryResponseDto>> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      userId,
      startDate,
      endDate,
      authMethod,
      status,
      platform,
      isSuccessful,
    } = query;
    const skip = (page - 1) * limit;

    // Build query conditions
    const whereConditions: any = {};

    if (userId) {
      whereConditions.userId = userId;
    }

    if (startDate && endDate) {
      const startTimestamp = new Date(startDate).getTime();
      const endTimestamp = new Date(endDate).getTime();
      whereConditions.createdAt = Between(startTimestamp, endTimestamp);
    }

    if (authMethod) {
      whereConditions.authMethod = authMethod;
    }

    // Xử lý isSuccessful parameter
    if (isSuccessful !== undefined) {
      whereConditions.status = isSuccessful
        ? AuthStatusEnum.SUCCESS
        : AuthStatusEnum.FAILED;
    } else if (status) {
      whereConditions.status = status;
    }

    // Build query builder for complex conditions
    let queryBuilder = this.authLogRepository.createQueryBuilder('log');

    // Apply basic where conditions
    Object.keys(whereConditions).forEach((key) => {
      if (key === 'createdAt') {
        // Between object has from and to properties
        const betweenCondition = whereConditions[key];
        queryBuilder = queryBuilder.andWhere(
          `log.${key} BETWEEN :startTime AND :endTime`,
          {
            startTime: betweenCondition.from,
            endTime: betweenCondition.to,
          },
        );
      } else {
        queryBuilder = queryBuilder.andWhere(`log.${key} = :${key}`, {
          [key]: whereConditions[key],
        });
      }
    });

    // Xử lý platform filter dựa trên userAgent
    if (platform) {
      switch (platform) {
        case 'web':
          queryBuilder = queryBuilder.andWhere(
            '(log.userAgent ILIKE :chrome OR log.userAgent ILIKE :firefox OR log.userAgent ILIKE :safari OR log.userAgent ILIKE :edge) AND log.userAgent NOT ILIKE :mobile',
            {
              chrome: '%Chrome%',
              firefox: '%Firefox%',
              safari: '%Safari%',
              edge: '%Edge%',
              mobile: '%Mobile%',
            },
          );
          break;
        case 'mobile':
          queryBuilder = queryBuilder.andWhere(
            'log.userAgent ILIKE :mobile OR log.userAgent ILIKE :android OR log.userAgent ILIKE :iphone',
            {
              mobile: '%Mobile%',
              android: '%Android%',
              iphone: '%iPhone%',
            },
          );
          break;
        case 'desktop':
          queryBuilder = queryBuilder.andWhere(
            'log.userAgent ILIKE :windows OR log.userAgent ILIKE :macos OR log.userAgent ILIKE :linux',
            {
              windows: '%Windows%',
              macos: '%Macintosh%',
              linux: '%Linux%',
            },
          );
          break;
        case 'api':
          queryBuilder = queryBuilder.andWhere(
            'log.userAgent IS NULL OR log.userAgent = :empty OR log.userAgent ILIKE :postman OR log.userAgent ILIKE :curl',
            {
              empty: '',
              postman: '%Postman%',
              curl: '%curl%',
            },
          );
          break;
      }
    }

    // Execute query with pagination and sorting
    const [logs, total] = await queryBuilder
      .orderBy(`log.${sortBy}`, sortDirection as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    // Get unique user IDs and fetch user info
    const userIds = [
      ...new Set(logs.map((log) => log.userId).filter((id) => id)),
    ];
    const users = await this.userRepository.findByIds(userIds);
    const userMap = new Map(users.map((user) => [user.id, user]));

    // Transform to response DTO
    const items: LoginHistoryResponseDto[] = logs.map((log) => {
      const user = userMap.get(log.userId);
      return {
        id: log.id,
        userId: log.userId,
        userName: user?.fullName || 'N/A',
        userEmail: user?.email || 'N/A',
        authMethod: log.authMethod,
        status: log.status,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        codeSentAt: log.codeSentAt,
        verifiedAt: log.verifiedAt,
        attemptCount: log.attemptCount,
        createdAt: log.createdAt,
      };
    });

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thông tin thiết bị với phân trang
   */
  async getDeviceAnalytics(
    query: DeviceAnalyticsQueryDto,
  ): Promise<PaginatedResult<DeviceInfoResponseDto>> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'lastLogin',
      sortDirection = 'DESC',
      userId,
      startDate,
      endDate,
      trustedOnly,
    } = query;
    const skip = (page - 1) * limit;

    // Build query conditions
    const whereConditions: any = {};

    if (userId) {
      whereConditions.userId = userId;
    }

    if (startDate && endDate) {
      const startTimestamp = new Date(startDate).getTime();
      const endTimestamp = new Date(endDate).getTime();
      whereConditions.createdAt = Between(startTimestamp, endTimestamp);
    }

    if (trustedOnly) {
      whereConditions.isTrusted = true;
    }

    // Execute query without join
    const [devices, total] = await this.deviceInfoRepository.findAndCount({
      where: whereConditions,
      order: { [sortBy]: sortDirection },
      skip,
      take: limit,
    });

    // Get unique user IDs and fetch user info
    const userIds = [
      ...new Set(devices.map((device) => device.userId).filter((id) => id)),
    ];
    const users = await this.userRepository.findByIds(userIds);
    const userMap = new Map(users.map((user) => [user.id, user]));

    // Transform to response DTO
    const items: DeviceInfoResponseDto[] = devices.map((device) => {
      const user = userMap.get(device.userId);
      return {
        id: device.id,
        userId: device.userId,
        userName: user?.fullName || 'N/A',
        userEmail: user?.email || 'N/A',
        fingerprint: device.fingerprint,
        ipAddress: device.ipAddress,
        userAgent: device.userAgent,
        browser: device.browser,
        operatingSystem: device.operatingSystem,
        isTrusted: device.isTrusted,
        lastLogin: device.lastLogin,
        createdAt: device.createdAt,
        updatedAt: device.updatedAt,
      };
    });

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thống kê tổng quan
   */
  async getOverviewStats(): Promise<UserAnalyticsOverviewDto> {
    const now = Date.now();
    const last24h = now - 24 * 60 * 60 * 1000;
    const last7d = now - 7 * 24 * 60 * 60 * 1000;
    const last30d = now - 30 * 24 * 60 * 60 * 1000;

    // Thống kê thiết bị
    const totalDevices = await this.deviceInfoRepository.count();
    const trustedDevices = await this.deviceInfoRepository.count({
      where: { isTrusted: true },
    });
    const untrustedDevices = totalDevices - trustedDevices;

    // Thống kê đăng nhập
    const totalLogins = await this.authLogRepository.count();
    const successfulLogins = await this.authLogRepository.count({
      where: { status: AuthStatusEnum.SUCCESS },
    });
    const failedLogins = totalLogins - successfulLogins;
    const successRate =
      totalLogins > 0 ? (successfulLogins / totalLogins) * 100 : 0;

    // Người dùng hoạt động
    const activeUsersLast24h = await this.authLogRepository
      .createQueryBuilder('log')
      .select('COUNT(DISTINCT log.userId)', 'count')
      .where('log.createdAt >= :last24h', { last24h })
      .andWhere('log.status = :status', { status: AuthStatusEnum.SUCCESS })
      .getRawOne()
      .then((result) => parseInt(result.count) || 0);

    const activeUsersLast7d = await this.authLogRepository
      .createQueryBuilder('log')
      .select('COUNT(DISTINCT log.userId)', 'count')
      .where('log.createdAt >= :last7d', { last7d })
      .andWhere('log.status = :status', { status: AuthStatusEnum.SUCCESS })
      .getRawOne()
      .then((result) => parseInt(result.count) || 0);

    const activeUsersLast30d = await this.authLogRepository
      .createQueryBuilder('log')
      .select('COUNT(DISTINCT log.userId)', 'count')
      .where('log.createdAt >= :last30d', { last30d })
      .andWhere('log.status = :status', { status: AuthStatusEnum.SUCCESS })
      .getRawOne()
      .then((result) => parseInt(result.count) || 0);

    return {
      totalDevices,
      trustedDevices,
      untrustedDevices,
      totalLogins,
      successfulLogins,
      failedLogins,
      successRate: Math.round(successRate * 100) / 100,
      activeUsersLast24h,
      activeUsersLast7d,
      activeUsersLast30d,
    };
  }

  /**
   * Tự động xác định đơn vị thời gian phù hợp dựa trên khoảng cách
   */
  private determineTimeUnit(startDate: Date, endDate: Date): TimeUnit {
    const diffMs = endDate.getTime() - startDate.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);
    const diffWeeks = diffDays / 7;
    const diffMonths = diffDays / 30;
    const diffYears = diffDays / 365;

    // Nếu khoảng cách <= 1 ngày: chia theo giờ
    if (diffDays <= 1) {
      return TimeUnit.HOUR;
    }
    // Nếu khoảng cách <= 2 tuần: chia theo ngày
    else if (diffWeeks <= 2) {
      return TimeUnit.DAY;
    }
    // Nếu khoảng cách <= 2 tháng: chia theo tuần
    else if (diffMonths <= 2) {
      return TimeUnit.WEEK;
    }
    // Nếu khoảng cách <= 2 năm: chia theo tháng
    else if (diffYears <= 2) {
      return TimeUnit.MONTH;
    }
    // Nếu khoảng cách > 2 năm: chia theo năm
    else {
      return TimeUnit.YEAR;
    }
  }

  /**
   * Tạo format SQL và label dựa trên đơn vị thời gian
   */
  private getTimeFormatConfig(timeUnit: TimeUnit) {
    switch (timeUnit) {
      case TimeUnit.HOUR:
        return {
          sqlFormat:
            "TO_CHAR(TO_TIMESTAMP(log.createdAt / 1000), 'YYYY-MM-DD HH24:00:00')",
          labelFormat: (timestamp: number) => {
            const date = new Date(timestamp);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
          },
        };
      case TimeUnit.DAY:
        return {
          sqlFormat:
            "TO_CHAR(TO_TIMESTAMP(log.createdAt / 1000), 'YYYY-MM-DD')",
          labelFormat: (timestamp: number) => {
            const date = new Date(timestamp);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          },
        };
      case TimeUnit.WEEK:
        return {
          sqlFormat:
            "TO_CHAR(DATE_TRUNC('week', TO_TIMESTAMP(log.createdAt / 1000)), 'YYYY-MM-DD')",
          labelFormat: (timestamp: number) => {
            const date = new Date(timestamp);
            const weekStart = new Date(
              date.setDate(date.getDate() - date.getDay()),
            );
            return `Tuần ${weekStart.getFullYear()}-${String(weekStart.getMonth() + 1).padStart(2, '0')}-${String(weekStart.getDate()).padStart(2, '0')}`;
          },
        };
      case TimeUnit.MONTH:
        return {
          sqlFormat: "TO_CHAR(TO_TIMESTAMP(log.createdAt / 1000), 'YYYY-MM')",
          labelFormat: (timestamp: number) => {
            const date = new Date(timestamp);
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          },
        };
      case TimeUnit.YEAR:
        return {
          sqlFormat: "TO_CHAR(TO_TIMESTAMP(log.createdAt / 1000), 'YYYY')",
          labelFormat: (timestamp: number) => {
            const date = new Date(timestamp);
            return `${date.getFullYear()}`;
          },
        };
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đăng nhập theo thời gian với tự động tính toán đơn vị
   */
  async getLoginTimeSeries(
    query: LoginChartQueryDto,
  ): Promise<LoginChartResponseDto> {
    const startDate = new Date(query.startDate);
    const endDate = new Date(query.endDate);
    const startTimestamp = startDate.getTime();
    const endTimestamp = endDate.getTime();

    // Tự động xác định đơn vị thời gian nếu không được cung cấp
    const timeUnit =
      query.timeUnit || this.determineTimeUnit(startDate, endDate);
    const formatConfig = this.getTimeFormatConfig(timeUnit);

    const rawData = await this.authLogRepository
      .createQueryBuilder('log')
      .select([
        `${formatConfig.sqlFormat} as period`,
        'COUNT(*) as loginCount',
        "COUNT(CASE WHEN log.status = 'SUCCESS' THEN 1 END) as successCount",
        "COUNT(CASE WHEN log.status != 'SUCCESS' THEN 1 END) as failedCount",
        'COUNT(DISTINCT log.userId) as uniqueUsers',
      ])
      .where('log.createdAt BETWEEN :start AND :end', {
        start: startTimestamp,
        end: endTimestamp,
      })
      .groupBy(`${formatConfig.sqlFormat}`)
      .orderBy('period', 'ASC')
      .getRawMany();

    const data = rawData.map((row) => {
      // Tạo timestamp từ period string
      let timestamp: number;
      if (timeUnit === TimeUnit.HOUR) {
        timestamp = new Date(row.period).getTime();
      } else if (timeUnit === TimeUnit.DAY) {
        timestamp = new Date(row.period).getTime();
      } else if (timeUnit === TimeUnit.WEEK) {
        timestamp = new Date(row.period).getTime();
      } else if (timeUnit === TimeUnit.MONTH) {
        timestamp = new Date(row.period + '-01').getTime();
      } else if (timeUnit === TimeUnit.YEAR) {
        timestamp = new Date(row.period + '-01-01').getTime();
      } else {
        timestamp = new Date(row.period).getTime();
      }

      return {
        label: formatConfig.labelFormat(timestamp),
        timestamp,
        loginCount: parseInt(row.logincount) || 0,
        successCount: parseInt(row.successcount) || 0,
        failedCount: parseInt(row.failedcount) || 0,
        uniqueUsers: parseInt(row.uniqueusers) || 0,
      };
    });

    // Tính tổng từ dữ liệu
    const totals = data.reduce(
      (acc, item) => ({
        totalLogins: acc.totalLogins + item.loginCount,
        totalSuccessLogins: acc.totalSuccessLogins + item.successCount,
        totalFailedLogins: acc.totalFailedLogins + item.failedCount,
      }),
      {
        totalLogins: 0,
        totalSuccessLogins: 0,
        totalFailedLogins: 0,
      },
    );

    // Tính tổng unique users riêng từ database (vì có thể trùng user giữa các khoảng thời gian)
    const uniqueUsersResult = await this.authLogRepository
      .createQueryBuilder('log')
      .select('COUNT(DISTINCT log.userId)', 'totalUniqueUsers')
      .where('log.createdAt BETWEEN :start AND :end', {
        start: startTimestamp,
        end: endTimestamp,
      })
      .getRawOne();

    return {
      timeUnit,
      data,
      ...totals,
      totalUniqueUsers: parseInt(uniqueUsersResult.totalUniqueUsers) || 0,
    };
  }

  /**
   * Lấy dữ liệu biểu đồ thiết bị theo thời gian
   */
  async getDeviceTimeSeries(
    startDate: string,
    endDate: string,
  ): Promise<DeviceTimeSeriesDataDto[]> {
    const startTimestamp = new Date(startDate).getTime();
    const endTimestamp = new Date(endDate).getTime();

    const rawData = await this.deviceInfoRepository
      .createQueryBuilder('device')
      .select([
        'DATE(TO_TIMESTAMP(device.createdAt / 1000)) as date',
        'COUNT(*) as newDevices',
        'COUNT(CASE WHEN device.isTrusted = true THEN 1 END) as newTrustedDevices',
      ])
      .where('device.createdAt BETWEEN :start AND :end', {
        start: startTimestamp,
        end: endTimestamp,
      })
      .groupBy('DATE(TO_TIMESTAMP(device.createdAt / 1000))')
      .orderBy('date', 'ASC')
      .getRawMany();

    // Calculate cumulative totals
    let cumulativeTotal = 0;
    return rawData.map((row) => {
      const newDevices = parseInt(row.newdevices) || 0;
      cumulativeTotal += newDevices;

      return {
        date: row.date,
        newDevices,
        totalDevices: cumulativeTotal,
        newTrustedDevices: parseInt(row.newtrusteddevices) || 0,
      };
    });
  }

  /**
   * Lấy thống kê theo phương thức xác thực
   */
  async getAuthMethodStats(): Promise<AuthMethodStatsDto[]> {
    const rawData = await this.authLogRepository
      .createQueryBuilder('log')
      .select([
        'log.authMethod as method',
        'COUNT(*) as count',
        "COUNT(CASE WHEN log.status = 'SUCCESS' THEN 1 END) as successCount",
      ])
      .groupBy('log.authMethod')
      .getRawMany();

    return rawData.map((row) => {
      const count = parseInt(row.count) || 0;
      const successCount = parseInt(row.successcount) || 0;
      const successRate = count > 0 ? (successCount / count) * 100 : 0;

      return {
        method: row.method as AuthMethodEnum,
        count,
        successRate: Math.round(successRate * 100) / 100,
      };
    });
  }

  /**
   * Lấy thống kê theo trình duyệt
   */
  async getBrowserStats(): Promise<BrowserStatsDto[]> {
    const totalDevices = await this.deviceInfoRepository.count();

    const rawData = await this.deviceInfoRepository
      .createQueryBuilder('device')
      .select(['device.browser as browser', 'COUNT(*) as deviceCount'])
      .where('device.browser IS NOT NULL')
      .groupBy('device.browser')
      .orderBy('deviceCount', 'DESC')
      .getRawMany();

    return rawData.map((row) => {
      const deviceCount = parseInt(row.devicecount) || 0;
      const percentage =
        totalDevices > 0 ? (deviceCount / totalDevices) * 100 : 0;

      return {
        browser: row.browser || 'Unknown',
        deviceCount,
        percentage: Math.round(percentage * 100) / 100,
      };
    });
  }

  /**
   * Lấy thống kê theo hệ điều hành
   */
  async getOperatingSystemStats(): Promise<OperatingSystemStatsDto[]> {
    const totalDevices = await this.deviceInfoRepository.count();

    const rawData = await this.deviceInfoRepository
      .createQueryBuilder('device')
      .select([
        'device.operatingSystem as operatingSystem',
        'COUNT(*) as deviceCount',
      ])
      .where('device.operatingSystem IS NOT NULL')
      .groupBy('device.operatingSystem')
      .orderBy('deviceCount', 'DESC')
      .getRawMany();

    return rawData.map((row) => {
      const deviceCount = parseInt(row.devicecount) || 0;
      const percentage =
        totalDevices > 0 ? (deviceCount / totalDevices) * 100 : 0;

      return {
        operatingSystem: row.operatingsystem || 'Unknown',
        deviceCount,
        percentage: Math.round(percentage * 100) / 100,
      };
    });
  }

  /**
   * Lấy thống kê loại tài khoản người dùng (biểu đồ tròn)
   */
  async getUserTypeStats(): Promise<UserTypeStatsDto[]> {
    const query = `
      SELECT
        type as userType,
        COUNT(*) as count
      FROM users
      WHERE is_active = true
      GROUP BY type
      ORDER BY count DESC
    `;

    const results = await this.userRepository.query(query);

    // Tính tổng số user để tính phần trăm
    const totalUsers = results.reduce(
      (sum: number, row: any) => sum + parseInt(row.count),
      0,
    );

    // Map labels cho từng loại
    const typeLabels: Record<string, string> = {
      INDIVIDUAL: 'Cá nhân',
      BUSINESS: 'Doanh nghiệp',
    };

    return results.map((row: any) => {
      const count = parseInt(row.count) || 0;
      const percentage = totalUsers > 0 ? (count / totalUsers) * 100 : 0;

      return {
        userType: row.usertype,
        count,
        percentage: Math.round(percentage * 100) / 100,
        label: typeLabels[row.usertype] || row.usertype,
      };
    });
  }
}
