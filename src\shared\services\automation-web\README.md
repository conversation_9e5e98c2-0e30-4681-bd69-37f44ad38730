# AutomationWebService

Service để tương tác với automation-web API, cung cấp các phương thức HTTP cơ bản để gọi API từ automation-web service.

## Cấu hình

Thêm các biến môi trường sau vào file `.env`:

```env
# Automation Web API Configuration
AUTOMATION_WEB_API_URL=http://localhost:8080
AUTOMATION_WEB_API_KEY=your_automation_web_api_key_here
AUTOMATION_WEB_TIMEOUT=30000
```

### Biến môi trường

- `AUTOMATION_WEB_API_URL`: URL base của automation-web API (mặc định: `http://localhost:8080`)
- `AUTOMATION_WEB_API_KEY`: API key để xác thực với automation-web API (tùy chọn)
- `AUTOMATION_WEB_TIMEOUT`: Timeout cho request tính bằng milliseconds (mặc định: `30000`)

## Sử dụng

### Import Module

```typescript
import { AutomationWebModule } from '@/shared/services/automation-web';

@Module({
  imports: [AutomationWebModule],
  // ...
})
export class YourModule {}
```

### Inject Service

```typescript
import { AutomationWebService } from '@/shared/services/automation-web';

@Injectable()
export class YourService {
  constructor(private readonly automationWebService: AutomationWebService) {}

  async example() {
    // Sử dụng service
    const result = await this.automationWebService.get('/api/endpoint');
    return result;
  }
}
```

## API Methods

### GET Request

```typescript
const response = await automationWebService.get<DataType>(
  '/api/endpoint',
  { param1: 'value1', param2: 'value2' }, // query params (tùy chọn)
  { timeout: 5000, headers: { 'Custom-Header': 'value' } }, // options (tùy chọn)
);
```

### POST Request

```typescript
const response = await automationWebService.post<ResponseType>(
  '/api/endpoint',
  { data: 'value' }, // request body (tùy chọn)
  { timeout: 5000, headers: { 'Custom-Header': 'value' } }, // options (tùy chọn)
);
```

### PUT Request

```typescript
const response = await automationWebService.put<ResponseType>(
  '/api/endpoint',
  { data: 'updated_value' }, // request body (tùy chọn)
  { timeout: 5000 }, // options (tùy chọn)
);
```

### DELETE Request

```typescript
const response = await automationWebService.delete<ResponseType>(
  '/api/endpoint',
  { timeout: 5000 }, // options (tùy chọn)
);
```

## Response Format

Tất cả các method trả về response theo format:

```typescript
interface AutomationWebResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}
```

## Utility Methods

### Health Check

Kiểm tra kết nối với automation-web API:

```typescript
const isHealthy = await automationWebService.healthCheck();
console.log('Automation-web API is healthy:', isHealthy);
```

### Get Configuration

Lấy thông tin cấu hình hiện tại:

```typescript
const config = automationWebService.getConfig();
console.log('Current config:', config);
// Output: { baseUrl: 'http://localhost:8080', timeout: 30000, hasApiKey: true }
```

## Specialized Methods

### Create Zalo QR Code Session

Tạo session QR code cho Zalo Personal login:

```typescript
const result = await automationWebService.createZaloQRCodeSession(
  'session-uuid-123',
  'user-id-456',
);

if (result.success) {
  console.log('QR Code URL:', result.data.qrCodeUrl);
  console.log('Session ID:', result.data.sessionId);
  console.log('Expires At:', result.data.expiresAt);
}
```

## Error Handling

Service tự động xử lý lỗi và throw `AppException` với các error code phù hợp:

- `401 Unauthorized`: Lỗi xác thực API
- `403 Forbidden`: Không có quyền truy cập
- `404 Not Found`: Endpoint không tồn tại
- `500+ Server Error`: Lỗi server automation-web
- `Other errors`: Lỗi khác (Bad Request)

```typescript
try {
  const result = await automationWebService.get('/api/endpoint');
  // Xử lý kết quả thành công
} catch (error) {
  if (error instanceof AppException) {
    // Xử lý lỗi từ automation-web API
    console.error('Automation-web API error:', error.message);
  }
  // Xử lý lỗi khác
}
```

## Logging

Service tự động log các request và response ở level DEBUG, và log lỗi ở level ERROR.

## Authentication

Nếu `AUTOMATION_WEB_API_KEY` được cấu hình, service sẽ tự động thêm headers xác thực:

- `Authorization: Bearer {API_KEY}`
- `X-API-Key: {API_KEY}`

Bạn có thể tùy chỉnh cách xác thực bằng cách sửa method `getDefaultHeaders()` trong service.

## Ví dụ sử dụng thực tế

```typescript
@Injectable()
export class WorkflowService {
  constructor(private readonly automationWebService: AutomationWebService) {}

  async createWorkflow(workflowData: any) {
    try {
      const response = await this.automationWebService.post(
        '/api/workflows',
        workflowData,
      );

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create workflow');
      }
    } catch (error) {
      this.logger.error('Error creating workflow:', error);
      throw error;
    }
  }

  async getWorkflowStatus(workflowId: string) {
    const response = await this.automationWebService.get(
      `/api/workflows/${workflowId}/status`,
    );
    return response.data;
  }

  async executeWorkflow(workflowId: string, params: any) {
    const response = await this.automationWebService.post(
      `/api/workflows/${workflowId}/execute`,
      params,
      { timeout: 60000 }, // Tăng timeout cho workflow execution
    );
    return response.data;
  }
}
```
