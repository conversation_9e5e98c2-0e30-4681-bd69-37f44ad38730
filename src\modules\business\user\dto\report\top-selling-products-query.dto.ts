import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON><PERSON>, IsEnum, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho loại dữ liệu sản phẩm bán chạy
 */
export enum TopSellingProductsDataType {
  REVENUE = 'REVENUE',
  QUANTITY = 'QUANTITY',
  ORDERS = 'ORDERS',
}

/**
 * DTO cho query parameters của API sản phẩm bán chạy
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class TopSellingProductsQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu sắp xếp
   */
  @ApiProperty({
    description: 'Loại dữ liệu sắp xếp',
    enum: TopSellingProductsDataType,
    example: TopSellingProductsDataType.REVENUE,
    required: false,
    default: TopSellingProductsDataType.REVENUE,
  })
  @IsOptional()
  @IsEnum(TopSellingProductsDataType)
  type?: TopSellingProductsDataType;

  /**
   * Số lượng sản phẩm tối đa trả về
   */
  @ApiProperty({
    description: 'Số lượng sản phẩm tối đa trả về',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * ID danh mục sản phẩm
   */
  @ApiProperty({
    description: 'ID danh mục sản phẩm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID danh mục phải là số' })
  @Min(1, { message: 'ID danh mục phải lớn hơn 0' })
  @Type(() => Number)
  categoryId?: number;
}
