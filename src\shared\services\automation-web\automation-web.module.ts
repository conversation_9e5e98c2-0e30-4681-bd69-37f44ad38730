import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AutomationWebService } from './automation-web.service';

/**
 * Module cung cấp AutomationWebService để tương tác với automation-web API
 */
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [AutomationWebService],
  exports: [AutomationWebService],
})
export class AutomationWebModule {}
