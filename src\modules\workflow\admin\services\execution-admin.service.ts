import { Injectable, Logger } from '@nestjs/common';
import { WorkflowExecutionResponseDto, NodeExecutionResponseDto } from '../../dto/response/execution-response.dto';

/**
 * Service xử lý execution cho admin
 */
@Injectable()
export class ExecutionAdminService {
  private readonly logger = new Logger(ExecutionAdminService.name);

  /**
   * Thực thi workflow (admin)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow
   * @returns Promise<WorkflowExecutionResponseDto>
   */
  async executeWorkflow(
    employeeId: number,
    workflowId: string,
  ): Promise<WorkflowExecutionResponseDto> {
    this.logger.log(`Admin executing workflow ${workflowId} by employee ${employeeId}`);

    // TODO: Implement admin workflow execution logic
    // 1. Validate workflow exists (admin can execute any workflow)
    // 2. Create execution record with admin context
    // 3. Queue workflow execution job
    // 4. Return execution response

    throw new Error('Method not implemented');
  }

  /**
   * Th<PERSON><PERSON> thi node đơn lẻ (admin)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow
   * @param nodeId ID của node
   * @returns Promise<NodeExecutionResponseDto>
   */
  async executeNode(
    employeeId: number,
    workflowId: string,
    nodeId: string,
  ): Promise<NodeExecutionResponseDto> {
    this.logger.log(`Admin executing node ${nodeId} in workflow ${workflowId} by employee ${employeeId}`);

    // TODO: Implement admin node execution logic
    // 1. Validate workflow and node exist (admin can execute any node)
    // 2. Create execution record for single node with admin context
    // 3. Queue node execution job
    // 4. Return execution response

    throw new Error('Method not implemented');
  }
}
