import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ZaloZnsCampaignService } from '../services/zalo-zns-campaign.service';
import {
  CreateZnsCampaignDto,
  CreatePersonalizedZnsCampaignDto,
  UpdateZnsCampaignDto,
  ZnsCampaignQueryDto,
  ZnsCampaignResponseDto,
  SendSingleZnsDto,
  SendBatchZnsDto,
  ZnsJobResponseDto,
  BulkDeleteZnsCampaignDto,
  BulkDeleteZnsCampaignResponseDto,
} from '../dto/zalo';
import { ZaloZnsCampaign } from '../entities/zalo-zns-campaign.entity';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';

/**
 * Controller xử lý tất cả API liên quan đến Zalo ZNS Campaigns
 * Bao gồm tạo, quản lý, gửi và theo dõi ZNS (Zalo Notification Service) campaigns
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ZNS_CAMPAIGN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/zns/zns-campaigns')
export class ZaloZnsCampaignController {
  constructor(private readonly znsCampaignService: ZaloZnsCampaignService) {}

  /**
   * Tạo chiến dịch ZNS mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo chiến dịch ZNS (Zalo Notification Service) mới',
    description: `Tạo chiến dịch ZNS mới với các tùy chọn:
    - Chọn ZNS template đã được Zalo approve
    - Thiết lập target audience hoặc segments
    - Cấu hình parameters cho template
    - Lên lịch gửi hoặc gửi ngay lập tức
    - Thiết lập tracking và analytics
    - Hỗ trợ personalization với custom fields

    **Các loại Target Audience:**
    - PHONE_LIST: Gửi đến danh sách số điện thoại cụ thể
    - SEGMENT: Gửi đến một segment đã tạo
    - AUDIENCE_LIST: Gửi đến danh sách audience IDs

    **Trạng thái Campaign:**
    - DRAFT: Nháp, chưa gửi
    - SCHEDULED: Đã lên lịch
    - SENT: Đã gửi
    - FAILED: Gửi thất bại`,
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến dịch ZNS thành công',
    schema: {
      example: {
        success: true,
        message: 'Tạo chiến dịch ZNS thành công',
        data: {
          id: 789,
          userId: 123,
          oaId: 'oa123456789',
          name: 'ZNS Campaign - Order Confirmation',
          description: 'Gửi thông báo xác nhận đơn hàng cho khách hàng',
          templateId: 'template_12345',
          templateData: {
            shopName: 'RedAI Shop',
            orderStatus: 'Đã xác nhận',
          },
          phoneList: ['**********', '0987654321'],
          status: 'DRAFT',
          totalMessages: 2,
          sentMessages: 0,
          failedMessages: 0,
          scheduledAt: 1640995200000,
          createdAt: 1719504000000,
          updatedAt: 1719504000000,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      examples: {
        'validation-error': {
          summary: 'Lỗi validation',
          value: {
            success: false,
            message: 'Validation failed',
            errors: [
              'Template ID không được để trống',
              'Target audience phải lớn hơn 0',
              'phoneList không được để trống khi targetAudienceType = PHONE_LIST',
            ],
          },
        },
        'missing-phone-list': {
          summary: 'Thiếu danh sách số điện thoại',
          value: {
            success: false,
            message: 'Danh sách số điện thoại không được để trống',
            errorCode: 'ZNS_CAMPAIGN_CREATION_FAILED',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Official Account',
    schema: {
      example: {
        success: false,
        message: 'Không tìm thấy Official Account',
        errorCode: 'ZNS_UNAUTHORIZED',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Official Account hoặc Template',
    schema: {
      example: {
        success: false,
        message: 'Zalo Official Account với ID "invalid-oa-id" không tồn tại',
        errorCode: 'OA_NOT_FOUND',
      },
    },
  })
  @ApiBody({
    description: 'Dữ liệu tạo chiến dịch ZNS với các trường hợp khác nhau',
    examples: {
      'phone-list-draft': {
        summary: '1. Gửi đến danh sách số điện thoại - Nháp',
        description: 'Tạo campaign nháp gửi đến danh sách số điện thoại cụ thể',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Thông báo đơn hàng - Khách VIP',
          description: 'Gửi thông báo xác nhận đơn hàng cho khách hàng VIP',
          templateId: 'template_order_confirmation_123',
          templateData: {
            shopName: 'RedAI Shop',
            customerName: '{{customerName}}',
            orderId: '{{orderId}}',
            orderStatus: 'Đã xác nhận',
            totalAmount: '{{totalAmount}}',
            deliveryDate: '{{deliveryDate}}',
          },
          targetAudienceType: 'PHONE_LIST',
          phoneList: ['**********', '0987654321', '0123456789'],
          status: 'DRAFT',
        },
      },
      'phone-list-scheduled': {
        summary: '2. Gửi đến danh sách số điện thoại - Lên lịch',
        description: 'Tạo campaign lên lịch gửi vào thời gian cụ thể',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Khuyến mãi Black Friday 2024',
          description: 'Thông báo chương trình khuyến mãi Black Friday',
          templateId: 'template_promotion_456',
          templateData: {
            shopName: 'RedAI Shop',
            promotionName: 'Black Friday Sale',
            discountPercent: '50%',
            validUntil: '30/11/2024',
            promoCode: 'BF2024',
          },
          targetAudienceType: 'PHONE_LIST',
          phoneList: ['**********', '0987654321', '0123456789', '0909123456'],
          status: 'SCHEDULED',
          scheduledAt: 1732982400000,
        },
      },
      'segment-target': {
        summary: '3. Gửi đến Segment',
        description:
          'Tạo campaign gửi đến một segment khách hàng đã định nghĩa',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Thông báo sản phẩm mới - Segment VIP',
          description: 'Giới thiệu sản phẩm mới cho khách hàng VIP',
          templateId: 'template_new_product_789',
          templateData: {
            shopName: 'RedAI Shop',
            productName: 'iPhone 15 Pro Max',
            productPrice: '29.990.000 VND',
            launchDate: '15/09/2024',
            preOrderLink: 'https://redai.shop/iphone15',
          },
          targetAudienceType: 'SEGMENT',
          segmentId: 123,
          status: 'DRAFT',
        },
      },
      'audience-list': {
        summary: '4. Gửi đến danh sách Audience IDs',
        description: 'Tạo campaign gửi đến danh sách audience IDs cụ thể',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Nhắc nhở thanh toán',
          description: 'Nhắc nhở khách hàng thanh toán đơn hàng đang chờ',
          templateId: 'template_payment_reminder_101',
          templateData: {
            shopName: 'RedAI Shop',
            customerName: '{{customerName}}',
            orderId: '{{orderId}}',
            totalAmount: '{{totalAmount}}',
            dueDate: '{{dueDate}}',
            paymentLink: '{{paymentLink}}',
          },
          targetAudienceType: 'AUDIENCE_LIST',
          audienceIds: [1, 2, 3, 4, 5],
          status: 'DRAFT',
        },
      },
      'otp-verification': {
        summary: '5. Template OTP/Xác thực',
        description: 'Gửi mã OTP hoặc xác thực tài khoản',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Xác thực tài khoản mới',
          description: 'Gửi mã OTP xác thực cho tài khoản mới đăng ký',
          templateId: 'template_otp_verification_202',
          templateData: {
            appName: 'RedAI App',
            otpCode: '{{otpCode}}',
            expiryMinutes: '5',
            supportPhone: '1900-1234',
          },
          targetAudienceType: 'PHONE_LIST',
          phoneList: ['**********'],
          status: 'DRAFT',
        },
      },
      'transaction-notification': {
        summary: '6. Thông báo giao dịch',
        description: 'Thông báo giao dịch thành công/thất bại',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Thông báo giao dịch thành công',
          description: 'Thông báo giao dịch chuyển tiền thành công',
          templateId: 'template_transaction_success_303',
          templateData: {
            bankName: 'RedAI Bank',
            customerName: '{{customerName}}',
            transactionId: '{{transactionId}}',
            amount: '{{amount}}',
            transactionTime: '{{transactionTime}}',
            balance: '{{balance}}',
          },
          targetAudienceType: 'PHONE_LIST',
          phoneList: ['**********'],
          status: 'DRAFT',
        },
      },
    },
  })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.createCampaign(
      user.id,
      createDto.integrationId,
      createDto,
    );
    return ApiResponseDto.success(result, 'Tạo chiến dịch ZNS thành công');
  }

  /**
   * Tạo chiến dịch ZNS với cá nhân hóa template data
   */
  @Post('personalized')
  @ApiOperation({
    summary: 'Tạo chiến dịch ZNS với cá nhân hóa',
    description: `Tạo chiến dịch ZNS mới với khả năng cá nhân hóa template data cho từng khách hàng:
    - Hỗ trợ mapping từ trường audience (name, email, phoneNumber, address) sang template variables
    - Hỗ trợ mapping từ custom fields sang template variables
    - Có thể kết hợp common data với personalized data
    - Tự động áp dụng cá nhân hóa khi gửi tin nhắn`,
  })
  @ApiBody({
    type: CreatePersonalizedZnsCampaignDto,
    description: 'Thông tin chiến dịch ZNS cá nhân hóa cần tạo',
    examples: {
      'personalized-campaign': {
        summary: 'Chiến dịch cá nhân hóa cơ bản',
        description:
          'Ví dụ tạo chiến dịch với cá nhân hóa tên khách hàng và email',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Chiến dịch thông báo đơn hàng cá nhân hóa',
          description:
            'Gửi thông báo đơn hàng với thông tin cá nhân hóa cho từng khách hàng',
          templateId: 'template123456789',
          personalizedTemplateData: {
            usePersonalization: true,
            commonData: {
              shopName: 'RedAI Shop',
              orderStatus: 'Đã xác nhận',
            },
            fieldMapping: {
              customerName: 'name',
              customerEmail: 'email',
              customerPhone: 'phoneNumber',
            },
            customFieldMapping: {
              orderCode: 'order_code',
              totalAmount: 'total_amount',
            },
          },
          targetAudienceType: 'SEGMENT',
          segmentId: 123,
          status: 'DRAFT',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến dịch ZNS cá nhân hóa thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloZnsCampaign) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy Official Account hoặc Segment/Audience',
  })
  async createPersonalizedCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreatePersonalizedZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.createPersonalizedCampaign(
      user.id,
      createDto.integrationId,
      createDto,
    );
    return ApiResponseDto.success(
      result,
      'Tạo chiến dịch ZNS cá nhân hóa thành công',
    );
  }

  /**
   * Preview template data cá nhân hóa cho một audience
   */
  @Post('personalized/preview/:audienceId')
  @ApiOperation({
    summary: 'Preview template data cá nhân hóa',
    description:
      'Xem trước template data sẽ được tạo cho một audience cụ thể với cấu hình cá nhân hóa',
  })
  @ApiBody({
    description: 'Cấu hình cá nhân hóa để preview',
    schema: {
      type: 'object',
      properties: {
        usePersonalization: { type: 'boolean', example: true },
        commonData: {
          type: 'object',
          example: { shopName: 'RedAI Shop', orderStatus: 'Đã xác nhận' },
        },
        fieldMapping: {
          type: 'object',
          example: { customerName: 'name', customerEmail: 'email' },
        },
        customFieldMapping: {
          type: 'object',
          example: { orderCode: 'order_code', totalAmount: 'total_amount' },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Preview template data thành công',
    schema: {
      type: 'object',
      properties: {
        templateData: {
          type: 'object',
          description: 'Template data đã được cá nhân hóa',
        },
        mappedFields: {
          type: 'object',
          description: 'Các trường đã được map thành công',
        },
        unmappedFields: {
          type: 'array',
          items: { type: 'string' },
          description: 'Các trường không thể map',
        },
      },
    },
  })
  async previewPersonalizedTemplateData(
    @CurrentUser() user: JwtPayload,
    @Param('audienceId') audienceId: number,
    @Body() personalizationConfig: any,
  ): Promise<ApiResponseDto<any>> {
    const result =
      await this.znsCampaignService.previewPersonalizedTemplateData(
        user.id,
        audienceId,
        personalizationConfig,
      );
    return ApiResponseDto.success(
      result,
      'Preview template data cá nhân hóa thành công',
    );
  }

  /**
   * Lấy danh sách chiến dịch ZNS với phân trang và filter
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch ZNS',
    description: `Lấy danh sách các chiến dịch ZNS với các tùy chọn:
    - Phân trang với page và limit
    - Tìm kiếm theo tên campaign
    - Filter theo trạng thái (DRAFT, SCHEDULED, SENDING, COMPLETED, FAILED)
    - Filter theo Integration ID (UUID)
    - Sắp xếp theo ngày tạo, ngày gửi
    - Thống kê cơ bản: số tin nhắn gửi, delivered, read`,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsCampaignResponseDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch' })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZnsCampaignQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsCampaign>>> {
    const result = await this.znsCampaignService.getCampaignsByUserId(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      result,
      'Lấy danh sách chiến dịch ZNS thành công',
    );
  }

  /**
   * Lấy chi tiết chiến dịch ZNS
   */
  @Get(':campaignId')
  @ApiOperation({
    summary: 'Lấy chi tiết chiến dịch ZNS',
    description: 'Lấy thông tin chi tiết của một chiến dịch ZNS',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async getCampaignDetail(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.getCampaignDetail(
      user.id,
      campaignId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy chi tiết chiến dịch ZNS thành công',
    );
  }

  /**
   * Cập nhật chiến dịch ZNS
   */
  @Put(':campaignId')
  @ApiOperation({
    summary: 'Cập nhật chiến dịch ZNS',
    description:
      'Cập nhật thông tin chiến dịch ZNS (chỉ cho phép khi ở trạng thái DRAFT hoặc SCHEDULED)',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Không thể cập nhật chiến dịch ở trạng thái hiện tại',
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async updateCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
    @Body() updateDto: UpdateZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.updateCampaign(
      user.id,
      campaignId,
      updateDto,
    );
    return ApiResponseDto.success(result, 'Cập nhật chiến dịch ZNS thành công');
  }

  /**
   * Xóa chiến dịch ZNS
   */
  @Delete(':campaignId')
  @ApiOperation({
    summary: 'Xóa chiến dịch ZNS',
    description: 'Xóa chiến dịch ZNS (chỉ cho phép khi không đang chạy)',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean', example: true },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Không thể xóa chiến dịch đang chạy',
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async deleteCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.znsCampaignService.deleteCampaign(
      user.id,
      campaignId,
    );
    return ApiResponseDto.success(result, 'Xóa chiến dịch ZNS thành công');
  }

  /**
   * Chạy chiến dịch ZNS ngay lập tức
   */
  @Post(':campaignId/run')
  @ApiOperation({
    summary: 'Chạy chiến dịch ZNS ngay lập tức',
    description: 'Bắt đầu chạy chiến dịch ZNS và đẩy vào queue để xử lý',
  })
  @ApiResponse({
    status: 200,
    description: 'Chạy chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Chiến dịch không ở trạng thái có thể chạy',
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async runCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.runCampaign(
      user.id,
      campaignId,
    );
    return ApiResponseDto.success(result, 'Chạy chiến dịch ZNS thành công');
  }

  /**
   * Hủy chiến dịch ZNS
   */
  @Post(':campaignId/cancel')
  @ApiOperation({
    summary: 'Hủy chiến dịch ZNS',
    description: 'Hủy chiến dịch ZNS đang chạy hoặc đã lên lịch',
  })
  @ApiResponse({
    status: 200,
    description: 'Hủy chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsCampaignResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Không thể hủy chiến dịch ở trạng thái hiện tại',
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch ZNS' })
  async cancelCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('campaignId') campaignId: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.cancelCampaign(
      user.id,
      campaignId,
    );
    return ApiResponseDto.success(result, 'Hủy chiến dịch ZNS thành công');
  }

  /**
   * Tạo job gửi ZNS đơn lẻ
   */
  @Post('send-single-zns')
  @ApiOperation({
    summary: 'Tạo job gửi ZNS đơn lẻ',
    description: 'Tạo job để gửi một tin nhắn ZNS đơn lẻ và đẩy vào queue',
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo job gửi ZNS đơn lẻ thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsJobResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async sendSingleZns(
    @CurrentUser() user: JwtPayload,
    @Body() sendDto: SendSingleZnsDto,
  ): Promise<ApiResponseDto<ZnsJobResponseDto>> {
    const jobId = await this.znsCampaignService.createSingleZnsJob(
      user.id,
      sendDto.integrationId,
      sendDto.phone,
      sendDto.templateId,
      sendDto.templateData,
      undefined,
      sendDto.trackingId,
    );
    return ApiResponseDto.success(
      { jobId },
      'Tạo job gửi ZNS đơn lẻ thành công',
    );
  }

  /**
   * Tạo job gửi batch ZNS
   */
  @Post('send-batch-zns')
  @ApiOperation({
    summary: 'Tạo job gửi batch ZNS',
    description: 'Tạo job để gửi nhiều tin nhắn ZNS cùng lúc và đẩy vào queue',
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo job gửi batch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsJobResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Official Account' })
  async sendBatchZns(
    @CurrentUser() user: JwtPayload,
    @Body() sendDto: SendBatchZnsDto,
  ): Promise<ApiResponseDto<ZnsJobResponseDto>> {
    const jobId = await this.znsCampaignService.createBatchZnsJob(
      user.id,
      sendDto.integrationId,
      sendDto.messages,
      undefined,
      sendDto.batchIndex || 0,
      sendDto.totalBatches || 1,
    );
    return ApiResponseDto.success(
      { jobId },
      'Tạo job gửi batch ZNS thành công',
    );
  }

  /**
   * Xóa nhiều chiến dịch ZNS
   */
  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều chiến dịch ZNS',
    description: `Xóa nhiều chiến dịch ZNS cùng lúc với các tính năng:
    - Xóa tối đa 50 chiến dịch mỗi lần
    - Kiểm tra quyền sở hữu chiến dịch
    - Kiểm tra trạng thái có thể xóa (không thể xóa chiến dịch đã lên lịch)
    - Trả về kết quả chi tiết cho từng chiến dịch
    - Thống kê tổng quan số lượng thành công/thất bại

    **Trạng thái có thể xóa:**
    - DRAFT: Nháp
    - SENT: Đã gửi
    - FAILED: Thất bại

    **Trạng thái không thể xóa:**
    - SCHEDULED: Đã lên lịch`,
  })
  @ApiBody({
    type: BulkDeleteZnsCampaignDto,
    description: 'Danh sách ID chiến dịch ZNS cần xóa',
    examples: {
      'basic-delete': {
        summary: 'Xóa nhiều chiến dịch cơ bản',
        description: 'Xóa 3 chiến dịch với ID khác nhau',
        value: {
          ids: [1, 2, 3],
        },
      },
      'large-batch': {
        summary: 'Xóa batch lớn',
        description: 'Xóa nhiều chiến dịch trong một lần (tối đa 50)',
        value: {
          ids: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa nhiều chiến dịch ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(BulkDeleteZnsCampaignResponseDto) },
          },
        },
      ],
    },
    examples: {
      'all-success': {
        summary: 'Tất cả chiến dịch xóa thành công',
        value: {
          success: true,
          message: 'Đã xóa thành công 3 chiến dịch.',
          data: {
            totalRequested: 3,
            successCount: 3,
            failureCount: 0,
            successfulDeletes: [1, 2, 3],
            failedDeletes: [],
            results: [
              {
                id: 1,
                name: 'Chiến dịch thông báo đơn hàng',
                success: true,
              },
              {
                id: 2,
                name: 'Chiến dịch khuyến mãi',
                success: true,
              },
              {
                id: 3,
                name: 'Chiến dịch chăm sóc khách hàng',
                success: true,
              },
            ],
            message: 'Đã xóa thành công 3 chiến dịch.',
          },
        },
      },
      'partial-success': {
        summary: 'Một số chiến dịch xóa thành công',
        value: {
          success: true,
          message: 'Đã xóa 2/3 chiến dịch. 1 chiến dịch không thể xóa.',
          data: {
            totalRequested: 3,
            successCount: 2,
            failureCount: 1,
            successfulDeletes: [1, 2],
            failedDeletes: [3],
            results: [
              {
                id: 1,
                name: 'Chiến dịch thông báo đơn hàng',
                success: true,
              },
              {
                id: 2,
                name: 'Chiến dịch khuyến mãi',
                success: true,
              },
              {
                id: 3,
                name: 'Chiến dịch đã lên lịch',
                success: false,
                reason: 'Không thể xóa chiến dịch đã lên lịch',
              },
            ],
            message: 'Đã xóa 2/3 chiến dịch. 1 chiến dịch không thể xóa.',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      examples: {
        'empty-ids': {
          summary: 'Danh sách ID trống',
          value: {
            success: false,
            message: 'Danh sách ID chiến dịch không được để trống',
            errorCode: 'VALIDATION_ERROR',
          },
        },
        'too-many-ids': {
          summary: 'Quá nhiều ID',
          value: {
            success: false,
            message: 'Không thể xóa quá 50 chiến dịch cùng lúc',
            errorCode: 'VALIDATION_ERROR',
          },
        },
        'invalid-ids': {
          summary: 'ID không hợp lệ',
          value: {
            success: false,
            message: 'ID phải là số',
            errorCode: 'VALIDATION_ERROR',
          },
        },
      },
    },
  })
  async bulkDeleteCampaigns(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteZnsCampaignDto,
  ): Promise<ApiResponseDto<BulkDeleteZnsCampaignResponseDto>> {
    const result = await this.znsCampaignService.bulkDeleteCampaigns(
      user.id,
      bulkDeleteDto.ids,
    );

    return ApiResponseDto.success(result, result.message);
  }
}
