import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ShippingStatusEnum } from '@modules/business/enums';

/**
 * Enum cho loại dữ liệu biểu đồ timeline đơn hàng
 */
export enum OrdersTimelineChartDataType {
  ORDERS_COUNT = 'ORDERS_COUNT',
  TOTAL_REVENUE = 'TOTAL_REVENUE',
  AVERAGE_ORDER_VALUE = 'AVERAGE_ORDER_VALUE',
}

/**
 * DTO cho query parameters của API biểu đồ thống kê đơn hàng theo thời gian tạo
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class OrdersTimelineChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: OrdersTimelineChartDataType,
    example: OrdersTimelineChartDataType.ORDERS_COUNT,
    required: false,
    default: OrdersTimelineChartDataType.ORDERS_COUNT,
  })
  @IsOptional()
  @IsEnum(OrdersTimelineChartDataType)
  type?: OrdersTimelineChartDataType;

  /**
   * Lọc theo trạng thái vận chuyển
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.DELIVERED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  status?: ShippingStatusEnum;
}
