{"openapi": "3.0.0", "info": {"title": "Marketing Module API", "description": "API documentation for Marketing module including audience management, campaigns, Zalo integration, and email marketing", "version": "1.0.0"}, "servers": [{"url": "/v1", "description": "API v1"}], "tags": [{"name": "User - Audience", "description": "User audience management endpoints"}, {"name": "User - Campaign", "description": "User campaign management endpoints"}, {"name": "User - Email Campaign", "description": "User email campaign management endpoints"}, {"name": "User - Template Email", "description": "User email template management endpoints"}, {"name": "User - Segment", "description": "User segment management endpoints"}, {"name": "User - Tag", "description": "User tag management endpoints"}, {"name": "User - Marketing Custom Field", "description": "User marketing custom field management endpoints"}, {"name": "User - Marketing Statistics", "description": "User marketing statistics endpoints"}, {"name": "User - Marketing", "description": "User marketing overview endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User Zalo integration endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User Zalo ZNS management endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User <PERSON><PERSON> campaign management endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User Zalo tag management endpoints"}, {"name": "User - Zalo Segment", "description": "User Zalo segment management endpoints"}, {"name": "User - Zalo Campaign", "description": "User Zalo campaign management endpoints"}, {"name": "User - Zalo OA Message Campaign", "description": "User Zalo OA message campaign endpoints"}, {"name": "User - Zalo Automation", "description": "User Zalo automation endpoints"}, {"name": "User - Zalo Integration", "description": "User Zalo integration management endpoints"}, {"name": "User - SMS Campaign", "description": "User SMS campaign management endpoints"}, {"name": "User - SMS Template", "description": "User SMS template management endpoints"}, {"name": "User - SMS Servers", "description": "User SMS server configuration endpoints"}, {"name": "User - Email Tracking", "description": "User email tracking endpoints"}, {"name": "User - Marketing Custom Field", "description": "User marketing custom field management endpoints"}, {"name": "User - Marketing - <PERSON><PERSON><PERSON>", "description": "User Twilio SMS integration endpoints"}, {"name": "User - System Template Email", "description": "User system template email endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User <PERSON><PERSON> campaign management endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User Zalo ZNS management endpoints"}, {"name": "User - Zalo OA Message Campaign", "description": "User Zalo OA message campaign endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User Zalo OAuth integration endpoints"}, {"name": "User - <PERSON><PERSON>", "description": "User <PERSON><PERSON> webhook handling endpoints"}, {"name": "User - Zalo Conversation", "description": "User Zalo conversation management endpoints"}, {"name": "User - <PERSON><PERSON> Follower", "description": "User Zalo follower management endpoints"}, {"name": "User - Zalo Statistics", "description": "User Zalo statistics and analytics endpoints"}, {"name": "User - Zalo Upload", "description": "User Zalo file upload endpoints"}], "paths": {"/marketing/audiences": {"get": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON><PERSON> danh sách audience với phân trang và filter", "description": "<PERSON><PERSON><PERSON> danh sách audience với các tùy chọn filter và phân trang: tìm kiếm theo tên/email/phone, filter theo tags/platform/integration, segment filtering, custom fields, ngày tạo, tương tác cuối và sắp xếp theo nhiều tiêu chí", "operationId": "getUserAudiences", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> hi<PERSON>n tại", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "in": "query", "description": "Số lượng audience trên mỗi trang", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> ki<PERSON>m theo tên, email hoặc phone", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "tags", "in": "query", "description": "Filter theo tags (danh s<PERSON>ch ID cách nhau bởi dấu phẩy)", "required": false, "schema": {"type": "string", "example": "1,2,3"}}, {"name": "platform", "in": "query", "description": "Filter theo platform", "required": false, "schema": {"type": "string", "enum": ["ZALO", "EMAIL", "SMS", "FACEBOOK", "MANUAL"]}}, {"name": "integration", "in": "query", "description": "Filter theo integration ID", "required": false, "schema": {"type": "string"}}, {"name": "segmentId", "in": "query", "description": "Filter theo segment ID cụ thể", "required": false, "schema": {"type": "integer"}}, {"name": "segmentIds", "in": "query", "description": "Filter theo nhiều segment IDs (cách nhau bởi dấu phẩy)", "required": false, "schema": {"type": "string", "example": "123,456,789"}}, {"name": "excludeSegmentId", "in": "query", "description": "Loại trừ audience thuộ<PERSON> segment cụ thể", "required": false, "schema": {"type": "integer"}}, {"name": "excludeSegmentIds", "in": "query", "description": "Loại trừ audience thu<PERSON>c ít nhất một trong các segment", "required": false, "schema": {"type": "string", "example": "123,456"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "required": false, "schema": {"type": "string", "enum": ["createdAt", "name", "email", "lastInteraction", "updatedAt"], "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "description": "<PERSON><PERSON><PERSON> tự sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch audience thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> s<PERSON> audience"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceResponse"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["User - Audience"], "summary": "Tạo audience mới", "description": "Tạo audience mới với thông tin cơ bản, custom fields, tags và hỗ trợ nhiều nguồn dữ liệu (Manual, Zalo, Email, SMS)", "operationId": "createUserAudience", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAudienceDto"}}}}, "responses": {"201": {"description": "Audience đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Audience đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ hoặc email/phone đã tồn tại", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> đã tồn tại trong hệ thống"}, "errorCode": {"type": "string", "example": "AUDIENCE_EMAIL_ALREADY_EXISTS"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["User - Audience"], "summary": "Xóa nhiều audience cùng lúc (bulk delete)", "description": "Xóa nhiều audience trong một request với xử lý parallel, tối đa 100 audience mỗi lần, hỗ trợ partial success và transaction safety", "operationId": "bulkDeleteUserAudiences", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteAudienceDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> cả audience đã đư<PERSON><PERSON> xóa thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã xóa thành công 5 audience"}, "data": {"$ref": "#/components/schemas/BulkDeleteResponseDto"}}}}}}, "207": {"description": "Một số audience không thể xóa (partial success)", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> xóa thành công 3/5 audience"}, "data": {"$ref": "#/components/schemas/BulkDeleteResponseDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/audiences/all": {"get": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON><PERSON> tất cả audience với filter (không phân trang)", "description": "L<PERSON>y tất cả audience với filter mà không phân trang, hỗ trợ segment filtering, giới hạn kết quả (tối đa 10000), tù<PERSON> chọn chỉ trả về thông tin cơ bản để tăng tốc độ", "operationId": "getAllUserAudiences", "parameters": [{"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> ki<PERSON>m theo tên, email hoặc phone", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "segmentId", "in": "query", "description": "Filter theo segment ID cụ thể", "required": false, "schema": {"type": "integer", "example": 123}}, {"name": "segmentIds", "in": "query", "description": "Filter theo nhiều segment IDs (cách nhau bởi dấu phẩy)", "required": false, "schema": {"type": "string", "example": "123,456,789"}}, {"name": "basicInfo", "in": "query", "description": "Chỉ trả về thông tin cơ bản để tăng tốc độ", "required": false, "schema": {"type": "boolean", "example": true}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> hạn số lượng kết quả (mặc định 1000, tối đa 10000)", "required": false, "schema": {"type": "integer", "example": 1000, "minimum": 1, "maximum": 10000}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch tất cả audience với filter", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> s<PERSON>ch tất cả audience"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/audiences/{id}": {"get": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết audience theo ID", "description": "<PERSON><PERSON>y thông tin đầy đủ của một audience cụ thể bao gồm thông tin cơ bản, custom fields, tags, lịch sử tương tác và thống kê engagement", "operationId": "getUserAudienceById", "parameters": [{"name": "id", "in": "path", "description": "ID của audience c<PERSON>n l<PERSON>y thông tin", "required": true, "schema": {"type": "integer", "example": 1234}}], "responses": {"200": {"description": "Th<PERSON>ng tin chi tiết audience", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Thông tin audience"}, "data": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}, "404": {"description": "<PERSON> không tồn tại", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Audience với ID 1234 không tồn tại"}, "errorCode": {"type": "string", "example": "AUDIENCE_NOT_FOUND"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON><PERSON> nhật thông tin audience", "description": "Cập nhật thông tin audience với các tùy chọn: cập nhật thông tin cơ bản, custom fields, tags, avatar và hỗ trợ partial update", "operationId": "updateUserAudience", "parameters": [{"name": "id", "in": "path", "description": "ID của audience c<PERSON><PERSON> cậ<PERSON> n<PERSON>t", "required": true, "schema": {"type": "integer", "example": 1234}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAudienceDto"}}}}, "responses": {"200": {"description": "<PERSON> đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON> đã đ<PERSON><PERSON><PERSON> cập nhật thành công"}, "data": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> đã tồn tại trong hệ thống"}, "errorCode": {"type": "string", "example": "AUDIENCE_EMAIL_ALREADY_EXISTS"}}}}}}, "404": {"description": "<PERSON> không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["User - Audience"], "summary": "Xóa audience khỏi hệ thống", "description": "Xóa audience và tất cả dữ liệu liên quan: custom fields, tags, segments, lịch sử tương tác và avatar. <PERSON><PERSON> tác này không thể hoàn tác", "operationId": "deleteUserAudience", "parameters": [{"name": "id", "in": "path", "description": "ID của audience cần x<PERSON>a", "required": true, "schema": {"type": "integer", "example": 1234}}], "responses": {"200": {"description": "Audience đã đư<PERSON><PERSON> xóa thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Audience đã đư<PERSON><PERSON> xóa thành công"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "deletedId": {"type": "integer", "example": 1234}, "deletedAt": {"type": "string", "example": "2024-06-24T16:50:00Z"}}}}}}}}, "404": {"description": "<PERSON> không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/audiences/{id}/avatar/upload-url": {"post": {"tags": ["User - Audience"], "summary": "Tạo presigned URL để upload avatar cho audience", "description": "Tạo presigned URL để frontend upload avatar trự<PERSON> tiếp lên S3: hỗ trợ JPG/PNG/GIF/WebP, tối đa 5MB, tự động resize 300x300px, URL có thời hạn 15 phút", "operationId": "createAudienceAvatarUploadUrl", "parameters": [{"name": "id", "in": "path", "description": "ID của audience cần upload avatar", "required": true, "schema": {"type": "integer", "example": 1234}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAvatarUploadUrlDto"}}}}, "responses": {"201": {"description": "Presigned URL đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "URL upload avatar đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/AvatarUploadUrlResponseDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "<PERSON> không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/audiences/{id}/avatar": {"put": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON><PERSON> nhật avatar sau khi upload thành công", "description": "<PERSON><PERSON><PERSON> nhật avatar sau khi upload thành công", "operationId": "updateAudienceAvatar", "parameters": [{"name": "id", "in": "path", "description": "ID của audience", "required": true, "schema": {"type": "integer", "example": 1234}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAvatarDto"}}}}, "responses": {"200": {"description": "Avatar đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Avatar đã đ<PERSON><PERSON><PERSON> cập nhật thành công"}, "data": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}, "404": {"description": "<PERSON> không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["User - Audience"], "summary": "Xóa avatar của audience", "description": "Xóa avatar của audience", "operationId": "removeAudienceAvatar", "parameters": [{"name": "id", "in": "path", "description": "ID của audience", "required": true, "schema": {"type": "integer", "example": 1234}}], "responses": {"200": {"description": "Avatar đã đư<PERSON><PERSON> xóa thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Avatar đã đư<PERSON><PERSON> xóa thành công"}, "data": {"$ref": "#/components/schemas/AudienceResponse"}}}}}}, "404": {"description": "<PERSON> không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/audiences/merge": {"post": {"tags": ["User - Audience"], "summary": "<PERSON><PERSON> n<PERSON>u audience thành một audience mới", "description": "API này nhận vào danh sách ID audience của user, tạo audience mới với thông tin được merge, và xóa các audience cũ", "operationId": "mergeUserAudiences", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MergeUserAudienceDto"}}}}, "responses": {"201": {"description": "Merge audience thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Merge audience thành công"}, "data": {"$ref": "#/components/schemas/MergeUserAudienceResponseDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu không hợp lệ hoặc email đã tồn tại"}, "404": {"description": "Một hoặc nhiều audience không tồn tại"}, "500": {"description": "Lỗi server khi merge audience"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/campaigns": {"get": {"tags": ["User - Campaign"], "description": "<PERSON><PERSON><PERSON> danh sách các marketing campaigns với các tùy chọn: phân trang, tì<PERSON> kiếm theo tên campaign, filter theo loại campaign (EMAIL, SMS, ZALO, MULTI_CHANNEL), filter theo trạng thái (DRAFT, SCHEDULED, RUNNING, COMPLETED, PAUSED), filter theo ngày tạo và ngày thực hiện, sắp xếp theo ngày tạo/tên/hiệ<PERSON> suấ<PERSON>, thống kê cơ bản: reach, engagement, conversion", "operationId": "getUserCampaigns", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> hi<PERSON>n tại", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "in": "query", "description": "Số lượng campaign trên mỗi trang", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> ki<PERSON>m theo tên campaign", "required": false, "schema": {"type": "string", "example": "Summer Sale 2024"}}, {"name": "type", "in": "query", "description": "Filter theo lo<PERSON> campaign", "required": false, "schema": {"type": "string", "enum": ["EMAIL", "SMS", "ZALO", "MULTI_CHANNEL"]}}, {"name": "status", "in": "query", "description": "Filter theo trạng thái campaign", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "RUNNING", "COMPLETED", "PAUSED"]}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "required": false, "schema": {"type": "string", "enum": ["createdAt", "name", "scheduledAt", "performance"], "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "description": "<PERSON><PERSON><PERSON> tự sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "Danh sách campaign với phân trang và thống kê", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> campaign"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignResponse"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}, "summary": {"type": "object", "properties": {"totalCampaigns": {"type": "integer", "example": 25}, "activeCampaigns": {"type": "integer", "example": 3}, "totalBudgetAllocated": {"type": "number", "example": 125000000}, "totalBudgetSpent": {"type": "number", "example": 98500000}}}}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["User - Campaign"], "summary": "Tạo marketing campaign mới", "description": "Tạo một marketing campaign mới với các tùy chọn: thiết lập thông tin cơ bản (tên, mô tả, loại campaign), cấu hình target audience hoặc segments, lên lịch thực hiện campaign, thiết lập các metrics để theo dõi hiệu suất", "operationId": "createUserCampaign", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCampaignDto"}}}}, "responses": {"201": {"description": "Campaign đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/CampaignResponse"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errors": {"type": "array", "items": {"type": "string"}, "example": ["Tên campaign không đư<PERSON><PERSON> để trống", "Loại campaign kh<PERSON>ng hợp lệ"]}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/campaigns/{id}": {"get": {"tags": ["User - Campaign"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết campaign theo ID", "description": "<PERSON><PERSON>y thông tin đầy đủ của một campaign cụ thể bao gồm: thông tin cơ bản và cấu hình campaign, target audience và segmentation details, performance metrics chi tiết, timeline và execution history, budget breakdown và cost analysis, A/B testing results (nếu có), related assets (templates, creatives)", "operationId": "getUserCampaignById", "parameters": [{"name": "id", "in": "path", "description": "ID của campaign c<PERSON>n l<PERSON>y thông tin", "required": true, "schema": {"type": "integer", "example": 456}}], "responses": {"200": {"description": "Thông tin chi tiết campaign", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Thông tin campaign"}, "data": {"$ref": "#/components/schemas/CampaignResponse"}}}}}}, "404": {"description": "Campaign không tồn tại", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Campaign với ID 456 không tồn tại"}, "errorCode": {"type": "string", "example": "CAMPAIGN_NOT_FOUND"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["User - Campaign"], "summary": "<PERSON><PERSON><PERSON> campaign", "description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> thông tin campaign theo ID", "operationId": "updateUserCampaign", "parameters": [{"name": "id", "in": "path", "description": "ID của campaign c<PERSON><PERSON> c<PERSON><PERSON> n<PERSON>t", "required": true, "schema": {"type": "integer", "example": 456}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCampaignDto"}}}}, "responses": {"200": {"description": "Campaign đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign đã đ<PERSON><PERSON><PERSON> cập nhật thành công"}, "data": {"$ref": "#/components/schemas/CampaignResponse"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "Campaign không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["User - Campaign"], "summary": "Xóa campaign", "description": "Xóa campaign theo ID", "operationId": "deleteUserCampaign", "parameters": [{"name": "id", "in": "path", "description": "ID của campaign cần xóa", "required": true, "schema": {"type": "integer", "example": 456}}], "responses": {"200": {"description": "Campaign đã đ<PERSON><PERSON><PERSON> xóa thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign đã đ<PERSON><PERSON><PERSON> xóa thành công"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}}}, "404": {"description": "Campaign không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/campaigns/{id}/history": {"get": {"tags": ["User - Campaign"], "summary": "<PERSON><PERSON><PERSON> l<PERSON>ch sử của campaign", "description": "<PERSON><PERSON><PERSON> l<PERSON>ch sử của campaign", "operationId": "getUserCampaignHistory", "parameters": [{"name": "id", "in": "path", "description": "ID của campaign", "required": true, "schema": {"type": "integer", "example": 456}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> s<PERSON> campaign", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> s<PERSON> campaign"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignHistoryResponseDto"}}}}}}}, "404": {"description": "Campaign không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/campaigns/{id}/run": {"post": {"tags": ["User - Campaign"], "summary": "Chạy campaign", "description": "Chạy campaign", "operationId": "runUserCampaign", "parameters": [{"name": "id", "in": "path", "description": "ID của campaign", "required": true, "schema": {"type": "integer", "example": 456}}], "responses": {"200": {"description": "Campaign đã đ<PERSON><PERSON><PERSON> chạy thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign đã đ<PERSON><PERSON><PERSON> chạy thành công"}, "data": {"$ref": "#/components/schemas/CampaignResponse"}}}}}}, "404": {"description": "Campaign không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/campaigns/from-template": {"post": {"tags": ["User - Campaign"], "summary": "Tạo campaign từ template", "description": "Tạo campaign mới từ template email với cấu hình email server và danh sách segments/audiences", "operationId": "createUserCampaignFromTemplate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateCampaignDto"}}}}, "responses": {"201": {"description": "Campaign từ template đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign từ template đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/CreateTemplateCampaignResponseDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "Template hoặc email server không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"bearerAuth": []}]}}, "/marketing/zalo/{oaId}/campaigns": {"get": {"tags": ["User - Zalo Campaign"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch chiến d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch chiến d<PERSON><PERSON> theo OA ID", "operationId": "getZaloCampaigns", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Zalo OA", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách chiến dịch <PERSON> thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedZaloCampaignResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - Zalo Campaign"], "summary": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON><PERSON>i", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n dị<PERSON> mới cho OA", "operationId": "createZaloCampaign", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Zalo OA", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateZaloCampaignDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> dịch <PERSON> đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloCampaignResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/{oaId}/campaigns/{id}": {"get": {"tags": ["User - Zalo Campaign"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết chiến d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một chiến dị<PERSON> theo <PERSON>", "operationId": "getZaloCampaignById", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Zalo OA", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "<PERSON> c<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Th<PERSON>ng tin chi tiết chiến d<PERSON><PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloCampaignResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "<PERSON><PERSON> campaign not found"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "put": {"tags": ["User - Zalo Campaign"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin chiến dịch <PERSON> theo ID", "operationId": "updateZaloCampaign", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Zalo OA", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "<PERSON> c<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateZaloCampaignDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dịch <PERSON> đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloCampaignResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "404": {"description": "<PERSON><PERSON> campaign not found"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - Zalo Campaign"], "summary": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "operationId": "deleteZaloCampaign", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Zalo OA", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "<PERSON> c<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dịch <PERSON> đã đư<PERSON><PERSON> xóa thành công"}, "401": {"description": "Unauthorized"}, "404": {"description": "<PERSON><PERSON> campaign not found"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/tags": {"get": {"tags": ["User - Tag"], "operationId": "getUserTags", "summary": "<PERSON><PERSON><PERSON> danh sách tag với phân trang và filter", "description": "<PERSON><PERSON><PERSON> danh sách tag với hỗ trợ phân trang, tìm kiếm theo tên và sắp xếp", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên tag", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON>h sách tag với phân trang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedTagResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["User - Tag"], "operationId": "createUserTag", "summary": "Tạo tag (nhãn phân loại) mới", "description": "Tạo tag mới để phân loại và gán nhãn cho audience với tên duy nhất và màu sắc phân biệt", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTagDto"}}}}, "responses": {"201": {"description": "Tag đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "409": {"description": "Conflict - Tag name already exists"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["User - Tag"], "operationId": "bulkDeleteUserTags", "summary": "Xóa nhiều tag cùng lúc", "description": "Xóa nhiều tag cùng lúc với danh sách ID", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> sách ID của các tag cần xóa"}}, "required": ["ids"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhiều tag thành công"}, "400": {"description": "Bad request - Invalid IDs"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/tags/all": {"get": {"tags": ["User - Tag"], "operationId": "getAllUserTags", "summary": "<PERSON><PERSON><PERSON> tất cả tag (không phân trang)", "description": "API này sẽ bị loại bỏ trong tương lai. Sử dụng GET /marketing/tags thay thế.", "deprecated": true, "security": [{"JWT-auth": []}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch tất cả tag", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/tags/{id}": {"get": {"tags": ["User - Tag"], "operationId": "getUserTagById", "summary": "<PERSON><PERSON><PERSON> thông tin tag theo ID", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một tag cụ thể bao gồm tên, mô tả và metadata", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của tag", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Thông tin chi tiết tag", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Tag not found"}, "500": {"description": "Internal server error"}}}, "put": {"tags": ["User - Tag"], "operationId": "updateUserTag", "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t tag", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin tag theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của tag", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTagDto"}}}}, "responses": {"200": {"description": "<PERSON> đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "404": {"description": "Tag not found"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["User - Tag"], "operationId": "deleteUserTag", "summary": "Xóa tag", "description": "Xóa tag theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của tag", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Tag đã đư<PERSON><PERSON> xóa thành công"}, "401": {"description": "Unauthorized"}, "404": {"description": "Tag not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments": {"get": {"tags": ["User - Segment"], "operationId": "getUserSegments", "summary": "<PERSON><PERSON><PERSON> danh sách segment với phân trang và filter", "description": "<PERSON><PERSON><PERSON> danh sách segment với hỗ trợ phân trang, tìm kiếm và filter", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Danh sách segment với phân trang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSegmentResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["User - Segment"], "operationId": "createUserSegment", "summary": "Tạo segment mới", "description": "Tạo segment mới để phân đoạn khách hàng với các điều kiện filter", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSegmentDto"}}}}, "responses": {"201": {"description": "Segment đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["User - Segment"], "operationId": "bulkDeleteUserSegments", "summary": "Xóa nhiều segment cùng lúc", "description": "Xóa nhiều segment cùng lúc với danh sách ID", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> sách ID của các segment cần xóa"}}, "required": ["ids"]}}}}, "responses": {"200": {"description": "Xóa nhiều segment thành công"}, "400": {"description": "Bad request - Invalid IDs"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments/preview": {"post": {"tags": ["User - Segment"], "operationId": "previewUserSegment", "summary": "Preview segment tr<PERSON><PERSON><PERSON> khi tạo", "description": "<PERSON>em trước số lượng audience sẽ có trong segment với các điều kiện filter", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentPreviewDto"}}}}, "responses": {"200": {"description": "Preview segment thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentPreviewResponse"}}}}, "400": {"description": "Bad request - Invalid filter conditions"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments/available-fields": {"get": {"tags": ["User - Segment"], "operationId": "getAvailableSegmentFields", "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch field có thể dùng để tạo segment", "description": "<PERSON><PERSON><PERSON> danh sách các field có thể sử dụng để tạo điều kiện filter cho segment", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch field có thể sử dụng", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableFieldsResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments/{id}": {"get": {"tags": ["User - Segment"], "operationId": "getUserSegmentById", "summary": "<PERSON><PERSON><PERSON> thông tin segment theo ID", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một segment theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của segment", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Thông tin chi tiết segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Segment not found"}, "500": {"description": "Internal server error"}}}, "put": {"tags": ["User - Segment"], "operationId": "updateUserSegment", "summary": "Cập nhật segment", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin segment theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của segment", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSegmentDto"}}}}, "responses": {"200": {"description": "Segment đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "404": {"description": "Segment not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments/{id}/stats": {"get": {"tags": ["User - Segment"], "operationId": "getUserSegmentStats", "summary": "<PERSON><PERSON><PERSON> thống kê của segment", "description": "<PERSON><PERSON><PERSON> thống kê chi tiết của segment bao gồm số lượng audience, tỷ lệ engagement", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của segment", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Thống kê segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentStatsResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Segment not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/segments/{id}/audiences": {"get": {"tags": ["User - Segment"], "operationId": "getUserSegmentAudiences", "summary": "<PERSON><PERSON><PERSON> danh sách audience trong segment", "description": "<PERSON><PERSON><PERSON> danh sách audience thuộc segment với phân trang", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của segment", "required": true, "schema": {"type": "integer"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch audience trong segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSegmentAudienceResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Segment not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/template-emails": {"get": {"tags": ["User - Template Email"], "operationId": "getUserTemplateEmails", "summary": "<PERSON><PERSON><PERSON> danh sách template email", "description": "<PERSON><PERSON><PERSON> danh sách template email với phân trang và filter", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch template email với phân trang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedTemplateEmailResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["User - Template Email"], "operationId": "createUserTemplateEmail", "summary": "Tạo template email mới", "description": "Tạo template email mới với HTML content và metadata", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateEmailDto"}}}}, "responses": {"201": {"description": "Template email đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateEmailResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/template-emails/overview": {"get": {"tags": ["User - Template Email"], "operationId": "getUserTemplateEmailOverview", "summary": "<PERSON><PERSON><PERSON> tổng quan template email", "description": "<PERSON><PERSON><PERSON> thống kê tổng quan về template email của user", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "Tổng quan template email", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateEmailOverviewResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/template-emails/bulk": {"delete": {"tags": ["User - Template Email"], "operationId": "bulkDeleteUserTemplateEmails", "summary": "Xóa nhiều template email", "description": "Xóa nhiều template email cùng lúc", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> sách ID của các template cần xóa"}}, "required": ["ids"]}}}}, "responses": {"200": {"description": "Xóa nhiều template email thành công"}, "400": {"description": "Bad request - Invalid IDs"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/template-emails/{id}": {"get": {"tags": ["User - Template Email"], "operationId": "getUserTemplateEmailById", "summary": "<PERSON><PERSON><PERSON> thông tin template email theo ID", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một template email", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của template email", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Th<PERSON>ng tin chi tiết template email", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateEmailResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Template email not found"}, "500": {"description": "Internal server error"}}}, "put": {"tags": ["User - Template Email"], "operationId": "updateUserTemplateEmail", "summary": "<PERSON><PERSON><PERSON> nhật template email", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin template email theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của template email", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateEmailDto"}}}}, "responses": {"200": {"description": "Template email đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateEmailResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "404": {"description": "Template email not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/email-campaigns": {"get": {"tags": ["User - Email Campaign"], "operationId": "getUserEmailCampaigns", "summary": "<PERSON><PERSON><PERSON> s<PERSON> email campaign", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch email campaign với phân trang và filter", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> email campaign v<PERSON>i phân trang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedEmailCampaignResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["User - Email Campaign"], "operationId": "createUserEmailCampaign", "summary": "Tạo email campaign mới", "description": "Tạo email campaign mới với nội dung và danh sách người nhận", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmailCampaignDto"}}}}, "responses": {"201": {"description": "Email campaign đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailCampaignResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["User - Email Campaign"], "operationId": "bulkDeleteUserEmailCampaigns", "summary": "<PERSON><PERSON><PERSON> n<PERSON>ều email campaign", "description": "Xóa nhiều email campaign cùng lúc", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID của các campaign cần xóa"}}, "required": ["ids"]}}}}, "responses": {"200": {"description": "Xóa nhiều email campaign thành công"}, "400": {"description": "Bad request - Invalid IDs"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/email-campaigns/with-template": {"post": {"tags": ["User - Email Campaign"], "operationId": "createUserEmailCampaignWithTemplate", "summary": "Tạo email campaign với template", "description": "Tạo email campaign sử dụng template có sẵn", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmailCampaignWithTemplateDto"}}}}, "responses": {"201": {"description": "Email campaign với template đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailCampaignResponse"}}}}, "400": {"description": "Bad request - Validation error"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/email-campaigns/overview": {"get": {"tags": ["User - Email Campaign"], "operationId": "getUserEmailCampaignOverview", "summary": "<PERSON><PERSON><PERSON> tổng quan email campaign", "description": "<PERSON><PERSON><PERSON> thống kê tổng quan về email campaign của user", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "Tổng quan email campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailCampaignOverviewResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/email-campaigns/recent": {"get": {"tags": ["User - Email Campaign"], "operationId": "getRecentUserEmailCampaigns", "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch email campaign gần đ<PERSON>y", "description": "<PERSON><PERSON><PERSON> danh sách email campaign đ<PERSON><PERSON><PERSON> tạo gần đây", "security": [{"JWT-auth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Số lượng campaign cầ<PERSON> l<PERSON>y", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON> email campaign g<PERSON><PERSON> đ<PERSON><PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecentEmailCampaignsResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/statistics/overview": {"get": {"tags": ["User - Marketing Statistics"], "operationId": "getUserMarketingStatisticsOverview", "summary": "<PERSON><PERSON><PERSON> tổng quan thống kê <PERSON>", "description": "<PERSON><PERSON><PERSON> thống kê tổng quan về hoạt động marketing của user", "security": [{"JWT-auth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "T<PERSON><PERSON> quan thống kê marketing", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketingStatisticsOverviewResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/statistics/audience-growth": {"get": {"tags": ["User - Marketing Statistics"], "operationId": "getUserAudienceGrowthStatistics", "summary": "<PERSON><PERSON><PERSON> thống kê tăng trưởng audience", "description": "<PERSON><PERSON><PERSON> thống kê về sự tăng trưởng số lượng audience theo thờ<PERSON> gian", "security": [{"JWT-auth": []}], "parameters": [{"name": "period", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> thời gian thống kê", "required": false, "schema": {"type": "string", "enum": ["7d", "30d", "90d", "1y"], "default": "30d"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> kê tăng trưởng audience", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudienceGrowthStatisticsResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/statistics/campaign-performance": {"get": {"tags": ["User - Marketing Statistics"], "operationId": "getUserCampaignPerformanceStatistics", "summary": "<PERSON><PERSON><PERSON> thống kê hiệu su<PERSON> campaign", "description": "<PERSON><PERSON><PERSON> thống kê về hiệu suất các campaign marketing", "security": [{"JWT-auth": []}], "parameters": [{"name": "campaignType", "in": "query", "description": "Loại campaign", "required": false, "schema": {"type": "string", "enum": ["email", "sms", "zalo"]}}, {"name": "period", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> thời gian thống kê", "required": false, "schema": {"type": "string", "enum": ["7d", "30d", "90d", "1y"], "default": "30d"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> kê hiệu su<PERSON> campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignPerformanceStatisticsResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/statistics/segment-distribution": {"get": {"tags": ["User - Marketing Statistics"], "operationId": "getUserSegmentDistributionStatistics", "summary": "<PERSON><PERSON><PERSON> thống kê phân phối segment", "description": "<PERSON><PERSON><PERSON> thống kê về phân phối audience theo c<PERSON> segment", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "Th<PERSON><PERSON> kê phân phối segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentDistributionStatisticsResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo": {"get": {"tags": ["User - <PERSON><PERSON>"], "operationId": "getUserZaloOfficialAccounts", "summary": "<PERSON><PERSON><PERSON>nh s<PERSON>ch Official Account của người dùng", "description": "<PERSON><PERSON><PERSON> danh sách tất cả Official Account <PERSON>alo đã kết nối", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "<PERSON><PERSON> Official Account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloOfficialAccountListResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo/paginated": {"get": {"tags": ["User - <PERSON><PERSON>"], "operationId": "getUserZaloOfficialAccountsPaginated", "summary": "<PERSON><PERSON><PERSON> danh sách Official Account có phân trang", "description": "<PERSON><PERSON><PERSON> danh sách Official Account Zalo với phân trang", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch Official Account v<PERSON>i ph<PERSON> trang", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedZaloOfficialAccountResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo/connect": {"post": {"tags": ["User - <PERSON><PERSON>"], "operationId": "connectZaloOfficialAccount", "summary": "<PERSON><PERSON><PERSON> Official Account v<PERSON><PERSON> hệ thống", "description": "<PERSON><PERSON><PERSON> nối Official Account <PERSON><PERSON> với hệ thống marketing", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"oaId": {"type": "string", "description": "ID của Official Account"}, "accessToken": {"type": "string", "description": "Access token để kết nối"}}, "required": ["oaId", "accessToken"]}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> Official Account thành công"}, "400": {"description": "Bad request - Invalid credentials"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo/{oaId}": {"get": {"tags": ["User - <PERSON><PERSON>"], "operationId": "getUserZaloOfficialAccountById", "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết Official Account", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một Official Account theo ID", "security": [{"JWT-auth": []}], "parameters": [{"name": "oaId", "in": "path", "description": "ID của Official Account", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "T<PERSON><PERSON><PERSON> tin chi tiết Official Account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloOfficialAccountResponse"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Official Account not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo/{oaId}/disconnect": {"delete": {"tags": ["User - <PERSON><PERSON>"], "operationId": "disconnectZaloOfficialAccount", "summary": "<PERSON><PERSON><PERSON> k<PERSON> Official Account", "description": "<PERSON><PERSON><PERSON> kết n<PERSON>i Official Account khỏi hệ thống", "security": [{"JWT-auth": []}], "parameters": [{"name": "oaId", "in": "path", "description": "ID của Official Account", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON> Official Account thành công"}, "401": {"description": "Unauthorized"}, "404": {"description": "Official Account not found"}, "500": {"description": "Internal server error"}}}}, "/marketing/zalo/{oaId}/followers": {"get": {"tags": ["User - <PERSON><PERSON> Follower"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ng<PERSON><PERSON>i theo dõi của Official Account", "description": "<PERSON><PERSON><PERSON> danh sách người theo dõi của Official Account từ database với phân trang và filter theo tên, trạng thái, tags, ngày theo dõi.", "operationId": "getZaloFollowers", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Official Account", "required": true, "schema": {"type": "string", "example": "1234567890123456789"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lư<PERSON>ng follower trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên hoặc user ID", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "status", "in": "query", "description": "Filter theo trạng thái follower", "required": false, "schema": {"type": "string", "enum": ["ACTIVE", "BLOCKED", "UNFOLLOWED"], "example": "ACTIVE"}}, {"name": "tags", "in": "query", "description": "Filter theo tags (danh s<PERSON>ch ID cách nhau bởi dấu phẩy)", "required": false, "schema": {"type": "string", "example": "1,2,3"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách người theo dõi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách người theo dõi từ database thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "userId": {"type": "string", "example": "****************"}, "displayName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/user_avatar.jpg"}, "followedAt": {"type": "integer", "description": "Timestamp khi follow", "example": *************}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCKED", "UNFOLLOWED"], "example": "ACTIVE"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "VIP Customer"}, "color": {"type": "string", "example": "#FF5722"}}}}, "lastInteraction": {"type": "integer", "description": "Timestamp tư<PERSON><PERSON> tác cu<PERSON>i", "example": *************}}}}, "meta": {"type": "object", "properties": {"totalItems": {"type": "integer", "example": 150}, "itemCount": {"type": "integer", "example": 20}, "itemsPerPage": {"type": "integer", "example": 20}, "totalPages": {"type": "integer", "example": 8}, "currentPage": {"type": "integer", "example": 1}}}}}}}}}}, "400": {"description": "<PERSON><PERSON> số không hợp lệ"}, "404": {"description": "Official Account kh<PERSON>ng tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"JWT-auth": []}]}}, "/marketing/sms-campaigns": {"get": {"tags": ["User - SMS Campaign"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch chiến d<PERSON>ch <PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách các chiến dịch SMS với các tùy chọn: phân trang với page và limit, tìm kiếm theo tên campaign, filter theo trạng thái (DRAFT, SCHEDULED, SENDING, SENT, FAILED), sắp xếp theo ng<PERSON> (mới nhất trước), thống kê cơ bản: s<PERSON> <PERSON> gửi, thành công, thất bại cho mỗi campaign", "operationId": "getSmsCampaigns", "security": [{"JWT-auth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> hi<PERSON>n tại", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "in": "query", "description": "Số lượng campaign trên mỗi trang", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> ki<PERSON>m theo tên campaign", "required": false, "schema": {"type": "string", "example": "Black Friday SMS"}}, {"name": "status", "in": "query", "description": "Filter theo trạng thái campaign", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "SENDING", "SENT", "FAILED"]}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch chiến dịch SMS với phân trang", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> s<PERSON>ch chi<PERSON>n d<PERSON>ch <PERSON>"}, "data": {"$ref": "#/components/schemas/PaginatedSmsCampaignResponseDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi server"}}}, "post": {"tags": ["User - SMS Campaign"], "summary": "Tạo SMS campaign với template", "description": "Tạo chiến dịch SMS marketing sử dụng template với các tùy chọn: sử dụng template SMS có sẵn với nội dung được định sẵn, chọn loại chiến dịch: OTP (tin nhắn xác thực) hoặc ADS (tin nhắn quảng cáo), tự động thay thế các biến trong template, hỗ trợ personalization với custom fields, lên lịch gửi hoặc gửi ngay lập tức, chọn segment, audience cụ thể hoặc danh sách số điện thoại, tự động tạo jobs và đẩy vào queue để worker xử lý", "operationId": "createSmsCampaign", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSmsCampaignDto"}}}}, "responses": {"201": {"description": "SMS campaign với template đã được tạo thành công và jobs đã được đẩy vào queue", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "SMS campaign với template đã được tạo thành công với 150 recipients. Campaign sẽ được xử lý bởi worker."}, "data": {"$ref": "#/components/schemas/CreateSmsCampaignResponseDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "Template SMS, segment hoặc audience không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}}, "delete": {"tags": ["User - SMS Campaign"], "summary": "Xóa nhiều SMS campaigns cùng lúc", "description": "<PERSON><PERSON><PERSON> nhiều SMS campaigns trong một request với xử lý parallel, tối đa 50 campaigns mỗi lần", "operationId": "bulkDeleteSmsCampaigns", "security": [{"JWT-auth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteSmsCampaignDto"}}}}, "responses": {"200": {"description": "T<PERSON>t cả SMS campaigns đã đư<PERSON>c xóa thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã xóa thành công 5 SMS campaigns"}, "data": {"$ref": "#/components/schemas/BulkDeleteResponseDto"}}}}}}, "207": {"description": "Một số SMS campaigns không thể xóa (partial success)"}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}}}, "/marketing/sms-campaigns/overview": {"get": {"tags": ["User - SMS Campaign"], "summary": "<PERSON><PERSON><PERSON> thống kê tổng quan SMS campaign", "description": "Tr<PERSON> về dashboard tổng quan với thống kê toàn bộ SMS marketing: tổng số campaign theo từng trạng thái, tổng số SMS đã gửi thành công và thất bại, tỷ lệ thành công tổng thể, số campaign đang hoạt động, tổng chi phí và chi phí trung bình mỗi tin nhắn, thống kê hiệu suất chung", "operationId": "getSmsCampaignOverview", "security": [{"JWT-auth": []}], "parameters": [{"name": "campaignIds", "in": "query", "description": "<PERSON>h sách ID campaigns cần thống kê (cách nhau bởi dấu phẩy)", "required": false, "schema": {"type": "string", "example": "1,2,3"}}, {"name": "startTime", "in": "query", "description": "Thời gian b<PERSON>t đầu filter (Unix timestamp)", "required": false, "schema": {"type": "integer", "example": 1703980800}}, {"name": "endTime", "in": "query", "description": "Thời gian kết thúc filter (Unix timestamp)", "required": false, "schema": {"type": "integer", "example": 1704067200}}, {"name": "filterByCreatedAt", "in": "query", "description": "Filter theo thời gian t<PERSON>o", "required": false, "schema": {"type": "boolean", "default": true}}, {"name": "filterByCompletedAt", "in": "query", "description": "Filter theo thời gian hoàn thành", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Thống kê tổng quan SMS marketing", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON>ng kê tổng quan SMS campaign"}, "data": {"$ref": "#/components/schemas/SmsCampaignOverviewDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi server"}}}}, "/marketing/sms-templates": {"get": {"tags": ["User - SMS Template"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch SMS template", "description": "<PERSON><PERSON><PERSON> danh sách SMS template với phân trang và filter", "operationId": "getUserSmsTemplates", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> SMS template", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch SMS template thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SmsTemplateResponseDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - SMS Template"], "summary": "Tạo SMS template mới", "description": "Tạo template SMS mới với tên duy nhất và nội dung có thể chứa placeholder", "operationId": "createSmsTemplate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSmsTemplateDto"}}}}, "responses": {"201": {"description": "SMS template đã đ<PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - SMS Template"], "summary": "<PERSON><PERSON>a nhiều SMS template", "description": "Xóa nhiều SMS template cùng lúc", "operationId": "bulkDeleteSmsTemplates", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteSmsTemplateDto"}}}}, "responses": {"200": {"description": "Xóa SMS template thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/sms-templates/{id}": {"get": {"tags": ["User - SMS Template"], "summary": "<PERSON><PERSON><PERSON> chi tiết SMS template", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của SMS template theo ID", "operationId": "getSmsTemplateById", "parameters": [{"name": "id", "in": "path", "description": "ID của SMS template", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON> tiết SMS template", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateResponseDto"}}}}, "404": {"description": "SMS template không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "put": {"tags": ["User - SMS Template"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t SMS template", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin SMS template", "operationId": "updateSmsTemplate", "parameters": [{"name": "id", "in": "path", "description": "ID của SMS template", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSmsTemplateDto"}}}}, "responses": {"200": {"description": "SMS template đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsTemplateResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "SMS template không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - SMS Template"], "summary": "Xóa SMS template", "description": "Xóa SMS template theo ID", "operationId": "deleteSmsTemplate", "parameters": [{"name": "id", "in": "path", "description": "ID của SMS template", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "SMS template đã đư<PERSON><PERSON> xóa thành công"}, "404": {"description": "SMS template không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/user/sms-servers/configurations": {"get": {"tags": ["User - SMS Servers"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch máy chủ <PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách tất cả các máy chủ <PERSON> (<PERSON>P<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Speed) của người dùng hiện tại với phân trang", "operationId": "getSmsServerConfigurations", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch máy chủ SMS có phân trang", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách máy chủ SMS thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SmsServerResponseDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/api/email-tracking/pixel/{trackingId}": {"get": {"tags": ["User - Email Tracking"], "summary": "Track email opened via pixel", "description": "Endpoint để track khi email được mở thông qua tracking pixel 1x1. Không cần authentication vì được gọ<PERSON> từ email của người nhận", "operationId": "trackEmailPixel", "parameters": [{"name": "trackingId", "in": "path", "description": "ID tracking <PERSON><PERSON><PERSON><PERSON> embed trong email", "required": true, "schema": {"type": "string", "example": "abc123-def456-ghi789"}}], "responses": {"200": {"description": "Trả về ảnh pixel 1x1 transparent", "content": {"image/png": {"schema": {"type": "string", "format": "binary"}}}}, "500": {"description": "Lỗi server nhưng vẫn trả về pixel để không làm hỏng email"}}}}, "/api/email-tracking/click/{trackingId}": {"get": {"tags": ["User - Email Tracking"], "summary": "Track email link clicks", "description": "Endpoint để track khi link trong email đư<PERSON><PERSON> click và redirect đến URL gốc. Không cần authentication vì được gọi từ email của người nhận", "operationId": "trackEmailClick", "parameters": [{"name": "trackingId", "in": "path", "description": "ID tracking <PERSON><PERSON><PERSON><PERSON> embed trong email", "required": true, "schema": {"type": "string", "example": "abc123-def456-ghi789"}}, {"name": "url", "in": "query", "description": "URL gố<PERSON> cần redirect đến", "required": true, "schema": {"type": "string", "example": "https://example.com/product/123"}}], "responses": {"302": {"description": "Redirect đến URL gốc sau khi track thành công"}, "400": {"description": "Thiếu URL parameter"}, "500": {"description": "Lỗi server, redirect về fallback URL"}}}}, "/user/marketing/audience-custom-fields": {"get": {"tags": ["User - Marketing Custom Field"], "summary": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> danh sách các trường tùy chỉnh của người dùng với phân trang và filter", "operationId": "getAudience<PERSON>ustomFields", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}, {"name": "dataType", "in": "query", "description": "Filter theo loại dữ liệu", "required": false, "schema": {"type": "string", "enum": ["text", "number", "date", "boolean", "email", "phone", "select", "multiselect", "textarea", "url"]}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch trường tùy chỉnh", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceCustomFieldDefinitionResponseDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - Marketing Custom Field"], "summary": "Tạo mới trường tùy chỉnh", "description": "Tạo mới trường tùy chỉnh với validation config theo dataType", "operationId": "createAudienceCustomField", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAudienceCustomFieldDefinitionDto"}}}}, "responses": {"201": {"description": "Trường tùy chỉnh đã được tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudienceCustomFieldDefinitionResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - Marketing Custom Field"], "summary": "<PERSON><PERSON><PERSON> nhiều trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> nhiều trường tùy chỉnh cùng lúc", "operationId": "bulkDeleteAudienceCustomFields", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteCustomFieldDto"}}}}, "responses": {"200": {"description": "<PERSON>óa trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/user/marketing/audience-custom-fields/{id}": {"get": {"tags": ["User - Marketing Custom Field"], "summary": "<PERSON><PERSON><PERSON> chi tiết trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của trường tùy chỉnh theo ID", "operationId": "getAudienceCustomFieldById", "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON> tiết trường tùy chỉnh", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudienceCustomFieldDefinitionResponseDto"}}}}, "404": {"description": "Trường tùy chỉnh không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "put": {"tags": ["User - Marketing Custom Field"], "summary": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> nhật thông tin trường tùy chỉnh", "operationId": "updateAudienceCustomField", "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAudienceCustomFieldDefinitionDto"}}}}, "responses": {"200": {"description": "Trường tùy chỉnh đã được cập nhật thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudienceCustomFieldDefinitionResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "404": {"description": "Trường tùy chỉnh không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - Marketing Custom Field"], "summary": "<PERSON>óa trường tùy chỉnh", "description": "Xóa trường tùy chỉnh theo ID", "operationId": "deleteAudienceCustomField", "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Trường tùy chỉnh đã được xóa thành công"}, "404": {"description": "Trường tùy chỉnh không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/user/marketing/twilio-sms/send": {"post": {"tags": ["User - Marketing - <PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>i SMS đơn lẻ qua Twilio", "description": "<PERSON><PERSON><PERSON> tin nhắn SMS đến một số điện thoại sử dụng Twilio provider", "operationId": "sendTwilioSms", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendTwilioSmsDto"}}}}, "responses": {"200": {"description": "SMS đã đ<PERSON><PERSON><PERSON> gửi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "SMS đã đ<PERSON><PERSON><PERSON> gửi thành công"}, "data": {"type": "object", "properties": {"sid": {"type": "string", "description": "Twilio message SID"}, "status": {"type": "string", "description": "<PERSON>r<PERSON>ng thái tin nh<PERSON>n"}}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/user/marketing/twilio-sms/send-bulk": {"post": {"tags": ["User - Marketing - <PERSON><PERSON><PERSON>"], "summary": "Gửi SMS hàng loạt qua Twilio", "description": "<PERSON><PERSON><PERSON> tin nhắn SMS đến nhiều số điện thoại cùng lúc", "operationId": "sendBulkTwilioSms", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendBulkTwilioSmsDto"}}}}, "responses": {"200": {"description": "SMS hàng loạt đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "SMS hàng loạt đã đ<PERSON><PERSON><PERSON> g<PERSON>i"}, "data": {"type": "object", "properties": {"successCount": {"type": "integer", "description": "Số lượng SMS gửi thành công"}, "failureCount": {"type": "integer", "description": "Số lượng SMS gửi thất bại"}, "results": {"type": "array", "items": {"type": "object", "properties": {"phone": {"type": "string"}, "success": {"type": "boolean"}, "sid": {"type": "string"}, "error": {"type": "string"}}}}}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/system-template-emails": {"get": {"tags": ["User - System Template Email"], "summary": "<PERSON><PERSON><PERSON> danh sách system template email", "description": "<PERSON><PERSON><PERSON> danh sách các system template email có sẵn", "operationId": "getSystemTemplateEmails", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}, {"name": "category", "in": "query", "description": "Filter theo danh mục", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON> system template email", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>y danh sách system template email thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SystemTemplateEmailResponseDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/system-template-emails/{id}": {"get": {"tags": ["User - System Template Email"], "summary": "<PERSON><PERSON><PERSON> chi tiết system template email", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của system template email theo ID", "operationId": "getSystemTemplateEmailById", "parameters": [{"name": "id", "in": "path", "description": "ID của system template email", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON> tiết system template email", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemTemplateEmailResponseDto"}}}}, "404": {"description": "System template email không tồn tại"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/zns/zns-campaigns": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch chi<PERSON>n d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách chiến dịch ZNS với phân trang và filter", "operationId": "getZnsCampaigns", "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "Filter theo trạng thái", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "SENDING", "COMPLETED", "FAILED"]}}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON>ch ch<PERSON>n d<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách chiến dịch Z<PERSON> thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZnsCampaignResponseDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON>ch <PERSON> mới", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON><PERSON> (Zalo Notification Service) mới", "operationId": "createZnsCampaign", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateZnsCampaignDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> dịch Z<PERSON> đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZnsCampaignResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/zns/zns-campaigns/{id}": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết chiến d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết chiến dịch Z<PERSON> theo ID", "operationId": "getZnsCampaignById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID c<PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON><PERSON>", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Th<PERSON><PERSON> tin chi tiết chiến d<PERSON><PERSON>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZnsCampaignResponseDto"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến d<PERSON><PERSON>"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "put": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin chiến dịch <PERSON>", "operationId": "updateZnsCampaign", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID c<PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON><PERSON>", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateZnsCampaignDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chi<PERSON>n dịch <PERSON> thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZnsCampaignResponseDto"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến d<PERSON><PERSON>"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "delete": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON> theo ID", "operationId": "deleteZnsCampaign", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID c<PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON><PERSON>", "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON>ch <PERSON> thành công"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến d<PERSON><PERSON>"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/zns/send-single": {"post": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON>i ZNS đơn lẻ", "description": "<PERSON><PERSON><PERSON> tin nhắn ZNS đến một số điện thoại cụ thể", "operationId": "sendSingleZns", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSingleZnsDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON>i Z<PERSON> đơn lẻ thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZnsJobResponseDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/zns/templates": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> template", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ZNS template đã đ<PERSON><PERSON><PERSON> approve", "operationId": "getZnsTemplates", "parameters": [{"name": "oaId", "in": "query", "description": "ID của Official Account", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ZNS template thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZnsTemplateDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/{integrationId}/tags": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách tag của integration Zalo", "operationId": "getZaloTags", "parameters": [{"name": "integrationId", "in": "path", "required": true, "description": "ID của integration Zalo", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> tag thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloTagDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON>o Zalo tag mới", "description": "Tạo tag mới cho integration Zalo", "operationId": "createZaloTag", "parameters": [{"name": "integrationId", "in": "path", "required": true, "description": "ID của integration Zalo", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateZaloTagDto"}}}}, "responses": {"201": {"description": "<PERSON>alo tag đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloTagDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/{integrationId}/segments": {"get": {"tags": ["User - Zalo Segment"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON><PERSON> segment", "description": "Lấy danh sách segment của integration Zalo", "operationId": "getZaloSegments", "parameters": [{"name": "integrationId", "in": "path", "required": true, "description": "ID của integration Zalo", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON> segment", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> segment thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloSegmentDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - Zalo Segment"], "summary": "Tạo Zalo segment mới", "description": "Tạo segment mới cho integration Zalo", "operationId": "createZaloSegment", "parameters": [{"name": "integrationId", "in": "path", "required": true, "description": "ID của integration Zalo", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateZaloSegmentDto"}}}}, "responses": {"201": {"description": "Zalo segment đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloSegmentDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/{oaId}/automations": {"get": {"tags": ["User - Zalo Automation"], "summary": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> của Official Account", "operationId": "getZaloAutomations", "parameters": [{"name": "oaId", "in": "path", "required": true, "description": "ID của Official Account", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Số lượng item trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON> thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloAutomationDto"}}, "totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "itemsPerPage": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}, "post": {"tags": ["User - Zalo Automation"], "summary": "Tạo Zalo automation mới", "description": "Tạo automation mới cho Official Account", "operationId": "createZaloAutomation", "parameters": [{"name": "oaId", "in": "path", "required": true, "description": "ID của Official Account", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateZaloAutomationDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON> <PERSON> đã đư<PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZaloAutomationDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/unified-campaigns": {"post": {"tags": ["User - Zalo OA Message Campaign"], "summary": "<PERSON><PERSON><PERSON> chiến dịch tin nhắn tổng hợp", "description": "<PERSON><PERSON><PERSON> chiến dịch tin nhắn tổng <PERSON> (OA Message, ZNS, Promotion, etc.)", "operationId": "sendUnifiedMessageCampaign", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendUnifiedMessageCampaignDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chiến dịch tin nhắn tổng hợp thành công", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedMessageCampaignResultDto"}}}}, "400": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "401": {"description": "Unauthorized"}}, "security": [{"JWT-auth": []}]}}, "/integration/zalo/oauth-url": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "Lấy URL OAuth Zalo v4", "description": "Tạo URL OAuth để người dùng có thể kết nối Official Account với hệ thống. State parameter sẽ được tự tạo với tiền tố zalo_integration_oa. LUỒNG OAUTH V4: 1. Frontend gọi API này để lấy URL OAuth, 2. Frontend redirect người dùng đến URL OAuth, 3. Người dùng đăng nhập và cấp quyền trên <PERSON>, 4. <PERSON><PERSON> redirect về callback URL với authorization code, 5. Frontend gọi API callback để xử lý code và lưu thông tin OA", "operationId": "getZaloOAuthUrl", "security": [{"JWT-auth": []}], "parameters": [{"name": "redirectUri", "in": "query", "description": "URL redirect sau khi <PERSON><PERSON> thành công", "required": false, "schema": {"type": "string", "example": "https://v2.redai.vn/integration/zalo/oa"}}, {"name": "enablePKCE", "in": "query", "description": "Bật PKCE (Proof Key for Code Exchange) để tăng bảo mật", "required": false, "schema": {"type": "boolean", "example": true}}], "responses": {"200": {"description": "URL OAuth đ<PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "URL OAuth đ<PERSON> tạo thành công"}, "result": {"$ref": "#/components/schemas/ZaloOAuthUrlResponseDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi hệ thống"}}}}, "/integration/zalo/oa": {"get": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON> lý callback <PERSON><PERSON><PERSON>", "description": "Endpoint callback để nhận authorization code từ Zalo và kết nối Official Account. Đ<PERSON><PERSON> là endpoint mà Zalo sẽ redirect về sau khi người dùng cấp quyền. URL này phải được cấu hình trong Zalo App settings.", "operationId": "handleZaloOAuthCallback", "parameters": [{"name": "code", "in": "query", "description": "Authorization code từ <PERSON>", "required": true, "schema": {"type": "string", "example": "auth_code_123456"}}, {"name": "state", "in": "query", "description": "State parameter để xác thực", "required": false, "schema": {"type": "string", "example": "random_state_string"}}, {"name": "error", "in": "query", "description": "<PERSON><PERSON> lỗi nếu có (từ <PERSON>alo)", "required": false, "schema": {"type": "string", "example": "access_denied"}}, {"name": "error_description", "in": "query", "description": "<PERSON><PERSON> tả lỗi nếu có (từ Zalo)", "required": false, "schema": {"type": "string", "example": "User denied the request"}}, {"name": "user_id", "in": "query", "description": "ID người dùng (có thể đư<PERSON> t<PERSON>y<PERSON>n qua state)", "required": false, "schema": {"type": "string", "example": "123"}}], "responses": {"302": {"description": "Redirect về frontend v<PERSON><PERSON> kết quả"}, "400": {"description": "Thiếu authorization code hoặc có lỗi từ Zalo"}, "500": {"description": "Lỗi server"}}}}, "/integration/zalo/oa/callback": {"post": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON> lý callback <PERSON><PERSON><PERSON> (API)", "description": "API để xử lý authorization code từ Zalo và kết nối Official Account. Đây là API alternative cho trường hợp frontend muốn gọi API thay vì dùng redirect.", "operationId": "handleZaloOAuthCallbackApi", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string", "description": "Authorization code từ <PERSON>", "example": "auth_code_123456"}, "state": {"type": "string", "description": "State parameter để xác thực", "example": "zalo_integration_oa_1234567890"}, "codeVerifier": {"type": "string", "description": "Code verifier cho PKCE (nếu sử dụng)", "example": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"}}, "required": ["code"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> Official Account thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> Official Account thành công"}, "result": {"type": "object", "properties": {"id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "oaId": {"type": "string", "example": "1234567890123456789"}, "name": {"type": "string", "example": "RedAI Official Account"}, "description": {"type": "string", "example": "Official Account c<PERSON><PERSON>A<PERSON>"}, "avatarUrl": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/oa_avatar.jpg"}, "status": {"type": "string", "example": "ACTIVE"}, "createdAt": {"type": "integer", "example": *************}}}}}}}}, "400": {"description": "Thiếu authorization code hoặc có lỗi validation"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/webhook/app": {"post": {"tags": ["User - <PERSON><PERSON>"], "summary": "<PERSON><PERSON> lý webhook từ Zalo Official Account", "description": "Endpoint để nhận và xử lý các sự kiện webhook từ Zalo Official Account bao gồm tin nhắn từ người dùng, sự kiện follow/unfollow, sự kiện nhóm GMF. Endpoint này không yêu cầu authentication vì được gọi từ Zalo servers.", "operationId": "handleZaloWebhook", "parameters": [{"name": "X-ZEvent-Signature", "in": "header", "description": "<PERSON><PERSON> ký MAC để xác thực webhook từ Zalo", "required": true, "schema": {"type": "string", "example": "sha256=abc123def456..."}}, {"name": "X-ZEvent-Timestamp", "in": "header", "description": "Timestamp c<PERSON>a sự kiện webhook", "required": true, "schema": {"type": "string", "example": "**********"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"app_id": {"type": "string", "description": "ID của <PERSON>", "example": "1234567890123456789"}, "user_id_by_app": {"type": "string", "description": "ID người dùng trong app", "example": "user_123456"}, "event_name": {"type": "string", "description": "<PERSON><PERSON><PERSON> sự kiện webhook", "enum": ["user_send_text", "user_send_image", "user_send_file", "user_send_sticker", "user_send_location", "user_send_link", "follow", "unfollow"], "example": "user_send_text"}, "message": {"type": "object", "description": "<PERSON><PERSON><PERSON> dung tin nhắn (có khi event_name là user_send_*)", "properties": {"text": {"type": "string", "example": "Xin chào!"}, "msg_id": {"type": "string", "example": "msg_123456789"}}}, "timestamp": {"type": "integer", "description": "Timestamp c<PERSON>a sự kiện", "example": *************}, "source": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>a sự kiện", "example": "official_account"}}, "required": ["app_id", "user_id_by_app", "event_name", "timestamp"]}}}}, "responses": {"200": {"description": "<PERSON>ử lý webhook thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Webhook processed"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Webhook processed"}}}}}}}}, "400": {"description": "<PERSON><PERSON> liệu webhook không hợp lệ"}, "401": {"description": "<PERSON><PERSON> ký webhook không hợp lệ"}}}}, "/marketing/zalo/webhook/zns": {"post": {"tags": ["User - <PERSON><PERSON>"], "summary": "Xử lý webhook từ Zalo Notification Service (ZNS)", "description": "Endpoint để nhận và xử lý các sự kiện webhook từ Zalo ZNS bao gồm phản hồi người dùng, thay đổi hạn mứ<PERSON>, thay đổi chất l<PERSON> template, journey events.", "operationId": "handleZaloZnsWebhook", "parameters": [{"name": "X-ZEvent-Signature", "in": "header", "description": "<PERSON><PERSON> ký MAC để xác thực webhook từ Zalo", "required": true, "schema": {"type": "string", "example": "sha256=abc123def456..."}}, {"name": "X-ZEvent-Timestamp", "in": "header", "description": "Timestamp c<PERSON>a sự kiện webhook", "required": true, "schema": {"type": "string", "example": "**********"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"app_id": {"type": "string", "description": "ID của <PERSON>", "example": "1234567890123456789"}, "event_name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON> kiện ZNS webhook", "enum": ["zns_user_feedback", "zns_quota_change", "zns_content_type_change", "zns_template_quality_change", "zns_journey_expired", "zns_journey_charged", "zns_user_received", "zns_template_status_change"], "example": "zns_user_feedback"}, "data": {"type": "object", "description": "<PERSON><PERSON> liệu chi tiết của sự kiện ZNS"}, "timestamp": {"type": "integer", "description": "Timestamp c<PERSON>a sự kiện", "example": *************}}, "required": ["app_id", "event_name", "timestamp"]}}}}, "responses": {"200": {"description": "Xử lý webhook ZNS thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "ZNS webhook processed"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu webhook không hợp lệ"}, "401": {"description": "<PERSON><PERSON> ký webhook không hợp lệ"}}}}, "/marketing/zalo/conversation/{integrationId}/messages": {"get": {"tags": ["User - Zalo Conversation"], "summary": "<PERSON><PERSON><PERSON> tin nhắn hội tho<PERSON>i", "description": "<PERSON><PERSON><PERSON> danh sách tin nhắn trong hội thoại giữa Official Account và một người dùng cụ thể từ Zalo API. Sử dụng phân trang với page và limit (tối đa 10 tin nhắn mỗi trang).", "operationId": "getZaloConversationMessages", "parameters": [{"name": "integrationId", "in": "path", "description": "ID của Integration (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "userId", "in": "query", "description": "ID của người d<PERSON>ng <PERSON>", "required": true, "schema": {"type": "string", "example": "****************"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng tin nhắn trên mỗi trang (tối đa 10)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 10, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tin nhắn hội thoại thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tin nhắn hội thoại thành công"}, "data": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "properties": {"message_id": {"type": "string", "example": "msg_123456789"}, "message": {"type": "string", "example": "Xin chào! Tôi cần hỗ trợ."}, "src": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> tin nhắn: 0 = từ user, 1 = từ OA", "example": 0}, "time": {"type": "integer", "description": "Timestamp của tin nh<PERSON>n", "example": *************}, "type": {"type": "string", "description": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "example": "text"}, "attachments": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch file đ<PERSON>h k<PERSON> (n<PERSON>u có)", "items": {"type": "object", "properties": {"type": {"type": "string", "example": "image"}, "payload": {"type": "object", "properties": {"url": {"type": "string", "example": "https://example.com/image.jpg"}}}}}}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}, "total": {"type": "integer", "example": 25}, "hasMore": {"type": "boolean", "example": true}}}}}}}}}}, "400": {"description": "<PERSON><PERSON> số không hợp lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "userId là bắt buộc hoặc limit vượt quá 10"}, "errorCode": {"type": "string", "example": "INVALID_INPUT"}}}}}}, "404": {"description": "Integration không tồn tại", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Integration với ID \"invalid-id\" không tồn tại"}, "errorCode": {"type": "string", "example": "INTEGRATION_NOT_FOUND"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/{oaId}/followers/{userId}": {"get": {"tags": ["User - <PERSON><PERSON> Follower"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết người theo dõi", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một người theo dõi cụ thể bao gồm profile, tags, lịch sử tương tác.", "operationId": "getZaloFollowerDetail", "parameters": [{"name": "oaId", "in": "path", "description": "ID của Official Account", "required": true, "schema": {"type": "string", "example": "1234567890123456789"}}, {"name": "userId", "in": "path", "description": "ID của người d<PERSON>ng <PERSON>", "required": true, "schema": {"type": "string", "example": "****************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin người theo dõi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin người theo dõi thành công"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "userId": {"type": "string", "example": "****************"}, "displayName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/user_avatar.jpg"}, "followedAt": {"type": "integer", "description": "Timestamp khi follow", "example": *************}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCKED", "UNFOLLOWED"], "example": "ACTIVE"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "VIP Customer"}, "color": {"type": "string", "example": "#FF5722"}}}}, "lastInteraction": {"type": "integer", "description": "Timestamp tư<PERSON><PERSON> tác cu<PERSON>i", "example": *************}, "messageCount": {"type": "integer", "description": "<PERSON><PERSON> tin nhắn đã gửi", "example": 25}, "profile": {"type": "object", "description": "Thông tin profile từ Zalo", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/user_avatar.jpg"}}}}}}}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> theo dõi không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/upload/{id}/upload/image": {"post": {"tags": ["User - Zalo Upload"], "summary": "Upload <PERSON><PERSON> cho Zalo Official Account", "description": "Upload ảnh để sử dụng trong tin nhắn Zalo OA. Định dạng hỗ trợ: JPG, PNG. Dung lượng tối đa: 1MB. Ảnh sẽ được lưu trên server <PERSON><PERSON> tối đa 7 ngày. Quota: 5000 request/ngày.", "operationId": "uploadZaloImage", "parameters": [{"name": "id", "in": "path", "description": "ID của Integration (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "File ảnh cần upload (JPG, PNG, tối đa 1MB)"}}, "required": ["file"]}}}}, "responses": {"201": {"description": "Upload <PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Upload <PERSON><PERSON> thành công"}, "data": {"type": "object", "properties": {"attachmentId": {"type": "string", "description": "ID của ảnh trên server <PERSON><PERSON>", "example": "att_123456789"}, "url": {"type": "string", "description": "URL của <PERSON>", "example": "https://stc-zaloprofile.zdn.vn/pc/v1/images/att_123456789"}, "uploadedAt": {"type": "integer", "description": "Timestamp upload", "example": *************}, "expiresAt": {"type": "integer", "description": "Timestamp hết hạn (7 ngày sau upload)", "example": 1641600000000}, "fileSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)", "example": 524288}, "mimeType": {"type": "string", "description": "MIME type của file", "example": "image/jpeg"}}}}}}}}, "400": {"description": "File không hợp lệ hoặc vượt quá giới hạn", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "File vượt quá dung lượng cho phép (1MB)"}, "errorCode": {"type": "string", "example": "FILE_TOO_LARGE"}}}}}}, "404": {"description": "Integration không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "429": {"description": "Vượt quá quota upload (5000 request/ngày)"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/upload/{id}/upload/file": {"post": {"tags": ["User - Zalo Upload"], "summary": "Upload file cho Zalo Official Account", "description": "Upload file để sử dụng trong tin nhắn Zalo OA. Định dạng hỗ trợ: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX. Dung lượng tối đa: 5MB. File sẽ được lưu trên server <PERSON><PERSON> tối đa 7 ngày.", "operationId": "uploadZaloFile", "parameters": [{"name": "id", "in": "path", "description": "ID của Integration (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "File cần upload (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, tối đa 5MB)"}}, "required": ["file"]}}}}, "responses": {"201": {"description": "Upload file thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Upload file thành công"}, "data": {"type": "object", "properties": {"attachmentId": {"type": "string", "description": "ID của file trên server Zalo", "example": "att_file_123456789"}, "url": {"type": "string", "description": "URL của file", "example": "https://stc-zaloprofile.zdn.vn/pc/v1/files/att_file_123456789"}, "fileName": {"type": "string", "description": "Tên file gốc", "example": "document.pdf"}, "uploadedAt": {"type": "integer", "description": "Timestamp upload", "example": *************}, "expiresAt": {"type": "integer", "description": "Timestamp hết hạn (7 ngày sau upload)", "example": 1641600000000}, "fileSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)", "example": 2097152}, "mimeType": {"type": "string", "description": "MIME type của file", "example": "application/pdf"}}}}}}}}, "400": {"description": "File không hợp lệ hoặc vượt quá giới hạn"}, "404": {"description": "Integration không tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "429": {"description": "<PERSON><PERSON><PERSON><PERSON> quá quota upload"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/integration/{integrationId}/statistics/users/count": {"get": {"tags": ["User - Zalo Statistics"], "summary": "<PERSON><PERSON><PERSON> tổng số lượng người dùng củ<PERSON> Official Account", "description": "<PERSON><PERSON>y tổng số lượng người dùng của Official Account thông qua Zalo API. Gọi getUserList API với offset=0, count=1 để lấy thông tin total. <PERSON><PERSON><PERSON> cầu OA phải có access token hợp lệ.", "operationId": "getZaloTotalUserCount", "parameters": [{"name": "integrationId", "in": "path", "description": "UUID của Integration entity chứa thông tin Zalo OA", "required": true, "schema": {"type": "string", "format": "uuid", "example": "d331ae2f-b314-4095-963f-6a7c157658a0"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tổng số lượng người dùng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tổng số lượng người dùng thành công"}, "data": {"type": "object", "properties": {"totalUsers": {"type": "integer", "description": "Tổng số người dùng", "example": 1250}, "oaInfo": {"type": "object", "properties": {"oaId": {"type": "string", "example": "1234567890123456789"}, "name": {"type": "string", "example": "RedAI Official Account"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/oa_avatar.jpg"}}}, "lastUpdated": {"type": "integer", "description": "<PERSON><PERSON><PERSON> cập nhật cu<PERSON>i", "example": *************}}}}}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Integration hoặc Official Account"}, "401": {"description": "Official Account ch<PERSON><PERSON> có access token hoặc token đã hết hạn"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/integration/{integrationId}/statistics/followers/count": {"get": {"tags": ["User - Zalo Statistics"], "summary": "<PERSON><PERSON><PERSON> tổng số lượng follower c<PERSON><PERSON> Official Account", "description": "<PERSON><PERSON><PERSON> tổng số lượng follower của Official Account thông qua Zalo API. Gọi getUserList API với is_follower=true để lấy chỉ follower. <PERSON><PERSON><PERSON> cầu OA phải có access token hợp lệ.", "operationId": "getZaloTotalFollowerCount", "parameters": [{"name": "integrationId", "in": "path", "description": "UUID của Integration entity chứa thông tin Zalo OA", "required": true, "schema": {"type": "string", "format": "uuid", "example": "d331ae2f-b314-4095-963f-6a7c157658a0"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tổng số lượng follower thà<PERSON> công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tổng số lượng follower thà<PERSON> công"}, "data": {"type": "object", "properties": {"totalFollowers": {"type": "integer", "description": "<PERSON><PERSON><PERSON> số follower", "example": 980}, "oaInfo": {"type": "object", "properties": {"oaId": {"type": "string", "example": "1234567890123456789"}, "name": {"type": "string", "example": "RedAI Official Account"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/oa_avatar.jpg"}}}, "lastUpdated": {"type": "integer", "description": "<PERSON><PERSON><PERSON> cập nhật cu<PERSON>i", "example": *************}, "followerRate": {"type": "number", "format": "float", "description": "Tỷ lệ follower/total users (%)", "example": 78.4}}}}}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Integration hoặc Official Account"}, "401": {"description": "Official Account ch<PERSON><PERSON> có access token hoặc token đã hết hạn"}}, "security": [{"JWT-auth": []}]}}, "/marketing/zalo/integration/{integrationId}/statistics/overview": {"get": {"tags": ["User - Zalo Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê tổng quan của Zalo Official Account", "description": "<PERSON><PERSON><PERSON> thống kê tổng quan bao gồm số lượng người dùng, follower, tin <PERSON>, tư<PERSON><PERSON> tác trong khoảng thời gian.", "operationId": "getZaloOverviewStatistics", "parameters": [{"name": "integrationId", "in": "path", "description": "UUID của Integration entity chứa thông tin Zalo OA", "required": true, "schema": {"type": "string", "format": "uuid", "example": "d331ae2f-b314-4095-963f-6a7c157658a0"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê tổng quan thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê tổng quan thành công"}, "data": {"type": "object", "properties": {"totalUsers": {"type": "integer", "example": 1250}, "totalFollowers": {"type": "integer", "example": 980}, "totalMessages": {"type": "integer", "description": "Tổng số tin nhắn đã gửi", "example": 5420}, "totalInteractions": {"type": "integer", "description": "Tổng số tương tác", "example": 3250}, "followerRate": {"type": "number", "format": "float", "description": "Tỷ lệ follower (%)", "example": 78.4}, "engagementRate": {"type": "number", "format": "float", "description": "Tỷ lệ tương tác (%)", "example": 65.2}, "oaInfo": {"type": "object", "properties": {"oaId": {"type": "string", "example": "1234567890123456789"}, "name": {"type": "string", "example": "RedAI Official Account"}, "avatar": {"type": "string", "example": "https://s3.amazonaws.com/zalo-api/avatars/oa_avatar.jpg"}}}, "lastUpdated": {"type": "integer", "description": "<PERSON><PERSON><PERSON> cập nhật cu<PERSON>i", "example": *************}}}}}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Integration hoặc Official Account"}, "401": {"description": "Official Account ch<PERSON><PERSON> có access token hoặc token đã hết hạn"}}, "security": [{"JWT-auth": []}]}}, "/marketing/overview": {"get": {"tags": ["User - Marketing"], "summary": "<PERSON><PERSON><PERSON> thông tin tổng quan marketing dashboard", "description": "<PERSON><PERSON><PERSON> tất cả thông tin cần thiết cho marketing dashboard bao gồm: thống kê tổng quan (tổng số audience, campaigns, templates), hiệu suất campaigns gần đây, tỷ lệ mở email và click-through rate, xu hướng tăng trưởng audience, top performing campaigns", "operationId": "getMarketingOverview", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "Thông tin tổng quan marketing dashboard", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Thông tin overview marketing"}, "data": {"$ref": "#/components/schemas/MarketingOverviewResponseDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi server"}}}}, "/marketing/overview/recent-templates": {"get": {"tags": ["User - Marketing"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch email templates gần đ<PERSON>y", "description": "<PERSON><PERSON><PERSON> danh sách 5 email templates được sử dụng gần đây nhất: sắp xếp theo thời gian sử dụng gần nhất, bao gồm thông tin cơ bản của template, hiển thị số lần sử dụng và hiệu suất, hỗ trợ quick access để tạo campaign mới", "operationId": "getRecentTemplates", "security": [{"JWT-auth": []}], "responses": {"200": {"description": "<PERSON><PERSON> s<PERSON> email templates gần đ<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> s<PERSON>ch templates gần đ<PERSON>y"}, "data": {"$ref": "#/components/schemas/RecentTemplatesResponseDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi server"}}}}}, "components": {"securitySchemes": {"JWT-auth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Authorization header using the Bear<PERSON> scheme"}}, "schemas": {"CreateAudienceDto": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> audience", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "description": "Email c<PERSON>a audience", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i của audience", "example": "+84901234567"}, "address": {"type": "string", "description": "Địa chỉ của audience", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "dateOfBirth": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> sinh c<PERSON>a audience", "example": "1990-01-01"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của audience", "example": "male"}, "customFields": {"type": "object", "description": "<PERSON><PERSON><PERSON> trường tùy chỉnh", "additionalProperties": true}}}, "UpdateAudienceDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> audience", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "description": "Email c<PERSON>a audience", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i của audience", "example": "+84901234567"}, "address": {"type": "string", "description": "Địa chỉ của audience", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "dateOfBirth": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> sinh c<PERSON>a audience", "example": "1990-01-01"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của audience", "example": "male"}, "customFields": {"type": "object", "description": "<PERSON><PERSON><PERSON> trường tùy chỉnh", "additionalProperties": true}}}, "AudienceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Audience đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/AudienceDto"}}}, "AudienceDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của audience", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> audience", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "description": "Email c<PERSON>a audience", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i của audience", "example": "+84901234567"}, "address": {"type": "string", "description": "Địa chỉ của audience", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "dateOfBirth": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> sinh c<PERSON>a audience", "example": "1990-01-01"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của audience", "example": "male"}, "customFields": {"type": "object", "description": "<PERSON><PERSON><PERSON> trường tùy chỉnh", "additionalProperties": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "PaginatedAudienceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách audience thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "CreateCampaignDto": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string", "description": "Tên campaign", "example": "Campaign khuyến mãi mùa hè"}, "type": {"type": "string", "enum": ["email", "sms", "zalo"], "description": "Loại campaign", "example": "email"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả campaign", "example": "Campaign khuyến mãi sản phẩm mùa hè với giảm giá 50%"}, "startDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> campaign", "example": "2024-06-01T00:00:00Z"}, "endDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> th<PERSON> campaign", "example": "2024-08-31T23:59:59Z"}, "status": {"type": "string", "enum": ["draft", "active", "paused", "completed"], "description": "Trạng thái campaign", "example": "draft"}}}, "UpdateCampaignDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên campaign", "example": "Campaign khuyến mãi mùa hè"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả campaign", "example": "Campaign khuyến mãi sản phẩm mùa hè với giảm giá 50%"}, "startDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> campaign", "example": "2024-06-01T00:00:00Z"}, "endDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> th<PERSON> campaign", "example": "2024-08-31T23:59:59Z"}, "status": {"type": "string", "enum": ["draft", "active", "paused", "completed"], "description": "Trạng thái campaign", "example": "active"}}}, "CampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Campaign đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/CampaignDto"}}}, "CampaignDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của campaign", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên campaign", "example": "Campaign khuyến mãi mùa hè"}, "type": {"type": "string", "enum": ["email", "sms", "zalo"], "description": "Loại campaign", "example": "email"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả campaign", "example": "Campaign khuyến mãi sản phẩm mùa hè với giảm giá 50%"}, "startDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> campaign", "example": "2024-06-01T00:00:00Z"}, "endDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> th<PERSON> campaign", "example": "2024-08-31T23:59:59Z"}, "status": {"type": "string", "enum": ["draft", "active", "paused", "completed"], "description": "Trạng thái campaign", "example": "active"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "PaginatedCampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách campaign thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 50}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "CreateZaloCampaignDto": {"type": "object", "required": ["name", "messageType"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> m<PERSON>a hè"}, "messageType": {"type": "string", "enum": ["text", "image", "file", "template"], "description": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "example": "text"}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Chào mừng bạn đến với chương trình khuyến mãi mùa hè!"}, "templateId": {"type": "string", "description": "ID template (nếu messageType là template)", "example": "template_123"}, "scheduledTime": {"type": "integer", "description": "<PERSON>h<PERSON>i gian gửi tin nhắn (Unix timestamp)", "example": **********}, "targetAudience": {"type": "array", "items": {"type": "string"}, "description": "<PERSON>h sách ID audience mục tiêu", "example": ["audience_1", "audience_2"]}}}, "UpdateZaloCampaignDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> m<PERSON>a hè"}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Chào mừng bạn đến với chương trình khuyến mãi mùa hè!"}, "scheduledTime": {"type": "integer", "description": "<PERSON>h<PERSON>i gian gửi tin nhắn (Unix timestamp)", "example": **********}, "status": {"type": "string", "enum": ["draft", "scheduled", "sending", "sent", "failed"], "description": "<PERSON><PERSON><PERSON><PERSON> thái chiến dịch", "example": "draft"}}}, "ZaloCampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dịch <PERSON> đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/ZaloCampaignDto"}}}, "ZaloCampaignDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> c<PERSON><PERSON> chi<PERSON><PERSON> d<PERSON><PERSON>", "example": 123}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> d<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> m<PERSON>a hè"}, "messageType": {"type": "string", "enum": ["text", "image", "file", "template"], "description": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "example": "text"}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Chào mừng bạn đến với chương trình khuyến mãi mùa hè!"}, "templateId": {"type": "string", "description": "ID template (nếu messageType là template)", "example": "template_123"}, "scheduledTime": {"type": "integer", "description": "<PERSON>h<PERSON>i gian gửi tin nhắn (Unix timestamp)", "example": **********}, "status": {"type": "string", "enum": ["draft", "scheduled", "sending", "sent", "failed"], "description": "<PERSON><PERSON><PERSON><PERSON> thái chiến dịch", "example": "draft"}, "sentCount": {"type": "integer", "description": "<PERSON><PERSON> tin nhắn đã gửi", "example": 100}, "deliveredCount": {"type": "integer", "description": "<PERSON><PERSON> tin nhắn đã đ<PERSON><PERSON><PERSON> giao", "example": 95}, "readCount": {"type": "integer", "description": "<PERSON><PERSON> tin nhắn đã đư<PERSON><PERSON> đọc", "example": 80}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "PaginatedZaloCampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách chiến dịch <PERSON> thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloCampaignDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 25}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "CreateTagDto": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Tên tag (duy nhất)", "example": "VIP Customer"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả tag", "example": "<PERSON><PERSON><PERSON><PERSON> hàng <PERSON> với giá trị đơn hàng cao"}, "color": {"type": "string", "description": "Màu sắc tag (hex code)", "example": "#FF5733"}}}, "UpdateTagDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên tag (duy nhất)", "example": "VIP Customer"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả tag", "example": "<PERSON><PERSON><PERSON><PERSON> hàng <PERSON> với giá trị đơn hàng cao"}, "color": {"type": "string", "description": "Màu sắc tag (hex code)", "example": "#FF5733"}}}, "TagDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tag", "example": 1}, "name": {"type": "string", "description": "Tên tag", "example": "VIP Customer"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả tag", "example": "<PERSON><PERSON><PERSON><PERSON> hàng <PERSON> với giá trị đơn hàng cao"}, "color": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON> tag", "example": "#FF5733"}, "audienceCount": {"type": "integer", "description": "Số lượng audience có tag này", "example": 150}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "TagResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Tag đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/TagDto"}}}, "PaginatedTagResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách tag thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 50}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "CreateSegmentDto": {"type": "object", "required": ["name", "conditions"], "properties": {"name": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON> hàng mua nhi<PERSON>u"}, "description": {"type": "string", "description": "Mô tả segment", "example": "Segment khách hàng có tổng giá trị đơn hàng > 1 triệu"}, "conditions": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> kiện filter để tạo segment", "additionalProperties": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "UpdateSegmentDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON> hàng mua nhi<PERSON>u"}, "description": {"type": "string", "description": "Mô tả segment", "example": "Segment khách hàng có tổng giá trị đơn hàng > 1 triệu"}, "conditions": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> kiện filter để tạo segment", "additionalProperties": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "SegmentDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của segment", "example": 1}, "name": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON> hàng mua nhi<PERSON>u"}, "description": {"type": "string", "description": "Mô tả segment", "example": "Segment khách hàng có tổng giá trị đơn hàng > 1 triệu"}, "conditions": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n filter", "additionalProperties": true}, "audienceCount": {"type": "integer", "description": "Số lượng audience trong segment", "example": 250}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "SegmentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Segment đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/SegmentDto"}}}, "PaginatedSegmentResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>y danh sách segment thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SegmentDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 30}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "SegmentPreviewDto": {"type": "object", "required": ["conditions"], "properties": {"conditions": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> kiện filter để preview segment", "additionalProperties": true}}}, "SegmentPreviewResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Preview segment thành công"}, "data": {"type": "object", "properties": {"estimatedCount": {"type": "integer", "description": "Số lượng audience <PERSON><PERSON><PERSON>", "example": 150}, "conditions": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n filter đã áp dụng", "additionalProperties": true}}}}}, "AvailableFieldsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách field thành công"}, "data": {"type": "object", "properties": {"standardFields": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "email"}, "type": {"type": "string", "enum": ["string", "number", "date", "boolean"], "example": "string"}, "label": {"type": "string", "example": "Email"}}}}, "customFields": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "totalPurchase"}, "type": {"type": "string", "enum": ["string", "number", "date", "boolean"], "example": "number"}, "label": {"type": "string", "example": "Tổng giá trị mua hàng"}}}}}}}}, "SegmentStatsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê segment thành công"}, "data": {"type": "object", "properties": {"totalAudience": {"type": "integer", "description": "Tổng số audience trong segment", "example": 250}, "activeAudience": {"type": "integer", "description": "Số audience đang hoạt động", "example": 200}, "engagementRate": {"type": "number", "format": "float", "description": "Tỷ lệ tương tác (%)", "example": 75.5}, "averageOrderValue": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON> trị đơn hàng trung bình", "example": 1500000}, "lastCampaignDate": {"type": "integer", "description": "Ngày campaign g<PERSON><PERSON> nh<PERSON> (Unix timestamp)", "example": **********}}}}}, "PaginatedSegmentAudienceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách audience trong segment thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 250}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 25}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "CreateTemplateEmailDto": {"type": "object", "required": ["name", "subject", "htmlContent"], "properties": {"name": {"type": "string", "description": "Tên template email", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "🌞 <PERSON><PERSON><PERSON>ến mãi mùa hè - Gi<PERSON>m giá 50%"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<html><body><h1><PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè</h1><p>Giảm giá 50% cho tất cả sản phẩm!</p></body></html>"}, "textContent": {"type": "string", "description": "<PERSON><PERSON>i dung text th<PERSON><PERSON><PERSON> (fallback)", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè - Giảm giá 50% cho tất cả sản phẩm!"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả template", "example": "Template cho campaign khu<PERSON><PERSON>n mãi mùa hè"}, "category": {"type": "string", "description": "<PERSON><PERSON> m<PERSON> template", "example": "promotion"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags để phân loại template", "example": ["promotion", "summer", "discount"]}}}, "UpdateTemplateEmailDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên template email", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "🌞 <PERSON><PERSON><PERSON>ến mãi mùa hè - Gi<PERSON>m giá 50%"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<html><body><h1><PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè</h1><p>Giảm giá 50% cho tất cả sản phẩm!</p></body></html>"}, "textContent": {"type": "string", "description": "<PERSON><PERSON>i dung text th<PERSON><PERSON><PERSON> (fallback)", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè - Giảm giá 50% cho tất cả sản phẩm!"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả template", "example": "Template cho campaign khu<PERSON><PERSON>n mãi mùa hè"}, "category": {"type": "string", "description": "<PERSON><PERSON> m<PERSON> template", "example": "promotion"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags để phân loại template", "example": ["promotion", "summer", "discount"]}}}, "TemplateEmailDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của template email", "example": 1}, "name": {"type": "string", "description": "Tên template email", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "🌞 <PERSON><PERSON><PERSON>ến mãi mùa hè - Gi<PERSON>m giá 50%"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<html><body><h1><PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè</h1><p>Giảm giá 50% cho tất cả sản phẩm!</p></body></html>"}, "textContent": {"type": "string", "description": "<PERSON><PERSON>i dung text th<PERSON><PERSON><PERSON> (fallback)", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi mùa hè - Giảm giá 50% cho tất cả sản phẩm!"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả template", "example": "Template cho campaign khu<PERSON><PERSON>n mãi mùa hè"}, "category": {"type": "string", "description": "<PERSON><PERSON> m<PERSON> template", "example": "promotion"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags để phân loại template", "example": ["promotion", "summer", "discount"]}, "usageCount": {"type": "integer", "description": "Số lần sử dụng template", "example": 15}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "TemplateEmailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Template email đã đ<PERSON><PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/TemplateEmailDto"}}}, "PaginatedTemplateEmailResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách template email thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateEmailDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 25}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "TemplateEmailOverviewResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tổng quan template email thành công"}, "data": {"type": "object", "properties": {"totalTemplates": {"type": "integer", "description": "Tổng số template", "example": 25}, "activeTemplates": {"type": "integer", "description": "Số template đang sử dụng", "example": 18}, "totalUsage": {"type": "integer", "description": "Tổng số lần sử dụng", "example": 150}, "popularTemplates": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateEmailDto"}, "description": "Top template đ<PERSON><PERSON><PERSON> sử dụng nhi<PERSON>u nhất"}, "recentTemplates": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateEmailDto"}, "description": "Template <PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> gần đâ<PERSON>"}}}}}, "CreateEmailCampaignDto": {"type": "object", "required": ["name", "subject", "htmlContent", "recipientType"], "properties": {"name": {"type": "string", "description": "<PERSON>ên email campaign", "example": "Campaign email mùa hè 2024"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "🌞 Ưu đãi đặc biệt mùa hè - Gi<PERSON>m giá 50%"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<html><body><h1>Ưu đãi mùa hè</h1><p>Giảm giá 50% cho tất cả sản phẩm!</p></body></html>"}, "textContent": {"type": "string", "description": "<PERSON><PERSON>i dung text th<PERSON><PERSON><PERSON> (fallback)", "example": "Ưu đãi mùa hè - Giảm giá 50% cho tất cả sản phẩm!"}, "recipientType": {"type": "string", "enum": ["all", "segment", "custom"], "description": "<PERSON><PERSON><PERSON> ng<PERSON> n<PERSON>n", "example": "segment"}, "segmentIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID segment (nếu recipientType là segment)", "example": [1, 2, 3]}, "audienceIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID audience (nếu recipientType là custom)", "example": ["123e4567-e89b-12d3-a456-************"]}, "scheduledTime": {"type": "integer", "description": "Thời gian gửi email (Unix timestamp, null = gửi ngay)", "example": **********}, "senderName": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>i", "example": "RedAI Marketing Team"}, "senderEmail": {"type": "string", "format": "email", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "trackOpens": {"type": "boolean", "description": "<PERSON> mở email", "example": true}, "trackClicks": {"type": "boolean", "description": "<PERSON> click", "example": true}}}, "CreateEmailCampaignWithTemplateDto": {"type": "object", "required": ["name", "templateId", "recipientType"], "properties": {"name": {"type": "string", "description": "<PERSON>ên email campaign", "example": "Campaign email mùa hè 2024"}, "templateId": {"type": "integer", "description": "ID của template email", "example": 1}, "recipientType": {"type": "string", "enum": ["all", "segment", "custom"], "description": "<PERSON><PERSON><PERSON> ng<PERSON> n<PERSON>n", "example": "segment"}, "segmentIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID segment (nếu recipientType là segment)", "example": [1, 2, 3]}, "audienceIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID audience (nếu recipientType là custom)", "example": ["123e4567-e89b-12d3-a456-************"]}, "scheduledTime": {"type": "integer", "description": "Thời gian gửi email (Unix timestamp, null = gửi ngay)", "example": **********}, "customSubject": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> đề email tùy chỉnh (ghi đè template)", "example": "🌞 Ưu đãi đặc biệt mùa hè - Gi<PERSON>m giá 50%"}, "senderName": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>i", "example": "RedAI Marketing Team"}, "senderEmail": {"type": "string", "format": "email", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "trackOpens": {"type": "boolean", "description": "<PERSON> mở email", "example": true}, "trackClicks": {"type": "boolean", "description": "<PERSON> click", "example": true}}}, "EmailCampaignDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của email campaign", "example": 1}, "name": {"type": "string", "description": "<PERSON>ên email campaign", "example": "Campaign email mùa hè 2024"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "🌞 Ưu đãi đặc biệt mùa hè - Gi<PERSON>m giá 50%"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<html><body><h1>Ưu đãi mùa hè</h1><p>Giảm giá 50% cho tất cả sản phẩm!</p></body></html>"}, "textContent": {"type": "string", "description": "Nội dung text thu<PERSON>n", "example": "Ưu đãi mùa hè - Giảm giá 50% cho tất cả sản phẩm!"}, "status": {"type": "string", "enum": ["draft", "scheduled", "sending", "sent", "failed", "cancelled"], "description": "Trạng thái campaign", "example": "sent"}, "recipientType": {"type": "string", "enum": ["all", "segment", "custom"], "description": "<PERSON><PERSON><PERSON> ng<PERSON> n<PERSON>n", "example": "segment"}, "totalRecipients": {"type": "integer", "description": "<PERSON><PERSON>ng số ng<PERSON>n", "example": 1500}, "sentCount": {"type": "integer", "description": "Số email đã gửi", "example": 1450}, "deliveredCount": {"type": "integer", "description": "Số <PERSON> đã đ<PERSON><PERSON><PERSON> giao", "example": 1400}, "openedCount": {"type": "integer", "description": "<PERSON><PERSON> <PERSON> đã được mở", "example": 850}, "clickedCount": {"type": "integer", "description": "Số email có click", "example": 320}, "bouncedCount": {"type": "integer", "description": "Số email bị bounce", "example": 50}, "unsubscribedCount": {"type": "integer", "description": "S<PERSON> người hủy đăng ký", "example": 15}, "openRate": {"type": "number", "format": "float", "description": "Tỷ lệ mở email (%)", "example": 60.7}, "clickRate": {"type": "number", "format": "float", "description": "Tỷ lệ click (%)", "example": 22.9}, "scheduledTime": {"type": "integer", "description": "<PERSON>h<PERSON><PERSON> gian g<PERSON>i đã lên lịch (Unix timestamp)", "example": **********}, "sentTime": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i thực tế (Unix timestamp)", "example": **********}, "senderName": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>i", "example": "RedAI Marketing Team"}, "senderEmail": {"type": "string", "format": "email", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "templateId": {"type": "integer", "description": "ID template đ<PERSON><PERSON><PERSON> sử dụng (n<PERSON><PERSON> c<PERSON>)", "example": 1}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": **********}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": **********}}}, "EmailCampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Email campaign đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/EmailCampaignDto"}}}, "PaginatedEmailCampaignResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách email campaign thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EmailCampaignDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 45}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "EmailCampaignOverviewResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tổng quan email campaign thành công"}, "data": {"type": "object", "properties": {"totalCampaigns": {"type": "integer", "description": "Tổng số campaign", "example": 45}, "activeCampaigns": {"type": "integer", "description": "Số campaign đang hoạt động", "example": 12}, "totalEmailsSent": {"type": "integer", "description": "Tổng số email đã gửi", "example": 25000}, "averageOpenRate": {"type": "number", "format": "float", "description": "Tỷ lệ mở email trung bình (%)", "example": 65.5}, "averageClickRate": {"type": "number", "format": "float", "description": "Tỷ lệ click trung bình (%)", "example": 25.3}, "topPerformingCampaigns": {"type": "array", "items": {"$ref": "#/components/schemas/EmailCampaignDto"}, "description": "Top campaign có hiệu suất tốt nhất"}}}}}, "RecentEmailCampaignsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách email campaign gần đ<PERSON>y thành công"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/EmailCampaignDto"}}}}, "MarketingStatisticsOverviewResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tổng quan thống kê marketing thành công"}, "data": {"type": "object", "properties": {"totalAudience": {"type": "integer", "description": "Tổng số audience", "example": 5000}, "totalCampaigns": {"type": "integer", "description": "Tổng số campaign", "example": 125}, "totalEmailsSent": {"type": "integer", "description": "Tổng số email đã gửi", "example": 50000}, "totalZaloMessagesSent": {"type": "integer", "description": "Tổng số tin nhắn Zalo đã gửi", "example": 15000}, "averageEngagementRate": {"type": "number", "format": "float", "description": "Tỷ lệ tương tác trung bình (%)", "example": 68.5}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 12.3}, "revenueGenerated": {"type": "number", "format": "float", "description": "<PERSON><PERSON>h thu tạo ra từ marketing", "example": 2500000000}, "roi": {"type": "number", "format": "float", "description": "Return on Investment (%)", "example": 350.5}, "periodComparison": {"type": "object", "properties": {"audienceGrowth": {"type": "number", "format": "float", "description": "Tăng trưởng audience so vớ<PERSON> kỳ trước (%)", "example": 15.2}, "campaignGrowth": {"type": "number", "format": "float", "description": "Tăng trưởng campaign so v<PERSON><PERSON> kỳ trước (%)", "example": 8.7}, "engagementGrowth": {"type": "number", "format": "float", "description": "Tăng trưởng tương tác so với kỳ trước (%)", "example": 22.1}}}}}}}, "AudienceGrowthStatisticsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê tăng trưởng audience thành công"}, "data": {"type": "object", "properties": {"currentTotal": {"type": "integer", "description": "Tổng số audience hiện tại", "example": 5000}, "previousTotal": {"type": "integer", "description": "Tổng số audience kỳ trước", "example": 4200}, "growthCount": {"type": "integer", "description": "Số audience tăng thêm", "example": 800}, "growthRate": {"type": "number", "format": "float", "description": "Tỷ lệ tăng trưởng (%)", "example": 19.05}, "dailyGrowth": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON>", "example": "2024-06-01"}, "count": {"type": "integer", "description": "Số audience mới trong ngày", "example": 25}, "total": {"type": "integer", "description": "Tổng số audience t<PERSON>h đến ngày đó", "example": 4225}}}, "description": "<PERSON><PERSON> liệu tăng trưởng theo ngày"}, "topSources": {"type": "array", "items": {"type": "object", "properties": {"source": {"type": "string", "description": "Nguồn audience", "example": "website"}, "count": {"type": "integer", "description": "Số audience từ nguồn này", "example": 320}, "percentage": {"type": "number", "format": "float", "description": "Tỷ lệ phần trăm", "example": 40.0}}}, "description": "Top nguồn audience"}}}}}, "CampaignPerformanceStatisticsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê hiệu suất campaign thành công"}, "data": {"type": "object", "properties": {"totalCampaigns": {"type": "integer", "description": "Tổng số campaign", "example": 125}, "activeCampaigns": {"type": "integer", "description": "Số campaign đang hoạt động", "example": 15}, "completedCampaigns": {"type": "integer", "description": "Số campaign đã hoàn thành", "example": 98}, "averageOpenRate": {"type": "number", "format": "float", "description": "Tỷ lệ mở trung bình (%)", "example": 65.5}, "averageClickRate": {"type": "number", "format": "float", "description": "Tỷ lệ click trung bình (%)", "example": 25.3}, "averageConversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi trung bình (%)", "example": 12.8}, "campaignsByType": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["email", "sms", "zalo"], "description": "Loại campaign", "example": "email"}, "count": {"type": "integer", "description": "Số lượng campaign", "example": 85}, "averagePerformance": {"type": "number", "format": "float", "description": "<PERSON><PERSON><PERSON> suất trung bình (%)", "example": 68.2}}}, "description": "<PERSON><PERSON><PERSON><PERSON> kê theo lo<PERSON> campaign"}, "topPerformingCampaigns": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "ID campaign", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên campaign", "example": "Campaign mùa hè 2024"}, "type": {"type": "string", "enum": ["email", "sms", "zalo"], "description": "Loại campaign", "example": "email"}, "openRate": {"type": "number", "format": "float", "description": "Tỷ lệ mở (%)", "example": 85.5}, "clickRate": {"type": "number", "format": "float", "description": "Tỷ lệ click (%)", "example": 35.2}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 18.7}}}, "description": "Top campaign có hiệu suất tốt nhất"}}}}}, "SegmentDistributionStatisticsResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê phân phối segment thành công"}, "data": {"type": "object", "properties": {"totalSegments": {"type": "integer", "description": "Tổng số segment", "example": 25}, "activeSegments": {"type": "integer", "description": "Số segment đang hoạt động", "example": 20}, "totalAudienceInSegments": {"type": "integer", "description": "Tổng số audience trong các segment", "example": 4500}, "segmentDistribution": {"type": "array", "items": {"type": "object", "properties": {"segmentId": {"type": "integer", "description": "ID segment", "example": 1}, "segmentName": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "audienceCount": {"type": "integer", "description": "Số audience trong segment", "example": 450}, "percentage": {"type": "number", "format": "float", "description": "Tỷ lệ phần trăm", "example": 10.0}, "averageEngagement": {"type": "number", "format": "float", "description": "Tỷ lệ tương tác trung bình (%)", "example": 75.5}}}, "description": "<PERSON><PERSON> phối audience theo segment"}, "segmentPerformance": {"type": "array", "items": {"type": "object", "properties": {"segmentId": {"type": "integer", "description": "ID segment", "example": 1}, "segmentName": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "campaignCount": {"type": "integer", "description": "Số campaign đã gửi", "example": 15}, "averageOpenRate": {"type": "number", "format": "float", "description": "Tỷ lệ mở trung bình (%)", "example": 78.5}, "averageClickRate": {"type": "number", "format": "float", "description": "Tỷ lệ click trung bình (%)", "example": 28.3}}}, "description": "<PERSON><PERSON><PERSON> su<PERSON> c<PERSON>a c<PERSON>c segment"}}}}}, "ZaloOfficialAccountDto": {"type": "object", "properties": {"oaId": {"type": "string", "description": "ID của Official Account", "example": "1234567890123456789"}, "name": {"type": "string", "description": "Tên Official Account", "example": "RedAI Official"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả Official Account", "example": "<PERSON><PERSON><PERSON> ch<PERSON>h thức của RedAI"}, "avatar": {"type": "string", "description": "URL avatar", "example": "https://example.com/avatar.jpg"}, "cover": {"type": "string", "description": "URL ảnh bìa", "example": "https://example.com/cover.jpg"}, "isVerified": {"type": "boolean", "description": "<PERSON>r<PERSON>ng thái x<PERSON>c minh", "example": true}, "followerCount": {"type": "integer", "description": "<PERSON><PERSON> ng<PERSON>ời theo dõi", "example": 15000}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"], "description": "<PERSON>r<PERSON><PERSON> thái tài <PERSON>n", "example": "active"}, "connectedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết n<PERSON>i (Unix timestamp)", "example": **********}, "lastSyncAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON> đồng bộ cuối (Unix timestamp)", "example": **********}}}, "ZaloOfficialAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin Official Account thành công"}, "data": {"$ref": "#/components/schemas/ZaloOfficialAccountDto"}}}, "ZaloOfficialAccountListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách Official Account thành công"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloOfficialAccountDto"}}}}, "PaginatedZaloOfficialAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách Official Account thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloOfficialAccountDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 5}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 1}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "ZaloFollowerDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID người d<PERSON>ng <PERSON>", "example": "1234567890123456789"}, "displayName": {"type": "string", "description": "<PERSON><PERSON><PERSON> hiển thị", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "description": "URL avatar", "example": "https://example.com/avatar.jpg"}, "isFollowing": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái theo dõi", "example": true}, "followedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đầu theo dõi (Unix timestamp)", "example": **********}, "lastInteractionAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> tác <PERSON> (Unix timestamp)", "example": **********}, "tags": {"type": "array", "items": {"type": "string"}, "description": "<PERSON>s của follower", "example": ["vip", "active"]}}}, "PaginatedZaloFollowerResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách người theo dõi thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ZaloFollowerDto"}}, "totalItems": {"type": "integer", "description": "Tổng số item", "example": 15000}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 1500}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 10}, "hasItems": {"type": "boolean", "description": "Có item hay không", "example": true}}}}}, "SmsCampaignItemDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON> d<PERSON>ch SMS khuyến mãi Black Friday"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> d<PERSON>ch SMS marketing cho sự kiện Black Friday"}, "campaignType": {"type": "string", "enum": ["OTP", "ADS"], "example": "ADS"}, "status": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "SENDING", "SENT", "FAILED"], "example": "SENT"}, "totalRecipients": {"type": "integer", "example": 150}, "sentCount": {"type": "integer", "example": 145}, "failedCount": {"type": "integer", "example": 5}, "successRate": {"type": "number", "example": 96.7}, "scheduledAt": {"type": "integer", "description": "Unix timestamp", "example": 1703980800}, "startedAt": {"type": "integer", "description": "Unix timestamp", "example": 1703980800}, "completedAt": {"type": "integer", "description": "Unix timestamp", "example": 1703981200}, "createdAt": {"type": "integer", "description": "Unix timestamp", "example": 1703980000}, "updatedAt": {"type": "integer", "description": "Unix timestamp", "example": 1703981200}}}, "CreateSmsCampaignDto": {"type": "object", "required": ["name", "templateId", "smsIntegrationId"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>", "example": "<PERSON><PERSON><PERSON> d<PERSON>ch SMS Black Friday"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "example": "<PERSON><PERSON><PERSON> d<PERSON>ch SMS marketing cho sự kiện Black Friday"}, "templateId": {"type": "integer", "description": "ID của SMS template", "example": 15}, "smsIntegrationId": {"type": "string", "description": "ID của SMS integration (Twilio, FPT, etc.)", "example": "sms_integration_123"}, "campaignType": {"type": "string", "enum": ["OTP", "ADS"], "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>", "example": "ADS"}, "segmentIds": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID segments", "example": [1, 2, 3]}, "audienceIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch ID audiences", "example": [100, 101, 102]}, "phoneNumbers": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch số điện thoại", "example": ["+84901234567", "+84987654321"]}, "scheduledAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian lên lịch g<PERSON> (Unix timestamp)", "example": 1703980800}, "sendImmediately": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> ngay lập tức", "example": false}}}, "CreateSmsCampaignResponseDto": {"type": "object", "properties": {"campaignId": {"type": "integer", "example": 124}, "jobCount": {"type": "integer", "description": "Số lượng jobs đ<PERSON><PERSON><PERSON> tạo", "example": 1}, "jobIds": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch job IDs", "example": ["job_2"]}, "status": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "SENDING"], "example": "SENDING"}, "scheduledAt": {"type": "integer", "description": "Unix timestamp", "example": 1703980800}, "totalRecipients": {"type": "integer", "example": 150}, "campaignType": {"type": "string", "enum": ["OTP", "ADS"], "example": "ADS"}}}, "SmsTemplateResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của template", "example": 1}, "customName": {"type": "string", "description": "Tên template", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi"}, "customContent": {"type": "string", "description": "Nội dung template", "example": "<PERSON>n chào {{customerName}}, chúng tôi có ưu đãi đặc biệt!"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON>ng thái active", "example": true}}}, "CreateSmsTemplateDto": {"type": "object", "required": ["customName", "customContent"], "properties": {"customName": {"type": "string", "description": "Tên template", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi"}, "customContent": {"type": "string", "description": "Nội dung template", "example": "<PERSON>n chào {{customerName}}, chúng tôi có ưu đãi đặc biệt!"}}}, "UpdateSmsTemplateDto": {"type": "object", "properties": {"customName": {"type": "string", "description": "Tên template", "example": "Template <PERSON><PERSON><PERSON><PERSON><PERSON> mãi updated"}, "customContent": {"type": "string", "description": "Nội dung template", "example": "<PERSON>n chào {{customerName}}, chúng tôi có ưu đãi mới!"}}}, "BulkDeleteSmsTemplateDto": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON> c<PERSON>n x<PERSON>a", "example": [1, 2, 3]}}}, "BulkDeleteSmsCampaignDto": {"type": "object", "required": ["campaignIds"], "properties": {"campaignIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID campaigns c<PERSON><PERSON> x<PERSON><PERSON> (tối đa 50)", "example": [1, 2, 3, 4, 5], "maxItems": 50}}}, "SmsServerResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của SMS server", "example": 1}, "provider": {"type": "string", "description": "<PERSON>hà cung cấp SMS", "enum": ["FPT", "TWILIO", "VONAGE", "SPEED"], "example": "TWILIO"}, "serverName": {"type": "string", "description": "Tên server", "example": "Twilio SMS Production"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "SendTwilioSmsDto": {"type": "object", "required": ["to", "body"], "properties": {"to": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i người nhận", "example": "+84901234567"}, "body": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Xin chào! <PERSON><PERSON><PERSON> là tin nhắn test từ Twilio SMS."}}}, "SendBulkTwilioSmsDto": {"type": "object", "required": ["recipients", "body"], "properties": {"recipients": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch số điện thoại người nhận", "example": ["+84901234567", "+84987654321"]}, "body": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Xin chào! <PERSON><PERSON>y là tin nhắn hàng loạt từ Twilio SMS."}}}, "SystemTemplateEmailResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của system template", "example": 1}, "name": {"type": "string", "description": "Tên template", "example": "<PERSON><PERSON> ch<PERSON>o mừng"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "<PERSON><PERSON><PERSON> mừng bạn đến với hệ thống"}, "htmlContent": {"type": "string", "description": "Nội dung HTML", "example": "<h1><PERSON><PERSON><PERSON> mừng {{customerName}}</h1>"}, "category": {"type": "string", "description": "<PERSON><PERSON> m<PERSON> template", "example": "Onboarding"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "AudienceCustomFieldDefinitionResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của trường tùy chỉnh", "example": 1}, "fieldKey": {"type": "string", "description": "<PERSON><PERSON>ó<PERSON> trường", "example": "full_name"}, "fieldName": {"type": "string", "description": "<PERSON><PERSON><PERSON> hiển thị của trường", "example": "Họ và tên"}, "dataType": {"type": "string", "description": "<PERSON><PERSON><PERSON> dữ liệu", "enum": ["text", "number", "date", "boolean", "email", "phone", "select", "multiselect", "textarea", "url"], "example": "text"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "CreateAudienceCustomFieldDefinitionDto": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "fieldName", "dataType"], "properties": {"fieldKey": {"type": "string", "description": "<PERSON><PERSON>ó<PERSON> trường", "example": "full_name"}, "fieldName": {"type": "string", "description": "<PERSON><PERSON><PERSON> hiển thị của trường", "example": "Họ và tên"}, "dataType": {"type": "string", "description": "<PERSON><PERSON><PERSON> dữ liệu", "enum": ["text", "number", "date", "boolean", "email", "phone", "select", "multiselect", "textarea", "url"], "example": "text"}}}, "UpdateAudienceCustomFieldDefinitionDto": {"type": "object", "properties": {"fieldName": {"type": "string", "description": "<PERSON><PERSON><PERSON> hiển thị của trường", "example": "<PERSON><PERSON> và tên đ<PERSON>y đủ"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "BulkDeleteCustomFieldDto": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON> c<PERSON>n x<PERSON>a", "example": [1, 2, 3]}}}, "BulkDeleteResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thành công"}, "data": {"type": "object", "properties": {"deletedCount": {"type": "integer", "description": "Số lượng đã xóa", "example": 3}, "failedCount": {"type": "integer", "description": "<PERSON><PERSON> l<PERSON> thất bại", "example": 0}}}}}, "SmsCampaignOverviewDto": {"type": "object", "properties": {"totalCampaigns": {"type": "integer", "description": "Tổng số campaigns", "example": 25}, "campaignsByStatus": {"type": "object", "properties": {"draft": {"type": "integer", "example": 3}, "scheduled": {"type": "integer", "example": 2}, "sending": {"type": "integer", "example": 1}, "sent": {"type": "integer", "example": 18}, "failed": {"type": "integer", "example": 1}}}, "totalSmsSent": {"type": "integer", "description": "Tổng số SMS đã gửi", "example": 5420}, "totalSmsSuccess": {"type": "integer", "description": "Tổng số SMS gửi thành công", "example": 5285}, "totalSmsFailed": {"type": "integer", "description": "Tổng số SMS gửi thất bại", "example": 135}, "overallSuccessRate": {"type": "number", "description": "Tỷ lệ thành công tổng thể (%)", "example": 97.5}, "activeCampaigns": {"type": "integer", "description": "Số campaigns đang hoạt động", "example": 3}, "totalCost": {"type": "number", "description": "Tổng chi phí (VND)", "example": 1084000}, "averageCostPerSms": {"type": "number", "description": "Chi phí trung bình mỗi SMS (VND)", "example": 200}}}, "ZnsCampaignResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID c<PERSON><PERSON> ch<PERSON><PERSON>n d<PERSON><PERSON>", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> k<PERSON>n mãi"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "example": "<PERSON><PERSON><PERSON> thông báo khu<PERSON>ến mãi cho khách hàng"}, "oaId": {"type": "string", "description": "ID của Official Account", "example": "oa123456789"}, "templateId": {"type": "string", "description": "ID của ZNS template", "example": "template_promotion_456"}, "templateData": {"type": "object", "description": "<PERSON><PERSON> liệu cho template", "example": {"shopName": "RedAI Shop", "discountPercent": "50%"}}, "status": {"type": "string", "enum": ["DRAFT", "SCHEDULED", "SENDING", "COMPLETED", "FAILED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái chiến dịch", "example": "DRAFT"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "example": "2024-01-01T00:00:00Z"}}}, "CreateZnsCampaignDto": {"type": "object", "required": ["integrationId", "name", "templateId", "templateData"], "properties": {"integrationId": {"type": "string", "description": "ID Integration của Zalo OA", "example": "uuid-integration-id"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> k<PERSON>n mãi"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "example": "<PERSON><PERSON><PERSON> thông báo khu<PERSON>ến mãi cho khách hàng"}, "templateId": {"type": "string", "description": "ID của ZNS template", "example": "template_promotion_456"}, "templateData": {"type": "object", "description": "<PERSON><PERSON> liệu cho template", "example": {"shopName": "RedAI Shop", "discountPercent": "50%"}}, "targetAudienceType": {"type": "string", "enum": ["PHONE_LIST", "SEGMENT", "AUDIENCE_LIST"], "description": "Loại target audience", "example": "PHONE_LIST"}, "phoneList": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch số điện thoại", "example": ["**********", "0987654321"]}, "segmentId": {"type": "integer", "description": "ID của segment", "example": 123}, "audienceIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h s<PERSON>ch ID audience", "example": [1, 2, 3]}, "status": {"type": "string", "enum": ["DRAFT", "SCHEDULED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái chiến dịch", "example": "DRAFT"}, "scheduledAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian lên l<PERSON> (Unix timestamp)", "example": **********}}}, "UpdateZnsCampaignDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> k<PERSON>ến mãi cập nh<PERSON>t"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "example": "<PERSON><PERSON><PERSON> thông bá<PERSON> khu<PERSON>ến mãi cho khách hàng <PERSON>"}, "templateData": {"type": "object", "description": "<PERSON><PERSON> liệu cho template", "example": {"shopName": "RedAI Shop", "discountPercent": "70%"}}, "status": {"type": "string", "enum": ["DRAFT", "SCHEDULED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái chiến dịch", "example": "SCHEDULED"}, "scheduledAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian lên l<PERSON> (Unix timestamp)", "example": **********}}}, "SendSingleZnsDto": {"type": "object", "required": ["oaId", "phone", "templateId", "templateData"], "properties": {"oaId": {"type": "string", "description": "ID của Official Account", "example": "oa123456789"}, "phone": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i người nhận", "example": "**********"}, "templateId": {"type": "string", "description": "ID của ZNS template", "example": "template_promotion_456"}, "templateData": {"type": "object", "description": "<PERSON><PERSON> liệu cho template", "example": {"shopName": "RedAI Shop", "discountPercent": "50%"}}, "trackingId": {"type": "string", "description": "ID để tracking", "example": "track_123456"}}}, "ZnsJobResponseDto": {"type": "object", "properties": {"jobId": {"type": "string", "description": "ID của job", "example": "job_123456789"}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái job", "example": "PENDING"}, "message": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "example": "<PERSON> đã đ<PERSON><PERSON><PERSON> tạo thành công"}}}, "ZnsTemplateDto": {"type": "object", "properties": {"templateId": {"type": "string", "description": "ID của template", "example": "template_promotion_456"}, "templateName": {"type": "string", "description": "Tên template", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi sản phẩm"}, "templateContent": {"type": "string", "description": "Nội dung template", "example": "Chào {{customerName}}, {{shopName}} có chương trình khu<PERSON>ến mãi {{discountPercent}}% cho tất cả sản phẩm!"}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"], "description": "Tr<PERSON>ng thái template", "example": "APPROVED"}, "templateType": {"type": "string", "description": "Loại template", "example": "PROMOTION"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}}}, "ZaloTagDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tag", "example": 1}, "name": {"type": "string", "description": "Tên tag", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả tag", "example": "Tag cho khách hàng <PERSON>"}, "color": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> tag", "example": "#FF5733"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}}}, "CreateZaloTagDto": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Tên tag", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả tag", "example": "Tag cho khách hàng <PERSON>"}, "color": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> tag", "example": "#FF5733"}}}, "ZaloSegmentDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của segment", "example": 1}, "name": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "string", "description": "Mô tả segment", "example": "Segment kh<PERSON>ch hàng <PERSON>"}, "criteria": {"type": "object", "description": "Tiêu chí của segment", "example": {"totalSpent": {"operator": ">=", "value": 1000000}}}, "audienceCount": {"type": "integer", "description": "Số lượng audience trong segment", "example": 150}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}}}, "CreateZaloSegmentDto": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Tên segment", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "string", "description": "Mô tả segment", "example": "Segment kh<PERSON>ch hàng <PERSON>"}, "criteria": {"type": "object", "description": "Tiêu chí của segment", "example": {"totalSpent": {"operator": ">=", "value": 1000000}}}}}, "ZaloAutomationDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của automation", "example": 1}, "name": {"type": "string", "description": "Tên automation", "example": "<PERSON><PERSON>o mừng khách hàng mới"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả <PERSON>", "example": "Tự động gửi tin nhắn chào mừng khách hàng mới"}, "trigger": {"type": "object", "description": "<PERSON>gger của automation", "example": {"type": "NEW_FOLLOWER", "conditions": {}}}, "actions": {"type": "array", "description": "<PERSON><PERSON> s<PERSON> action", "items": {"type": "object"}, "example": [{"type": "SEND_MESSAGE", "templateId": "welcome_template", "delay": 0}]}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON>ng thái active", "example": true}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}}}, "CreateZaloAutomationDto": {"type": "object", "required": ["name", "trigger", "actions"], "properties": {"name": {"type": "string", "description": "Tên automation", "example": "<PERSON><PERSON>o mừng khách hàng mới"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả <PERSON>", "example": "Tự động gửi tin nhắn chào mừng khách hàng mới"}, "trigger": {"type": "object", "description": "<PERSON>gger của automation", "example": {"type": "NEW_FOLLOWER", "conditions": {}}}, "actions": {"type": "array", "description": "<PERSON><PERSON> s<PERSON> action", "items": {"type": "object"}, "example": [{"type": "SEND_MESSAGE", "templateId": "welcome_template", "delay": 0}]}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON>ng thái active", "example": true}}}, "SendUnifiedMessageCampaignDto": {"type": "object", "required": ["recipients", "messageType", "contentType", "integrationId"], "properties": {"recipients": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch recipients (số điện thoại hoặc Zalo ID)", "example": ["**********", "0987654321"]}, "messageType": {"type": "string", "enum": ["consultation", "transaction", "promotion", "zns"], "description": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "example": "promotion"}, "contentType": {"type": "string", "enum": ["consultation_text", "transaction_order_confirmation", "promotion_campaign", "zns_template"], "description": "<PERSON><PERSON><PERSON> nội dung", "example": "promotion_campaign"}, "integrationId": {"type": "string", "description": "ID của integration", "example": "integration_123456"}, "consultationContent": {"type": "object", "description": "<PERSON><PERSON><PERSON> dung tin nhắn tư vấn", "properties": {"text": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung tin nhắn", "example": "Xin chào! Chúng tôi sẵn sàng hỗ trợ bạn 24/7."}}}, "transactionContent": {"type": "object", "description": "<PERSON><PERSON><PERSON> dung tin nhắn giao dịch", "properties": {"templateId": {"type": "string", "description": "ID template", "example": "order_confirmation_template"}, "templateData": {"type": "object", "description": "<PERSON><PERSON> liệu template", "example": {"orderNumber": "ORD001", "customerName": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "promotionContent": {"type": "object", "description": "<PERSON><PERSON><PERSON> dung tin nhắn khu<PERSON>ến mãi", "properties": {"title": {"type": "string", "description": "<PERSON>i<PERSON><PERSON> đề khu<PERSON>ến mãi", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi cuối năm"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> khu<PERSON>ến mãi", "example": "<PERSON><PERSON><PERSON><PERSON> giá lên đến 50% cho tất cả sản phẩm"}, "discountPercent": {"type": "number", "description": "Phần tr<PERSON>m giảm giá", "example": 50}}}, "znsContent": {"type": "object", "description": "<PERSON>ội dung tin nhắn ZNS", "properties": {"templateId": {"type": "string", "description": "ID template ZNS", "example": "zns_template_123"}, "templateData": {"type": "object", "description": "<PERSON>ữ liệu template ZNS", "example": {"customerName": "<PERSON><PERSON><PERSON><PERSON>", "orderNumber": "ORD001"}}}}, "campaignName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> dị<PERSON> k<PERSON>ến mãi cuối năm"}, "campaignDescription": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "example": "<PERSON><PERSON><PERSON> tin nhắn khuyến mãi cho khách hàng <PERSON>"}}}, "UnifiedMessageCampaignResultDto": {"type": "object", "properties": {"campaignId": {"type": "string", "description": "<PERSON> c<PERSON>a chi<PERSON>n d<PERSON>ch", "example": "campaign_123456"}, "totalRecipients": {"type": "integer", "description": "Tổng số recipients", "example": 100}, "successCount": {"type": "integer", "description": "S<PERSON> lư<PERSON> gửi thành công", "example": 95}, "failedCount": {"type": "integer", "description": "Số lư<PERSON> gửi thất bại", "example": 5}, "results": {"type": "array", "description": "<PERSON> tiết kết quả", "items": {"type": "object", "properties": {"recipient": {"type": "string", "description": "Recipient", "example": "**********"}, "messageId": {"type": "string", "description": "ID tin nhắn", "example": "msg_123456"}, "status": {"type": "string", "enum": ["sent", "failed", "pending"], "description": "<PERSON><PERSON><PERSON><PERSON> thái", "example": "sent"}, "sentAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (Unix timestamp)", "example": **********}, "error": {"type": "string", "description": "Lỗi (n<PERSON>u có)", "example": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}}}}}}, "UpdateSystemTemplateEmailDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên template email", "example": "Welcome Email Template"}, "subject": {"type": "string", "description": "Ti<PERSON><PERSON> đ<PERSON> email", "example": "Chào mừng bạn đến với RedAI!"}, "htmlContent": {"type": "string", "description": "Nội dung HTML của email", "example": "<h1><PERSON><PERSON><PERSON> mừng {{customerName}}!</h1><p>Cảm ơn bạn đã đăng ký tài khoản tại RedAI.</p>"}, "textContent": {"type": "string", "description": "Nội dung text của email", "example": "Chào mừng {{customerName}}! Cảm ơn bạn đã đăng ký tài khoản tại RedAI."}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "example": true}}}, "AudienceResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của audience", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> audience", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "description": "Email c<PERSON>a audience", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i của audience", "example": "+84901234567"}, "address": {"type": "string", "description": "Địa chỉ của audience", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "dateOfBirth": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> sinh c<PERSON>a audience", "example": "1990-01-01"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của audience", "example": "male"}, "customFields": {"type": "object", "description": "<PERSON><PERSON><PERSON> trường tùy chỉnh", "additionalProperties": true}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-01T00:00:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "example": "2024-01-01T00:00:00Z"}}}, "BulkDeleteAudienceDto": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID audience c<PERSON><PERSON> x<PERSON>a", "example": [1, 2, 3, 4, 5]}}}, "CreateAvatarUploadUrlDto": {"type": "object", "required": ["fileName", "fileType"], "properties": {"fileName": {"type": "string", "description": "Tên file avatar", "example": "avatar.jpg"}, "fileType": {"type": "string", "description": "Loại file (MIME type)", "example": "image/jpeg"}, "fileSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)", "example": 524288}}}, "AvatarUploadUrlResponseDto": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload avatar", "example": "https://s3.amazonaws.com/bucket/upload-url"}, "fileKey": {"type": "string", "description": "Key của file trên S3", "example": "avatars/user-123/avatar-**********.jpg"}, "expiresAt": {"type": "integer", "description": "Timestamp hết hạn URL", "example": 1640998800000}}}, "UpdateAvatarDto": {"type": "object", "required": ["fileKey"], "properties": {"fileKey": {"type": "string", "description": "Key của file avatar <PERSON><PERSON> upload", "example": "avatars/user-123/avatar-**********.jpg"}, "fileName": {"type": "string", "description": "Tên file avatar", "example": "avatar.jpg"}}}, "MergeUserAudienceDto": {"type": "object", "required": ["sourceAudienceIds", "targetAudienceId"], "properties": {"sourceAudienceIds": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID audience nguồn cần merge", "example": [1, 2, 3]}, "targetAudienceId": {"type": "integer", "description": "ID audience đích để merge vào", "example": 4}, "mergeStrategy": {"type": "string", "enum": ["KEEP_TARGET", "KEEP_SOURCE", "MERGE_FIELDS"], "description": "Chiến lược merge dữ liệu", "example": "MERGE_FIELDS"}}}, "MergeUserAudienceResponseDto": {"type": "object", "properties": {"mergedAudienceId": {"type": "integer", "description": "ID audience sau khi merge", "example": 4}, "mergedCount": {"type": "integer", "description": "Số lượng audience đã đượ<PERSON> merge", "example": 3}, "deletedAudienceIds": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID audience đã bị xóa sau merge", "example": [1, 2, 3]}, "mergedAt": {"type": "integer", "description": "Timestamp thực hiện merge", "example": *************}}}, "CampaignHistoryResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> lịch sử", "example": 1}, "campaignId": {"type": "integer", "description": "ID campaign", "example": 123}, "action": {"type": "string", "description": "<PERSON><PERSON><PERSON> động thực hiện", "example": "STARTED"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả chi tiết", "example": "Campaign đã đ<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu"}, "performedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "example": "<EMAIL>"}, "performedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n", "example": *************}, "metadata": {"type": "object", "description": "<PERSON><PERSON> li<PERSON><PERSON> b<PERSON> sung", "additionalProperties": true}}}, "CreateTemplateCampaignDto": {"type": "object", "required": ["templateId", "name"], "properties": {"templateId": {"type": "integer", "description": "ID template để tạo campaign", "example": 5}, "name": {"type": "string", "description": "Tên campaign mới", "example": "Campaign từ Template ABC"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả campaign", "example": "Campaign đ<PERSON><PERSON><PERSON> tạo từ template ABC"}, "startDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> campaign", "example": "2024-01-01T00:00:00Z"}, "endDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> th<PERSON> campaign", "example": "2024-01-31T23:59:59Z"}, "customizations": {"type": "object", "description": "Tùy chỉnh cho campaign", "additionalProperties": true, "example": {"subject": "Custom Subject", "content": "Custom Content"}}}}, "CreateTemplateCampaignResponseDto": {"type": "object", "properties": {"campaignId": {"type": "integer", "description": "ID campaign đã tạo", "example": 456}, "templateId": {"type": "integer", "description": "ID template đ<PERSON> sử dụng", "example": 5}, "name": {"type": "string", "description": "Tên campaign", "example": "Campaign từ Template ABC"}, "status": {"type": "string", "description": "Trạng thái campaign", "example": "DRAFT"}, "createdAt": {"type": "integer", "description": "Timestamp tạo campaign", "example": *************}}}, "PaginationInfo": {"type": "object", "properties": {"totalItems": {"type": "integer", "description": "Tổng số item", "example": 150}, "itemCount": {"type": "integer", "description": "Số item trong trang hiện tại", "example": 20}, "itemsPerPage": {"type": "integer", "description": "Số item trên mỗi trang", "example": 20}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 8}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "hasNextPage": {"type": "boolean", "description": "<PERSON><PERSON> trang tiếp theo không", "example": true}, "hasPreviousPage": {"type": "boolean", "description": "<PERSON><PERSON> trang tr<PERSON><PERSON><PERSON> không", "example": false}}}, "MarketingOverviewResponseDto": {"type": "object", "properties": {"totalAudience": {"type": "integer", "description": "Tổng số audience", "example": 1250}, "totalCampaigns": {"type": "integer", "description": "Tổng số campaigns", "example": 45}, "totalTemplates": {"type": "integer", "description": "Tổng số templates", "example": 12}, "totalEmailsSent": {"type": "integer", "description": "<PERSON><PERSON>ng số <PERSON> đã gửi", "example": 15600}, "averageOpenRate": {"type": "number", "description": "Tỷ lệ mở email trung bình (%)", "example": 24.5}, "averageClickRate": {"type": "number", "description": "Tỷ lệ click trung bình (%)", "example": 3.2}, "audienceGrowth": {"type": "object", "properties": {"thisMonth": {"type": "integer", "description": "Số audience tăng tháng này", "example": 85}, "lastMonth": {"type": "integer", "description": "Số audience tăng tháng tr<PERSON>c", "example": 67}, "growthRate": {"type": "number", "description": "Tỷ lệ tăng trưởng (%)", "example": 26.9}}}, "recentCampaigns": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "Summer Sale Campaign"}, "sentAt": {"type": "string", "format": "date-time", "example": "2024-06-20T10:00:00Z"}, "openRate": {"type": "number", "example": 28.5}, "clickRate": {"type": "number", "example": 4.1}}}}}}, "RecentTemplatesResponseDto": {"type": "object", "properties": {"templates": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 456}, "name": {"type": "string", "example": "Welcome Email Template"}, "subject": {"type": "string", "example": "Chào mừng bạn đến với RedAI"}, "lastUsedAt": {"type": "string", "format": "date-time", "example": "2024-06-20T15:30:00Z"}, "usageCount": {"type": "integer", "description": "Số lần sử dụng", "example": 15}, "averageOpenRate": {"type": "number", "description": "Tỷ lệ mở trung bình (%)", "example": 32.1}}}}}}, "PaginatedSmsCampaignResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SmsCampaignItemDto"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "ZaloOAuthUrlResponseDto": {"type": "object", "properties": {"oauthUrl": {"type": "string", "description": "URL OAuth để redirect ngư<PERSON><PERSON> dùng", "example": "https://oauth.zaloapp.com/v4/oa/permission?app_id=123&redirect_uri=https://example.com/callback&state=random_state"}, "state": {"type": "string", "description": "State parameter để xác thực", "example": "zalo_integration_oa_1703980800_random"}, "codeVerifier": {"type": "string", "description": "Code verifier cho PKCE (chỉ có khi enablePKCE=true)", "example": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"}}}}}}