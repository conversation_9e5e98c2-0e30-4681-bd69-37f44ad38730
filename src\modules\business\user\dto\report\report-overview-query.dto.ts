import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho các khoảng thời gian báo cáo
 */
export enum ReportPeriodEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
}

/**
 * Custom validator để kiểm tra endDate >= startDate (deprecated - không dùng nữa)
 */
export class DateRangeValidator {
  validate(endDate: string, args: any): boolean {
    const startDate = args.object.startDate;
    if (!startDate || !endDate) return true;
    return new Date(endDate) >= new Date(startDate);
  }

  defaultMessage(): string {
    return 'Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu';
  }
}

/**
 * Enum cho loại dữ liệu báo cáo tổng quan
 */
export enum ReportOverviewDataType {
  REVENUE = 'REVENUE',
  ORDERS = 'ORDERS',
  CUSTOMERS = 'CUSTOMERS',
  PRODUCTS = 'PRODUCTS',
}

/**
 * DTO cho query parameters của API tổng quan báo cáo
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class ReportOverviewQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu báo cáo
   */
  @ApiProperty({
    description: 'Loại dữ liệu báo cáo',
    enum: ReportOverviewDataType,
    example: ReportOverviewDataType.REVENUE,
    required: false,
    default: ReportOverviewDataType.REVENUE,
  })
  @IsOptional()
  @IsEnum(ReportOverviewDataType)
  type?: ReportOverviewDataType;

  /**
   * Khoảng thời gian báo cáo
   */
  @ApiProperty({
    description: 'Khoảng thời gian báo cáo',
    enum: ReportPeriodEnum,
    example: ReportPeriodEnum.MONTH,
    default: ReportPeriodEnum.MONTH,
    required: false,
  })
  @IsOptional()
  @IsEnum(ReportPeriodEnum, { message: 'Khoảng thời gian không hợp lệ' })
  period?: ReportPeriodEnum = ReportPeriodEnum.MONTH;
}
