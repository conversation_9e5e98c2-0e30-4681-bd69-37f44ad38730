import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiT<PERSON>s,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { User } from '@modules/user/entities/user.entity';
import { BusinessReportService } from '@modules/business/user/services';
import { ApiResponseDto } from '@common/response/api-response-dto';

import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  BusinessLineChartQueryDto,
  BusinessLineChartResponseDto,
} from '../dto/report';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các endpoint báo cáo business cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_REPORT)
@Controller('user/business/reports')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  ReportOverviewResponseDto,
  OrdersChartResponseDto,
  BusinessLineChartResponseDto,
)
export class BusinessReportController {
  constructor(private readonly businessReportService: BusinessReportService) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy dữ liệu tổng quan báo cáo',
    description:
      'API lấy dữ liệu tổng quan bao gồm tổng doanh thu, tổng đơn hàng, khách hàng mới và so sánh với kỳ trước',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu tổng quan thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/ReportOverviewResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực',
  })
  async getOverview(
    @CurrentUser() user: User,
    @Query() query: ReportOverviewQueryDto,
  ): Promise<ApiResponseDto<ReportOverviewResponseDto>> {
    const data = await this.businessReportService.getReportOverview(
      user.id,
      query,
    );
    return ApiResponseDto.success(data, 'Lấy dữ liệu tổng quan thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ đường business (API chính - thay thế customers-chart và sales-chart)
   */
  @Get('line-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ đường business (API chính)',
    description:
      '🎯 API chính cho tất cả biểu đồ business. Hỗ trợ đầy đủ các loại dữ liệu: ORDER, CUSTOMER, PRODUCT, REVENUE, NEW_CUSTOMERS, RETURNING_CUSTOMERS, TOTAL_CUSTOMERS, AVERAGE_ORDER_VALUE. API tự động tính toán period (hour/day/week/month/year) dựa trên khoảng thời gian begin-end. Nếu không truyền begin thì tự động lấy từ dữ liệu đầu tiên của user. Nếu không truyền end thì mặc định là thời gian hiện tại.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ đường thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/BusinessLineChartResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ',
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực',
  })
  async getLineChart(
    @CurrentUser() user: User,
    @Query() query: BusinessLineChartQueryDto,
  ): Promise<ApiResponseDto<BusinessLineChartResponseDto>> {
    const data = await this.businessReportService.getLineChartData(
      user.id,
      query,
    );
    return ApiResponseDto.success(data, 'Lấy dữ liệu biểu đồ đường thành công');
  }
}
