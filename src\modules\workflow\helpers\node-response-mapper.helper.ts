/**
 * @file Helper để map Node entity + NodeDefinition entity thành NodeResponseDto
 * 
 * K<PERSON><PERSON> hợp dữ liệu từ:
 * - Node entity: chứa values đã cấu hình (parameters, position, disabled, etc.)
 * - NodeDefinition entity: chứa cấu trúc chuẩn (properties, displayName, etc.)
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Node } from '../entities/node.entity';
import { NodeDefinition } from '../entities/node-definition.entity';
import { NodeResponseDto } from '../dto/response/node-response.dto';
import { 
    mapParametersToProperties, 
    getPropertiesErrors, 
    hasPropertiesErrors,
    IMapperOptions 
} from './node-property-mapper.helper';
import { ENodeType } from '../interfaces';

// =================================================================
// SECTION 1: MAIN MAPPER FUNCTION
// =================================================================

/**
 * Map Node entity + NodeDefinition entity thành NodeResponseDto
 * 
 * @param node - Node entity (chứa values đã cấu hình)
 * @param nodeDefinition - NodeDefinition entity (chứa cấu trúc chuẩn)
 * @param options - Tùy chọn mapping
 * @returns NodeResponseDto đã được map đầy đủ
 */
export function mapToNodeResponseDto(
    node: Node,
    nodeDefinition: NodeDefinition,
    options: IMapperOptions = {}
): NodeResponseDto {
    // Xác định node type từ typeName
    const nodeType = nodeDefinition.typeName as ENodeType;
    
    // Map parameters thành properties với values
    const propertiesWithValues = mapParametersToProperties(
        nodeType,
        node.parameters || {},
        nodeDefinition.properties || [],
        {
            validateValues: true,
            useDefaultValues: true,
            mapNestedProperties: true,
            ...options
        }
    );
    
    // Lấy validation errors
    const validationErrors = getPropertiesErrors(propertiesWithValues);
    const hasErrors = hasPropertiesErrors(propertiesWithValues);
    
    // Tạo NodeResponseDto
    const responseDto: NodeResponseDto = {
        // =================================================================
        // NODE INSTANCE DATA (từ Node entity)
        // =================================================================
        id: node.id,
        workflowId: node.workflowId,
        name: node.name,
        position: node.position,
        disabled: node.disabled,
        notes: node.notes,
        notesInFlow: node.notesInFlow,
        retryOnFail: node.retryOnFail,
        maxTries: node.maxTries,
        waitBetweenTries: node.waitBetweenTries,
        onError: node.onError,
        agentId: node.agentId || undefined,
        webhookId: node.webhookId || undefined,
        integrationId: node.integrationId || undefined,
        
        // =================================================================
        // NODE DEFINITION DATA (từ NodeDefinition entity)
        // =================================================================
        nodeDefinitionId: nodeDefinition.id,
        typeName: nodeDefinition.typeName,
        version: nodeDefinition.version,
        displayName: nodeDefinition.displayName,
        description: nodeDefinition.description,
        groupName: nodeDefinition.groupName,
        icon: nodeDefinition.icon,
        properties: propertiesWithValues,
        inputs: nodeDefinition.inputs,
        outputs: nodeDefinition.outputs,
        credentials: nodeDefinition.credentials,
        createdAt: nodeDefinition.createdAt,
        updatedAt: nodeDefinition.updatedAt,
        
        // =================================================================
        // COMPUTED/METADATA FIELDS
        // =================================================================
        rawParameters: node.parameters,
        hasValidationErrors: hasErrors,
        validationErrors: validationErrors.length > 0 ? validationErrors : undefined
    };
    
    return responseDto;
}

// =================================================================
// SECTION 2: BATCH MAPPING FUNCTIONS
// =================================================================

/**
 * Map nhiều nodes cùng lúc
 * 
 * @param nodesWithDefinitions - Array of [Node, NodeDefinition] pairs
 * @param options - Tùy chọn mapping
 * @returns Array of NodeResponseDto
 */
export function mapToNodeResponseDtoArray(
    nodesWithDefinitions: Array<[Node, NodeDefinition]>,
    options: IMapperOptions = {}
): NodeResponseDto[] {
    return nodesWithDefinitions.map(([node, nodeDefinition]) => 
        mapToNodeResponseDto(node, nodeDefinition, options)
    );
}

/**
 * Map nodes với shared node definitions (optimization cho trường hợp nhiều nodes cùng type)
 * 
 * @param nodes - Array of Node entities
 * @param nodeDefinitionsMap - Map từ nodeDefinitionId đến NodeDefinition
 * @param options - Tùy chọn mapping
 * @returns Array of NodeResponseDto
 */
export function mapToNodeResponseDtoWithSharedDefinitions(
    nodes: Node[],
    nodeDefinitionsMap: Map<string, NodeDefinition>,
    options: IMapperOptions = {}
): NodeResponseDto[] {
    return nodes.map(node => {
        const nodeDefinition = nodeDefinitionsMap.get(node.nodeDefinitionId || '');
        if (!nodeDefinition) {
            throw new Error(`NodeDefinition not found for node ${node.id} with definitionId ${node.nodeDefinitionId}`);
        }
        
        return mapToNodeResponseDto(node, nodeDefinition, options);
    });
}

// =================================================================
// SECTION 3: UTILITY FUNCTIONS
// =================================================================

/**
 * Tạo empty NodeResponseDto từ NodeDefinition (cho trường hợp tạo node mới)
 * 
 * @param nodeDefinition - NodeDefinition entity
 * @param partialNode - Partial Node data (id, workflowId, etc.)
 * @returns NodeResponseDto với default values
 */
export function createEmptyNodeResponseDto(
    nodeDefinition: NodeDefinition,
    partialNode: Partial<Node> & { id: string; workflowId: string; name: string }
): NodeResponseDto {
    const emptyNode: Node = {
        id: partialNode.id,
        workflowId: partialNode.workflowId,
        name: partialNode.name,
        position: partialNode.position || { x: 0, y: 0 },
        parameters: {},
        disabled: false,
        notes: partialNode.notes || '',
        notesInFlow: false,
        retryOnFail: false,
        maxTries: 0,
        waitBetweenTries: 1000,
        onError: 'continue',
        agentId: partialNode.agentId || null,
        webhookId: partialNode.webhookId || null,
        integrationId: partialNode.integrationId || null,
        nodeDefinitionId: nodeDefinition.id
    };
    
    return mapToNodeResponseDto(emptyNode, nodeDefinition, {
        useDefaultValues: true,
        validateValues: false
    });
}

/**
 * Validate NodeResponseDto
 * 
 * @param nodeResponseDto - NodeResponseDto để validate
 * @returns Validation result
 */
export function validateNodeResponseDto(nodeResponseDto: NodeResponseDto): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];
    
    // Required fields validation
    if (!nodeResponseDto.id) errors.push('Node ID is required');
    if (!nodeResponseDto.workflowId) errors.push('Workflow ID is required');
    if (!nodeResponseDto.name) errors.push('Node name is required');
    if (!nodeResponseDto.nodeDefinitionId) errors.push('Node definition ID is required');
    if (!nodeResponseDto.typeName) errors.push('Type name is required');
    
    // Properties validation
    if (nodeResponseDto.hasValidationErrors && nodeResponseDto.validationErrors) {
        errors.push(...nodeResponseDto.validationErrors.map(err => 
            `Property ${err.propertyName}: ${err.errorMessage}`
        ));
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Extract parameters từ NodeResponseDto (reverse mapping)
 * 
 * @param nodeResponseDto - NodeResponseDto
 * @returns INodeParameters object
 */
export function extractParametersFromNodeResponseDto(nodeResponseDto: NodeResponseDto): any {
    const parameters: Record<string, any> = {};
    
    nodeResponseDto.properties.forEach(property => {
        if (property.value !== undefined) {
            parameters[property.name] = property.value;
        }
    });
    
    return parameters;
}
