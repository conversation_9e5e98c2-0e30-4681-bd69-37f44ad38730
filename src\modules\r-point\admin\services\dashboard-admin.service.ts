import { Injectable } from '@nestjs/common';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { TransactionStatus } from '@modules/r-point/enums';
import {
  AdminDashboardChartQueryDto,
  AdminChartPeriodType,
  AdminDashboardChartResponseDto,
  AdminChartDataType,
} from '../dto';

/**
 * Service xử lý logic dashboard admin cho R-Point
 */
@Injectable()
export class DashboardAdminService {
  constructor(
    private readonly pointPurchaseTransactionRepository: PointPurchaseTransactionRepository,
  ) {}

  /**
   * Lấy dữ liệu biểu đồ cho admin
   * @param queryDto Tham số truy vấn thời gian
   * @returns Dữ liệu biểu đồ và period
   */
  async getAdminChart(
    queryDto: AdminDashboardChartQueryDto,
  ): Promise<AdminDashboardChartResponseDto> {
    // Xử lý thời gian mặc định
    const now = Math.floor(Date.now() / 1000);
    const endTimestamp = queryDto.end ?? now;

    let beginTimestamp: number;
    if (queryDto.begin) {
      beginTimestamp = queryDto.begin;
    } else {
      // Tìm bản ghi đầu tiên trong hệ thống (hoặc của user cụ thể)
      const queryBuilder = this.pointPurchaseTransactionRepository
        .createQueryBuilder('transaction')
        .select('transaction.completedAt')
        .where('transaction.status = :status', {
          status: TransactionStatus.CONFIRMED,
        })
        .andWhere('transaction.completedAt IS NOT NULL');

      if (queryDto.userId) {
        queryBuilder.andWhere('transaction.userId = :userId', {
          userId: queryDto.userId,
        });
      }

      const firstTransaction = await queryBuilder
        .orderBy('transaction.completedAt', 'ASC')
        .limit(1)
        .getRawOne();

      beginTimestamp = firstTransaction?.completedAt ?? now - 30 * 24 * 60 * 60;
    }

    // Chuyển đổi timestamp thành Date objects để tính toán period
    const beginDate = new Date(beginTimestamp * 1000);
    const endDate = new Date(endTimestamp * 1000);

    // Tính toán period dựa trên khoảng thời gian
    const period = this.calculatePeriod(beginDate, endDate);

    // Lấy dữ liệu từ database
    const chartType = queryDto.type ?? AdminChartDataType.TRANSACTION;
    const groupByClause = this.getGroupByClause(period);
    const selectClause = this.getSelectClause(chartType);

    const queryBuilder = this.pointPurchaseTransactionRepository
      .createQueryBuilder('transaction')
      .select(selectClause, 'value')
      .addSelect(groupByClause, 'period_key')
      .where('transaction.status = :status', {
        status: TransactionStatus.CONFIRMED,
      })
      .andWhere('transaction.completedAt IS NOT NULL')
      .andWhere('transaction.completedAt >= :beginTimestamp', {
        beginTimestamp,
      })
      .andWhere('transaction.completedAt <= :endTimestamp', { endTimestamp });

    // Thêm filter theo userId nếu có
    if (queryDto.userId) {
      queryBuilder.andWhere('transaction.userId = :userId', {
        userId: queryDto.userId,
      });
    }

    const rawResults = await queryBuilder
      .groupBy('period_key')
      .orderBy('period_key', 'ASC')
      .getRawMany();

    // Xử lý dữ liệu thành format mong muốn
    const data = this.processChartData(rawResults, beginDate, endDate, period);

    return {
      data,
      period,
    };
  }

  /**
   * Tính toán period dựa trên khoảng thời gian
   */
  private calculatePeriod(
    beginDate: Date,
    endDate: Date,
  ): AdminChartPeriodType {
    const diffMs = endDate.getTime() - beginDate.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffDays <= 2) {
      return AdminChartPeriodType.HOUR;
    } else if (diffDays <= 7) {
      return AdminChartPeriodType.DAY;
    } else if (diffDays <= 90) {
      return AdminChartPeriodType.WEEK;
    } else if (diffDays <= 730) {
      return AdminChartPeriodType.MONTH;
    } else {
      return AdminChartPeriodType.YEAR;
    }
  }

  /**
   * Tạo SELECT clause dựa trên loại biểu đồ
   */
  private getSelectClause(chartType: AdminChartDataType): string {
    switch (chartType) {
      case AdminChartDataType.TRANSACTION:
        return 'COUNT(*)';
      case AdminChartDataType.AMOUNT:
        return 'SUM(transaction.amount)';
      case AdminChartDataType.POINT:
        return 'SUM(transaction.pointsAmount)';
      case AdminChartDataType.USER:
        return 'COUNT(DISTINCT transaction.userId)';
      default:
        return 'COUNT(*)';
    }
  }

  /**
   * Tạo GROUP BY clause dựa trên period (PostgreSQL syntax)
   */
  private getGroupByClause(period: AdminChartPeriodType): string {
    switch (period) {
      case AdminChartPeriodType.HOUR:
        return "TO_CHAR(TO_TIMESTAMP(transaction.completedAt), 'YYYY-MM-DD HH24:00:00')";
      case AdminChartPeriodType.DAY:
        return "TO_CHAR(TO_TIMESTAMP(transaction.completedAt), 'YYYY-MM-DD')";
      case AdminChartPeriodType.WEEK:
        return "CONCAT(EXTRACT(YEAR FROM TO_TIMESTAMP(transaction.completedAt)), '-W', LPAD(EXTRACT(WEEK FROM TO_TIMESTAMP(transaction.completedAt))::text, 2, '0'))";
      case AdminChartPeriodType.MONTH:
        return "TO_CHAR(TO_TIMESTAMP(transaction.completedAt), 'YYYY-MM')";
      case AdminChartPeriodType.YEAR:
        return "TO_CHAR(TO_TIMESTAMP(transaction.completedAt), 'YYYY')";
      default:
        return "TO_CHAR(TO_TIMESTAMP(transaction.completedAt), 'YYYY-MM-DD')";
    }
  }

  /**
   * Xử lý dữ liệu thô thành format chart
   */
  private processChartData(
    rawResults: any[],
    beginDate: Date,
    endDate: Date,
    period: AdminChartPeriodType,
  ): Record<string, number> {
    const data: Record<string, number> = {};

    // Tạo tất cả các key trong khoảng thời gian với giá trị 0
    const allKeys = this.generateAllKeys(beginDate, endDate, period);
    allKeys.forEach((key) => {
      data[key] = 0;
    });

    // Điền dữ liệu thực tế
    rawResults.forEach((row) => {
      const key = this.formatKey(row.period_key);
      data[key] = parseFloat(row.value) || 0;
    });

    return data;
  }

  /**
   * Tạo tất cả các key trong khoảng thời gian
   */
  private generateAllKeys(
    beginDate: Date,
    endDate: Date,
    period: AdminChartPeriodType,
  ): string[] {
    const keys: string[] = [];
    const current = new Date(beginDate);

    while (current <= endDate) {
      keys.push(this.formatDateToKey(current, period));

      // Tăng current theo period
      switch (period) {
        case AdminChartPeriodType.HOUR:
          current.setHours(current.getHours() + 1);
          break;
        case AdminChartPeriodType.DAY:
          current.setDate(current.getDate() + 1);
          break;
        case AdminChartPeriodType.WEEK:
          current.setDate(current.getDate() + 7);
          break;
        case AdminChartPeriodType.MONTH:
          current.setMonth(current.getMonth() + 1);
          break;
        case AdminChartPeriodType.YEAR:
          current.setFullYear(current.getFullYear() + 1);
          break;
      }
    }

    return keys;
  }

  /**
   * Format date thành key theo period
   */
  private formatDateToKey(date: Date, period: AdminChartPeriodType): string {
    switch (period) {
      case AdminChartPeriodType.HOUR:
        return date.toISOString().substring(0, 13) + ':00:00';
      case AdminChartPeriodType.DAY:
        return date.toISOString().substring(0, 10);
      case AdminChartPeriodType.WEEK:
        const year = date.getFullYear();
        const week = this.getWeekNumber(date);
        return `${year}-W${week.toString().padStart(2, '0')}`;
      case AdminChartPeriodType.MONTH:
        return date.toISOString().substring(0, 7);
      case AdminChartPeriodType.YEAR:
        return date.getFullYear().toString();
      default:
        return date.toISOString().substring(0, 10);
    }
  }

  /**
   * Format key từ database result
   */
  private formatKey(dbKey: string): string {
    return dbKey;
  }

  /**
   * Lấy số tuần trong năm
   */
  private getWeekNumber(date: Date): number {
    const d = new Date(
      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
    );
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
  }
}
