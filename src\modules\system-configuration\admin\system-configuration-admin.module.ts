import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from '../entities/system-configuration.entity';
import { SystemConfigurationRepository } from '../repositories';
import { SystemConfigurationService } from './service';
import { SystemConfigurationAdminController } from './controller/system-configuration-admin.controller';

import { SystemConfigurationEmailOtpService } from './service/system-configuration-email-otp.service';
import { SystemConfigurationOtpManagerService } from './service/system-configuration-otp-manager.service';
import { SystemConfigEncryptionService } from '../services/system-config-encryption.service';
import { JwtModule } from '@nestjs/jwt';

/**
 * Module quản lý các chức năng admin của module system-configuration
 * ServicesModule không cần import vì đã là @Global()
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfiguration]),
    JwtModule,
    // QueueModule không cần import vì đã là @Global()
  ],
  controllers: [SystemConfigurationAdminController],
  providers: [
    SystemConfigurationRepository,
    SystemConfigurationService,
    SystemConfigurationEmailOtpService,
    SystemConfigurationOtpManagerService,
    SystemConfigEncryptionService,
  ],
  exports: [
    SystemConfigurationService,
    SystemConfigurationEmailOtpService,
    SystemConfigurationOtpManagerService,
    SystemConfigEncryptionService,
    SystemConfigurationRepository, // Export repository để các module khác có thể sử dụng
  ],
})
export class SystemConfigurationAdminModule {}
