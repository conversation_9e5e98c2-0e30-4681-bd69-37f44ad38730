import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from '../entities/system-configuration.entity';
import { SystemConfigurationRepository } from '../repositories';
import { SystemConfigurationService } from './service';
import { SystemConfigurationAdminController } from './controller/system-configuration-admin.controller';

import { SystemConfigurationEmailOtpService } from './service/system-configuration-email-otp.service';
import { SystemConfigurationOtpManagerService } from './service/system-configuration-otp-manager.service';
import { SystemConfigEncryptionService } from '../services/system-config-encryption.service';
import { ContractTemplateService } from '../services/contract-template.service';
import { ServicesModule } from '@shared/services/services.module';
import { JwtModule } from '@nestjs/jwt';

/**
 * Module quản lý các chức năng admin của module system-configuration
 * ServicesModule không cần import vì đã là @Global()
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfiguration]),
    JwtModule,
    ServicesModule, // Import để có access đến RedisService và S3Service cho ContractTemplateService
    // QueueModule không cần import vì đã là @Global()
  ],
  controllers: [SystemConfigurationAdminController],
  providers: [
    SystemConfigurationRepository,
    SystemConfigurationService,
    SystemConfigurationEmailOtpService,
    SystemConfigurationOtpManagerService,
    SystemConfigEncryptionService,
    ContractTemplateService, // Di chuyển từ SystemConfigurationModule
  ],
  exports: [
    SystemConfigurationService,
    SystemConfigurationEmailOtpService,
    SystemConfigurationOtpManagerService,
    SystemConfigEncryptionService,
    SystemConfigurationRepository, // Export repository để các module khác có thể sử dụng
    ContractTemplateService, // Export để các module khác có thể sử dụng
  ],
})
export class SystemConfigurationAdminModule {}
