import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các loại period
 */
export enum BusinessChartPeriodType {
  HOUR = 'hour',
  DAY = 'day', 
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year'
}

/**
 * DTO cho response biểu đồ đường business
 * Tương tự như DashboardChartResponseDto từ r-point module
 */
export class BusinessLineChartResponseDto {
  /**
   * Dữ liệu biểu đồ theo key-value
   * Key là mốc thời gian, value là số lượng
   */
  @ApiProperty({
    description: 'Dữ liệu biểu đồ theo key-value',
    example: {
      '2024-01-01': 10,
      '2024-01-02': 15,
      '2024-01-03': 8
    }
  })
  data: Record<string, number>;

  /**
   * Loại period được sử dụng để gom nhóm dữ liệu
   */
  @ApiProperty({
    description: 'Loại period được sử dụng để gom nhóm dữ liệu',
    enum: BusinessChartPeriodType,
    example: BusinessChartPeriodType.DAY
  })
  period: BusinessChartPeriodType;
}
