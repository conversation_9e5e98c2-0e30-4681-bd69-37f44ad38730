import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO response cho workflow execution
 */
export class WorkflowExecutionResponseDto {
  @ApiProperty({
    description: 'ID của execution',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  executionId: string;

  @ApiProperty({
    description: 'ID của workflow được thực thi',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @Expose()
  workflowId: string;

  @ApiProperty({
    description: 'Trạng thái thực thi',
    example: 'running',
    enum: ['running', 'succeeded', 'failed', 'cancelled', 'waiting', 'error'],
  })
  @Expose()
  status: string;

  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Node execution failed: Invalid API key',
  })
  @Expose()
  errorMessage?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu thực thi (timestamp)',
    example: 1640995200000,
  })
  @Expose()
  startedAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc thực thi (timestamp)',
    example: 1640995260000,
  })
  @Expose()
  finishedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian thực thi (milliseconds)',
    example: 60000,
  })
  @Expose()
  duration?: number;
}

/**
 * DTO response cho node execution
 */
export class NodeExecutionResponseDto {
  @ApiProperty({
    description: 'ID của execution',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  result: Record<string, any>;
}
