import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { WorkflowRepository } from '../../repositories';
import { WorkflowValidationHelper } from '../../helpers';
import { WORKFLOW_ERROR_CODES } from '../../exceptions';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowResponseDto,
  QueryWorkflowDto,
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
} from '../../dto';
import { WorkflowDetailResponseDto } from '../../dto/response/workflow-detail-response.dto';
import { NodeRepository } from '../../repositories/node.repository';
import { ConnectionRepository } from '../../repositories/connection.repository';
import { NodeDefinitionRepository } from '../../repositories/node-definition.repository';
import { mapToWorkflowDetailResponseDto } from '../../helpers/workflow-detail-mapper.helper';
import { In } from 'typeorm';

/**
 * Service xử lý business logic cho Workflow của User
 */
@Injectable()
export class WorkflowUserService {
  private readonly logger = new Logger(WorkflowUserService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly validationHelper: WorkflowValidationHelper,
    private readonly nodeRepository: NodeRepository,
    private readonly connectionRepository: ConnectionRepository,
    private readonly nodeDefinitionRepository: NodeDefinitionRepository,
  ) {}

  /**
   * Lấy danh sách workflows của user với phân trang và filter
   * @param userId ID của user
   * @param queryDto Query parameters
   * @returns Danh sách workflows với pagination
   */
  async getWorkflows(
    userId: number,
    queryDto: QueryWorkflowDto,
  ): Promise<PaginatedResult<WorkflowResponseDto>> {
    this.logger.log(`Lấy danh sách workflows của user: ${userId}`);

    // Validate input
    this.validationHelper.validateUserId(userId);
    this.validationHelper.validatePaginationParams(queryDto.page, queryDto.limit);

    try {
      const { workflows, total } = await this.workflowRepository.findUserWorkflows(userId, {
        page: queryDto.page,
        limit: queryDto.limit,
        search: queryDto.search,
        isActive: queryDto.isActive,
        sortBy: queryDto.sortBy,
        sortDirection: queryDto.sortDirection,
      });

      const workflowDtos = workflows.map(workflow =>
        plainToInstance(WorkflowResponseDto, workflow, { excludeExtraneousValues: true })
      );

      return {
        items: workflowDtos,
        meta: {
          totalItems: total,
          itemCount: workflowDtos.length,
          itemsPerPage: queryDto.limit || 10,
          totalPages: Math.ceil(total / (queryDto.limit || 10)),
          currentPage: queryDto.page || 1,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách workflows của user ${userId}:`, error);
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_FETCH_ERROR,
        'Lỗi khi lấy danh sách workflows'
      );
    }
  }

  /**
   * Lấy workflow theo ID với đầy đủ thông tin (nodes + connections)
   * @param userId ID của user
   * @param workflowId ID của workflow
   * @returns Workflow details với nodes và connections
   */
  async getWorkflowById(userId: number, workflowId: string): Promise<WorkflowDetailResponseDto> {
    this.logger.log(`Lấy workflow detail ${workflowId} của user: ${userId}`);

    // Validate input
    this.validationHelper.validateUserId(userId);
    this.validationHelper.validateWorkflowIds([workflowId]);

    try {
      // 1. Lấy workflow và validate ownership
      const workflow = await this.workflowRepository.findWorkflowByIdAndUserId(workflowId, userId);
      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      await this.workflowRepository.update(workflowId, { isActive: false });

      this.validationHelper.validateUserOwnership(workflow, userId);

      // 2. Lấy tất cả nodes trong workflow
      const nodes = await this.nodeRepository.findByWorkflowId(workflowId);

      // 3. Lấy tất cả connections trong workflow
      const connections = await this.connectionRepository.findByWorkflowId(workflowId);

      // 4. Lấy unique node definition IDs (filter out null values)
      const nodeDefinitionIds = [...new Set(nodes.map(node => node.nodeDefinitionId).filter(Boolean))] as string[];

      // 5. Lấy tất cả node definitions cần thiết
      let nodeDefinitionsMap = new Map();
      if (nodeDefinitionIds.length > 0) {
        const nodeDefinitions = await this.nodeDefinitionRepository.find({
          where: { id: In(nodeDefinitionIds) }
        });
        nodeDefinitionsMap = new Map(nodeDefinitions.map(def => [def.id, def]));
      }

      // 6. Map thành WorkflowDetailResponseDto
      return mapToWorkflowDetailResponseDto(workflow, nodes, nodeDefinitionsMap, connections);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy workflow detail ${workflowId} của user ${userId}:`, error);
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_FETCH_ERROR,
        'Lỗi khi lấy thông tin workflow detail'
      );
    }
  }

  /**
   * Tạo workflow mới
   * @param userId ID của user
   * @param createDto Dữ liệu tạo workflow
   * @returns Workflow đã tạo
   */
  async createWorkflow(userId: number, createDto: CreateWorkflowDto): Promise<WorkflowResponseDto> {
    this.logger.log(`Tạo workflow mới cho user: ${userId}`);

    // Validate input
    this.validationHelper.validateCreateWorkflowData(createDto, userId);

    try {
      const workflowData = {
        name: createDto.name,
        isActive: false,
        settings: createDto.settings || undefined,
        userId: userId,
        employeeId: null,
      };

      const workflow = await this.workflowRepository.createWorkflow(workflowData);

      return plainToInstance(WorkflowResponseDto, workflow, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo workflow cho user ${userId}:`, error);
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_CREATION_ERROR,
        'Lỗi khi tạo workflow'
      );
    }
  }

  /**
   * Cập nhật workflow
   * @param userId ID của user
   * @param workflowId ID của workflow
   * @param updateDto Dữ liệu cập nhật
   * @returns Workflow đã cập nhật
   */
  async updateWorkflow(
    userId: number,
    workflowId: string,
    updateDto: UpdateWorkflowDto,
  ): Promise<WorkflowResponseDto> {
    this.logger.log(`Cập nhật workflow ${workflowId} của user: ${userId}`);

    // Validate input
    this.validationHelper.validateUserId(userId);
    this.validationHelper.validateWorkflowIds([workflowId]);
    this.validationHelper.validateUpdateWorkflowData(updateDto);

    try {
      // Check ownership
      const existingWorkflow = await this.workflowRepository.findWorkflowByIdAndUserId(workflowId, userId);
      this.validationHelper.validateUserOwnership(existingWorkflow, userId);

      // Update workflow - convert null to undefined for TypeORM compatibility
      const updateData = {
        ...updateDto,
        settings: updateDto.settings === null ? undefined : updateDto.settings,
      };
      const updatedWorkflow = await this.workflowRepository.updateWorkflow(workflowId, updateData);

      if (!updatedWorkflow) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_ERROR,
          'Không thể cập nhật workflow'
        );
      }

      return plainToInstance(WorkflowResponseDto, updatedWorkflow, { excludeExtraneousValues: true });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi cập nhật workflow ${workflowId} của user ${userId}:`, error);
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_ERROR,
        'Lỗi khi cập nhật workflow'
      );
    }
  }

  /**
   * Bulk delete workflows
   * @param userId ID của user
   * @param bulkDeleteDto Danh sách IDs cần xóa
   * @returns Kết quả bulk delete
   */
  async bulkDeleteWorkflows(
    userId: number,
    bulkDeleteDto: BulkDeleteWorkflowDto,
  ): Promise<BulkDeleteWorkflowResponseDto> {
    this.logger.log(`Bulk delete workflows của user ${userId}: ${bulkDeleteDto.ids.join(', ')}`);

    // Validate input
    this.validationHelper.validateUserId(userId);
    this.validationHelper.validateWorkflowIds(bulkDeleteDto.ids);

    try {
      // Validate ownership for all workflows
      const validIds = await this.workflowRepository.validateUserWorkflowIds(bulkDeleteDto.ids, userId);
      const invalidIds = bulkDeleteDto.ids.filter(id => !validIds.includes(id));

      // If no workflows can be deleted, throw ownership error
      if (validIds.length === 0) {
        this.logger.warn(`User ${userId} không thể xóa bất kỳ workflow nào trong danh sách: ${bulkDeleteDto.ids.join(', ')}`);
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_OWNERSHIP_ERROR,
          'Workflow không thuộc về bạn'
        );
      }

      const errors: Record<string, string> = {};
      invalidIds.forEach(id => {
        errors[id] = 'Workflow không tồn tại hoặc không thuộc về bạn';
      });

      let cascadeStats = {
        totalConnectionsDeleted: 0,
        totalNodesDeleted: 0,
        totalExecutionsDeleted: 0,
      };

      let deletedIds: string[] = [];

      // Perform cascade delete for valid workflows
      const cascadeResult = await this.workflowRepository.bulkCascadeDeleteWorkflows(validIds);

      cascadeStats = {
        totalConnectionsDeleted: cascadeResult.totalConnectionsDeleted,
        totalNodesDeleted: cascadeResult.totalNodesDeleted,
        totalExecutionsDeleted: cascadeResult.totalExecutionsDeleted,
      };

      deletedIds = validIds;

      // If some workflows failed to delete, throw partial failure error
      if (invalidIds.length > 0) {
        this.logger.warn(`User ${userId} chỉ xóa được ${deletedIds.length}/${bulkDeleteDto.ids.length} workflows`);
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_BULK_DELETE_ERROR,
          `Chỉ xóa được ${deletedIds.length}/${bulkDeleteDto.ids.length} workflows. Một số workflow không thuộc về bạn.`
        );
      }

      // All workflows deleted successfully
      return {
        totalRequested: bulkDeleteDto.ids.length,
        deletedCount: deletedIds.length,
        failedCount: invalidIds.length,
        deletedIds,
        failedIds: invalidIds,
        errors,
        cascadeStats,
      };
    } catch (error) {
      // Re-throw AppException as-is
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi bulk delete workflows của user ${userId}:`, error);
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_BULK_DELETE_ERROR,
        'Lỗi khi xóa workflows'
      );
    }
  }
}
