 import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as services from './services';
import * as controllers from './controllers';
import { User } from '@modules/user/entities';
import { SystemConfigurationModule } from '@modules/system-configuration';
import { RedisService } from '@shared/services/redis.service';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { CdnService } from '@shared/services/cdn.service';
import { Bank } from '@modules/user/entities/bank.entity';
import { AuthModule } from '@modules/auth/auth.module';
// Tạm thời comment out để tránh circular dependency
// import { SubscriptionWebhookService } from '@modules/subscription/user/services/subscription-webhook.service';

/**
 * Module quản lý R-Point cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), User, Bank]),
    forwardRef(() => SystemConfigurationModule), // Import SystemConfigurationModule để sử dụng SystemConfigurationService
    AuthModule, // Import AuthModule để sử dụng JwtUtilService trong RPointSseGuard
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
    RedisService,
    BankRepository,
    CdnService,
    // Tạm thời comment out để tránh circular dependency
    // SubscriptionWebhookService,
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class RPointUserModule {}
