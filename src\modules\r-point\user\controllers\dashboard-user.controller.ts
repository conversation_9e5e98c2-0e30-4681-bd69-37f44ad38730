import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { DashboardUserService } from '../services/dashboard-user.service';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import {
  DashboardChartQueryDto,
  DashboardChartResponseDto,
  ChartPeriodType,
  ChartDataType,
} from '../dto';
import { ApiResponseDto } from '@/common/response';
import { JwtUserGuard } from '@modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.RPOINT_DASHBOARD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/r-point/dashboard')
export class DashboardUserController {
  constructor(private readonly dashboardUserService: DashboardUserService) {}

  /**
   * L<PERSON>y dữ liệu biểu đồ số lượng mua hàng theo thời gian
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn thời gian
   * @returns Dữ liệu biểu đồ và period
   */
  @Get('purchase-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ số lượng mua hàng theo thời gian',
    description:
      'API tự động tính toán period (hour/day/week/month/year) dựa trên khoảng thời gian begin-end. Nếu không truyền begin thì tự động lấy từ giao dịch đầu tiên của user. Nếu không truyền end thì mặc định là thời gian hiện tại.',
  })
  @ApiQuery({
    name: 'begin',
    required: false,
    description:
      'Thời gian bắt đầu (Unix timestamp seconds). Mặc định: giao dịch đầu tiên của user',
    type: Number,
    example: 1704067200,
  })
  @ApiQuery({
    name: 'end',
    required: false,
    description:
      'Thời gian kết thúc (Unix timestamp seconds). Mặc định: hiện tại',
    type: Number,
    example: 1735689599,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description:
      'Loại dữ liệu biểu đồ: TRANSACTION (số lượng giao dịch), AMOUNT (tổng tiền), POINT (tổng điểm)',
    enum: ChartDataType,
    example: ChartDataType.TRANSACTION,
  })
  @ApiResponse({
    status: 200,
    description: 'Dữ liệu biểu đồ số lượng mua hàng',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy dữ liệu biểu đồ thành công' },
        result: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              description: 'Dữ liệu biểu đồ theo key-value',
              example: {
                '2024-01-01': 10,
                '2024-01-02': 15,
                '2024-01-03': 8,
                '2024-01-04': 12,
              },
            },
            period: {
              type: 'string',
              enum: Object.values(ChartPeriodType),
              description: 'Loại period được sử dụng để gom nhóm dữ liệu',
              example: ChartPeriodType.DAY,
            },
          },
        },
      },
    },
  })
  async getPurchaseChart(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: DashboardChartQueryDto,
  ): Promise<ApiResponseDto<DashboardChartResponseDto>> {
    const chartData = await this.dashboardUserService.getPurchaseChart(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(chartData, 'Lấy dữ liệu biểu đồ thành công');
  }
}
