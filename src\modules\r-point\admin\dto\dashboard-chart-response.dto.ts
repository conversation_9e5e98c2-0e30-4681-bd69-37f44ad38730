import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các loại period admin
 */
export enum AdminChartPeriodType {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

/**
 * DTO cho response biểu đồ dashboard admin
 */
export class AdminDashboardChartResponseDto {
  /**
   * Dữ liệu biểu đồ theo key-value
   * Key là mốc thời gian, value là giá trị
   */
  @ApiProperty({
    description: 'Dữ liệu biểu đồ theo key-value',
    example: {
      '2024-01-01': 150,
      '2024-01-02': 200,
      '2024-01-03': 180,
    },
  })
  data: Record<string, number>;

  /**
   * Loại period được sử dụng để gom nhóm dữ liệu
   */
  @ApiProperty({
    description: 'Loại period được sử dụng để gom nhóm dữ liệu',
    enum: AdminChartPeriodType,
    example: AdminChartPeriodType.DAY,
  })
  period: AdminChartPeriodType;
}
