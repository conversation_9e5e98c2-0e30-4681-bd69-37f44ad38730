import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ShippingStatusEnum } from '@modules/business/enums';

/**
 * Enum cho loại dữ liệu biểu đồ đơn hàng
 */
export enum OrdersChartDataType {
  ORDERS_COUNT = 'ORDERS_COUNT',
  TOTAL_AMOUNT = 'TOTAL_AMOUNT',
  AVERAGE_ORDER_VALUE = 'AVERAGE_ORDER_VALUE',
}

/**
 * DTO cho query parameters của API biểu đồ đơn hàng
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class OrdersChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds hoặc milliseconds - tự động detect)
   */
  @ApiProperty({
    description:
      'Thời gian bắt đầu (Unix timestamp seconds hoặc milliseconds - tự động detect)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds hoặc milliseconds - tự động detect)
   */
  @ApiProperty({
    description:
      'Thời gian kết thúc (Unix timestamp seconds hoặc milliseconds - tự động detect)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: OrdersChartDataType,
    example: OrdersChartDataType.ORDERS_COUNT,
    required: false,
    default: OrdersChartDataType.ORDERS_COUNT,
  })
  @IsOptional()
  @IsEnum(OrdersChartDataType)
  type?: OrdersChartDataType;

  /**
   * Lọc theo trạng thái vận chuyển
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.DELIVERED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  status?: ShippingStatusEnum;
}
