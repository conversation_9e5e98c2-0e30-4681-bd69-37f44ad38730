import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO response cho connection
 */
export class ConnectionResponseDto {
  @ApiProperty({
    description: 'ID của connection',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của source node',
    example: 'uuid-source-node-id',
  })
  sourceNodeId: string;

  @ApiProperty({
    description: 'ID của target node',
    example: 'uuid-target-node-id',
  })
  targetNodeId: string;

  @ApiProperty({
    description: 'Tên của output handle từ source node',
    example: 'main',
  })
  sourceHandle: string;

  @ApiProperty({
    description: 'Index của source handle',
    example: 0,
  })
  sourceHandleIndex: number;

  @ApiProperty({
    description: 'Tên của input handle từ target node',
    example: 'main',
  })
  targetHandle: string;

  @ApiProperty({
    description: 'Index của target handle',
    example: 0,
  })
  targetHandleIndex: number;
}
