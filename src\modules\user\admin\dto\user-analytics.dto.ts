import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsString,
  IsBoolean,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { AuthMethodEnum } from '../../enums/auth-method.enum';
import { AuthStatusEnum } from '../../enums/auth-status.enum';
import { AuthLogMethodEnum } from '../../enums/auth-log-method.enum';

/**
 * Enum định nghĩa các platform
 */
export enum PlatformEnum {
  /**
   * Web browser
   */
  WEB = 'web',

  /**
   * Mobile app
   */
  MOBILE = 'mobile',

  /**
   * Desktop app
   */
  DESKTOP = 'desktop',

  /**
   * API/Other
   */
  API = 'api',
}

/**
 * DTO cho query thống kê lịch sử đăng nhập
 */
export class LoginHistoryQueryDto extends QueryDto {
  @ApiProperty({
    description: 'ID người dùng (optional)',
    required: false,
    example: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  userId?: number;

  @ApiProperty({
    description: 'Ngày bắt đầu (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Phương thức xác thực',
    enum: AuthMethodEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthMethodEnum)
  authMethod?: AuthMethodEnum;

  @ApiProperty({
    description: 'Trạng thái xác thực',
    enum: AuthStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthStatusEnum)
  status?: AuthStatusEnum;

  @ApiProperty({
    description: 'Platform (web, mobile, desktop, api)',
    enum: PlatformEnum,
    required: false,
    example: PlatformEnum.WEB,
  })
  @IsOptional()
  @IsEnum(PlatformEnum)
  platform?: PlatformEnum;

  @ApiProperty({
    description: 'Chỉ lấy đăng nhập thành công (true) hoặc thất bại (false)',
    required: false,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isSuccessful?: boolean;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    example: 'verifiedAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  declare sortBy?: string;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  declare sortDirection?: SortDirection;

  @ApiProperty({
    description: 'Số lượng bản ghi mỗi trang',
    required: false,
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  declare limit: number;

  constructor() {
    super();
    this.limit = 20;
  }
}

/**
 * DTO cho query thống kê thiết bị
 */
export class DeviceAnalyticsQueryDto extends QueryDto {
  @ApiProperty({
    description: 'ID người dùng (optional)',
    required: false,
    example: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  userId?: number;

  @ApiProperty({
    description: 'Ngày bắt đầu (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Chỉ thiết bị đáng tin cậy',
    required: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  trustedOnly?: boolean = false;

  @ApiProperty({
    description: 'Số lượng bản ghi mỗi trang',
    required: false,
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  declare limit: number;

  constructor() {
    super();
    this.limit = 20;
  }
}

/**
 * DTO cho response lịch sử đăng nhập
 */
export class LoginHistoryResponseDto {
  @ApiProperty({ description: 'ID log' })
  id: number;

  @ApiProperty({ description: 'ID người dùng' })
  userId: number;

  @ApiProperty({ description: 'Tên người dùng' })
  userName: string;

  @ApiProperty({ description: 'Email người dùng' })
  userEmail: string;

  @ApiProperty({ description: 'Phương thức xác thực', enum: AuthLogMethodEnum })
  authMethod: AuthLogMethodEnum;

  @ApiProperty({ description: 'Trạng thái xác thực', enum: AuthStatusEnum })
  status: AuthStatusEnum;

  @ApiProperty({ description: 'Địa chỉ IP' })
  ipAddress: string;

  @ApiProperty({ description: 'User agent' })
  userAgent: string;

  @ApiProperty({ description: 'Thời gian gửi mã' })
  codeSentAt: number;

  @ApiProperty({ description: 'Thời gian xác thực' })
  verifiedAt: number;

  @ApiProperty({ description: 'Số lần thử' })
  attemptCount: number;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: number;
}

/**
 * DTO cho response thông tin thiết bị
 */
export class DeviceInfoResponseDto {
  @ApiProperty({ description: 'ID thiết bị' })
  id: string;

  @ApiProperty({ description: 'ID người dùng' })
  userId: number;

  @ApiProperty({ description: 'Tên người dùng' })
  userName: string;

  @ApiProperty({ description: 'Email người dùng' })
  userEmail: string;

  @ApiProperty({ description: 'Fingerprint thiết bị' })
  fingerprint: string;

  @ApiProperty({ description: 'Địa chỉ IP' })
  ipAddress: string;

  @ApiProperty({ description: 'User agent' })
  userAgent: string;

  @ApiProperty({ description: 'Trình duyệt' })
  browser: string;

  @ApiProperty({ description: 'Hệ điều hành' })
  operatingSystem: string;

  @ApiProperty({ description: 'Thiết bị đáng tin cậy' })
  isTrusted: boolean;

  @ApiProperty({ description: 'Thời gian đăng nhập gần nhất' })
  lastLogin: number;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: number;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedAt: number;
}

/**
 * DTO cho thống kê tổng quan
 */
export class UserAnalyticsOverviewDto {
  @ApiProperty({ description: 'Tổng số thiết bị' })
  totalDevices: number;

  @ApiProperty({ description: 'Số thiết bị đáng tin cậy' })
  trustedDevices: number;

  @ApiProperty({ description: 'Số thiết bị không đáng tin cậy' })
  untrustedDevices: number;

  @ApiProperty({ description: 'Tổng số lượt đăng nhập' })
  totalLogins: number;

  @ApiProperty({ description: 'Số lượt đăng nhập thành công' })
  successfulLogins: number;

  @ApiProperty({ description: 'Số lượt đăng nhập thất bại' })
  failedLogins: number;

  @ApiProperty({ description: 'Tỷ lệ đăng nhập thành công (%)' })
  successRate: number;

  @ApiProperty({ description: 'Số người dùng hoạt động trong 24h qua' })
  activeUsersLast24h: number;

  @ApiProperty({ description: 'Số người dùng hoạt động trong 7 ngày qua' })
  activeUsersLast7d: number;

  @ApiProperty({ description: 'Số người dùng hoạt động trong 30 ngày qua' })
  activeUsersLast30d: number;
}

/**
 * Enum cho đơn vị thời gian trong biểu đồ
 */
export enum TimeUnit {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

/**
 * DTO cho query parameters của biểu đồ đăng nhập
 */
export class LoginChartQueryDto {
  @ApiProperty({
    description: 'Ngày bắt đầu (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm:ss)',
    example: '2024-01-01',
  })
  @IsNotEmpty()
  @IsString()
  startDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm:ss)',
    example: '2024-01-31',
  })
  @IsNotEmpty()
  @IsString()
  endDate: string;

  @ApiProperty({
    description: 'Đơn vị thời gian (tự động tính nếu không cung cấp)',
    enum: TimeUnit,
    required: false,
  })
  @IsOptional()
  @IsEnum(TimeUnit)
  timeUnit?: TimeUnit;
}

/**
 * DTO cho dữ liệu biểu đồ đăng nhập theo thời gian
 */
export class LoginTimeSeriesDataDto {
  @ApiProperty({ description: 'Nhãn thời gian (format tùy theo đơn vị)' })
  label: string;

  @ApiProperty({
    description: 'Giá trị timestamp bắt đầu của khoảng thời gian',
  })
  timestamp: number;

  @ApiProperty({ description: 'Số lượt đăng nhập' })
  loginCount: number;

  @ApiProperty({ description: 'Số lượt đăng nhập thành công' })
  successCount: number;

  @ApiProperty({ description: 'Số lượt đăng nhập thất bại' })
  failedCount: number;

  @ApiProperty({ description: 'Số người dùng duy nhất' })
  uniqueUsers: number;
}

/**
 * DTO cho response của biểu đồ đăng nhập
 */
export class LoginChartResponseDto {
  @ApiProperty({ description: 'Đơn vị thời gian được sử dụng', enum: TimeUnit })
  timeUnit: TimeUnit;

  @ApiProperty({
    description: 'Dữ liệu biểu đồ',
    type: [LoginTimeSeriesDataDto],
  })
  data: LoginTimeSeriesDataDto[];

  @ApiProperty({ description: 'Tổng số lượt đăng nhập trong khoảng thời gian' })
  totalLogins: number;

  @ApiProperty({ description: 'Tổng số lượt đăng nhập thành công' })
  totalSuccessLogins: number;

  @ApiProperty({ description: 'Tổng số lượt đăng nhập thất bại' })
  totalFailedLogins: number;

  @ApiProperty({ description: 'Tổng số người dùng duy nhất' })
  totalUniqueUsers: number;
}

/**
 * DTO cho dữ liệu biểu đồ thiết bị theo thời gian
 */
export class DeviceTimeSeriesDataDto {
  @ApiProperty({ description: 'Ngày (YYYY-MM-DD)' })
  date: string;

  @ApiProperty({ description: 'Số thiết bị mới' })
  newDevices: number;

  @ApiProperty({ description: 'Tổng số thiết bị' })
  totalDevices: number;

  @ApiProperty({ description: 'Số thiết bị đáng tin cậy mới' })
  newTrustedDevices: number;
}

/**
 * DTO cho thống kê theo phương thức xác thực
 */
export class AuthMethodStatsDto {
  @ApiProperty({ description: 'Phương thức xác thực', enum: AuthMethodEnum })
  method: AuthMethodEnum;

  @ApiProperty({ description: 'Số lượt sử dụng' })
  count: number;

  @ApiProperty({ description: 'Tỷ lệ thành công (%)' })
  successRate: number;
}

/**
 * DTO cho thống kê theo trình duyệt
 */
export class BrowserStatsDto {
  @ApiProperty({ description: 'Tên trình duyệt' })
  browser: string;

  @ApiProperty({ description: 'Số thiết bị' })
  deviceCount: number;

  @ApiProperty({ description: 'Tỷ lệ (%)' })
  percentage: number;
}

/**
 * DTO cho thống kê theo hệ điều hành
 */
export class OperatingSystemStatsDto {
  @ApiProperty({ description: 'Tên hệ điều hành' })
  operatingSystem: string;

  @ApiProperty({ description: 'Số thiết bị' })
  deviceCount: number;

  @ApiProperty({ description: 'Tỷ lệ (%)' })
  percentage: number;
}

/**
 * DTO cho response thống kê loại tài khoản người dùng (biểu đồ tròn)
 */
export class UserTypeStatsDto {
  @ApiProperty({
    description: 'Loại tài khoản (INDIVIDUAL hoặc BUSINESS)',
    example: 'INDIVIDUAL',
  })
  userType: string;

  @ApiProperty({
    description: 'Số lượng người dùng',
    example: 1250,
  })
  count: number;

  @ApiProperty({
    description: 'Tỷ lệ phần trăm',
    example: 75.5,
  })
  percentage: number;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Cá nhân',
  })
  label: string;
}
