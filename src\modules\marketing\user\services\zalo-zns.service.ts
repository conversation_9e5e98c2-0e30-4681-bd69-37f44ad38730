import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ILike } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { ZaloZnsService as SharedZaloZnsService } from '@shared/services/zalo/zalo-zns.service';
import { ZaloZnsInfoService } from '@shared/services/zalo/zalo-zns-info.service';
import { ZaloService } from './zalo.service';
import { ZaloTokenService } from './zalo-token.service';
import { ZaloZnsTemplate, ZaloZnsMessage } from '../entities';
import {
  ZnsTemplateQueryDto,
  ZnsMessageQueryDto,
  RegisterZnsTemplateDto,
  CreateZnsTemplateDraftDto,
  SendZnsMessageDto,
  UploadZnsImageDto,
  UploadZnsImageResponseDto,
  ZnsImageQueryDto,
  ZnsImageListResponseDto,
  ZnsTemplateTagResponseDto,
  ZnsRatingQueryDto,
  ZnsRatingResponseDto,
  ZnsRatingDetailDto,
} from '../dto/zalo';
import {
  ZaloZnsTemplateRepository,
  ZaloZnsImageRepository,
} from '../repositories';
import { ZaloWebhookTemplateStatusChangeDto } from '@shared/services/zalo/dto/zalo-webhook-template-status.dto';
import { ZaloOAIntegrationService } from '@modules/integration/services/zalo-oa-integration.service';
import { ZaloOAAdapterService } from '@modules/integration/services/zalo-oa-adapter.service';
import { ZaloOAMetadata } from '@modules/integration/interfaces/zalo-oa-metadata.interface';

/**
 * Service xử lý các chức năng liên quan đến Zalo ZNS (Zalo Notification Service)
 * Kết hợp giữa việc gọi Zalo API và quản lý dữ liệu trong database
 */
@Injectable()
export class ZaloZnsService {
  private readonly logger = new Logger(ZaloZnsService.name);

  constructor(
    private readonly zaloZnsTemplateRepository: ZaloZnsTemplateRepository,
    private readonly zaloZnsImageRepository: ZaloZnsImageRepository,
    @InjectRepository(ZaloZnsMessage)
    private readonly zaloZnsMessageRepository: Repository<ZaloZnsMessage>,
    private readonly sharedZaloZnsService: SharedZaloZnsService,
    private readonly zaloZnsInfoService: ZaloZnsInfoService,
    private readonly zaloService: ZaloService,
    private readonly zaloTokenService: ZaloTokenService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly zaloOAIntegrationService: ZaloOAIntegrationService,
    private readonly zaloOAAdapterService: ZaloOAAdapterService,
  ) {}

  /**
   * Helper method để lấy oaId từ integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns oaId từ metadata của Integration
   */
  private async getOaIdFromIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<string> {
    try {
      const integration =
        await this.zaloOAIntegrationService.getZaloOAIntegrationById(
          integrationId,
        );

      // Kiểm tra quyền truy cập
      if (integration.userId !== userId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_OA_ACCESS_DENIED,
          'Không có quyền truy cập Integration này',
        );
      }

      // Lấy oaId từ metadata
      const metadata = integration.metadata as ZaloOAMetadata;
      if (!metadata?.oaId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
          'Không tìm thấy oaId trong metadata của Integration',
        );
      }

      return metadata.oaId;
    } catch (error) {
      this.logger.error(
        `Failed to get oaId from integrationId ${integrationId}: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_OA_NOT_FOUND,
        'Không tìm thấy Integration hoặc Integration không hợp lệ',
      );
    }
  }

  /**
   * Lấy tất cả ZNS templates từ database
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tất cả template ZNS với phân trang
   */
  async getAllZnsTemplates(
    userId: number,
    queryDto: ZnsTemplateQueryDto,
  ): Promise<PaginatedResult<ZaloZnsTemplate>> {
    // Nếu có integrationId, lấy oaId từ integrationId và filter theo oaId đó
    if (queryDto.integrationId) {
      const oaId = await this.getOaIdFromIntegrationId(
        userId,
        queryDto.integrationId,
      );
      return this.getZnsTemplates(userId, oaId, queryDto);
    }

    // Nếu không có integrationId, lấy tất cả templates
    return this.getZnsTemplates(userId, undefined, queryDto);
  }

  /**
   * Lấy danh sách ZNS templates từ database bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS với phân trang
   */
  async getZnsTemplatesByIntegrationId(
    userId: number,
    integrationId: string,
    queryDto: ZnsTemplateQueryDto,
  ): Promise<PaginatedResult<ZaloZnsTemplate>> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsTemplates(userId, oaId, queryDto);
  }

  /**
   * Lấy danh sách template ZNS từ Zalo API bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS từ Zalo API với phân trang
   */
  async getZnsTemplatesFromZaloApiByIntegrationId(
    userId: number,
    integrationId: string,
    queryDto: ZnsTemplateQueryDto,
  ): Promise<PaginatedResult<any>> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsTemplatesFromZaloApi(userId, oaId, queryDto);
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param templateId ID của template
   * @returns Thông tin chi tiết template ZNS
   */
  async getZnsTemplateDetailByIntegrationId(
    userId: number,
    integrationId: string,
    templateId: number,
  ): Promise<ZaloZnsTemplate> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsTemplateDetail(userId, oaId, templateId);
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database (không cần integrationId)
   * @param userId ID của người dùng
   * @param templateId ID của template
   * @returns Thông tin chi tiết template ZNS
   */
  async getZnsTemplateDetailById(
    userId: number,
    templateId: number,
  ): Promise<ZaloZnsTemplate> {
    try {
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId, userId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to get ZNS template detail by ID: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED,
        'Không thể lấy thông tin chi tiết template ZNS',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ Zalo API bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param templateId ID của template từ Zalo
   * @returns Thông tin chi tiết template ZNS từ Zalo API
   */
  async getZnsTemplateDetailFromZaloApiByIntegrationId(
    userId: number,
    integrationId: string,
    templateId: string,
  ): Promise<any> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsTemplateDetailFromZaloApi(userId, oaId, templateId);
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS từ Zalo API bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param templateId ID của template từ Zalo
   * @returns Dữ liệu mẫu của template ZNS
   */
  async getZnsTemplateSampleDataByIntegrationId(
    userId: number,
    integrationId: string,
    templateId: string,
  ): Promise<any> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsTemplateSampleData(userId, oaId, templateId);
  }

  /**
   * Đồng bộ template ZNS từ Zalo API sang database bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Kết quả đồng bộ
   */
  async syncTemplatesFromZaloApiByIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<any> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.syncTemplatesFromZaloApi(userId, oaId);
  }

  /**
   * Kiểm tra trạng thái ZNS của Official Account bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Trạng thái ZNS của Official Account
   */
  async getZnsStatusByIntegrationId(
    userId: number,
    integrationId: string,
    messageId: string,
  ): Promise<any> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsStatus(userId, oaId, messageId);
  }

  /**
   * Lấy thông tin quota ZNS của Official Account bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Thông tin quota ZNS
   */
  async getZnsQuotaByIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<any> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsQuota(userId, oaId);
  }

  /**
   * Đăng ký template ZNS bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param registerDto Dữ liệu đăng ký template
   * @returns Template ZNS đã đăng ký
   */
  async registerZnsTemplateByIntegrationId(
    userId: number,
    integrationId: string,
    registerDto: RegisterZnsTemplateDto,
  ): Promise<ZaloZnsTemplate> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.registerZnsTemplate(userId, oaId, registerDto);
  }

  /**
   * Tạo template ZNS draft chỉ vào database (không đăng ký lên Zalo) bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param createDto Dữ liệu tạo template draft
   * @returns Template ZNS draft đã tạo
   */
  async createZnsTemplateDraftByIntegrationId(
    userId: number,
    integrationId: string,
    createDto: CreateZnsTemplateDraftDto,
  ): Promise<ZaloZnsTemplate> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.createZnsTemplateDraft(userId, oaId, createDto);
  }

  /**
   * Chỉnh sửa template ZNS bằng integrationId (chỉ cho template có trạng thái REJECT)
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param templateId ID của template trong database
   * @param editDto Dữ liệu chỉnh sửa template
   * @returns Template ZNS đã chỉnh sửa
   */
  async editZnsTemplateByIntegrationId(
    userId: number,
    integrationId: string,
    templateId: number,
    editDto: RegisterZnsTemplateDto,
  ): Promise<ZaloZnsTemplate> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.editZnsTemplate(userId, oaId, templateId, editDto);
  }

  /**
   * Cập nhật trạng thái template ZNS bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param id ID của template
   * @param status Trạng thái mới
   * @returns Template ZNS đã cập nhật
   */
  async updateZnsTemplateStatusByIntegrationId(
    userId: number,
    integrationId: string,
    id: number,
    status: string,
  ): Promise<ZaloZnsTemplate> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.updateZnsTemplateStatus(userId, oaId, id, status);
  }

  /**
   * Gửi tin nhắn ZNS bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param sendDto Dữ liệu gửi tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendZnsMessageByIntegrationId(
    userId: number,
    integrationId: string,
    sendDto: SendZnsMessageDto,
  ): Promise<ZaloZnsMessage> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.sendZnsMessage(userId, oaId, sendDto);
  }

  /**
   * Lấy lịch sử tin nhắn ZNS bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn ZNS với phân trang
   */
  async getZnsMessagesByIntegrationId(
    userId: number,
    integrationId: string,
    queryDto: ZnsMessageQueryDto,
  ): Promise<PaginatedResult<ZaloZnsMessage>> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsMessages(userId, oaId, queryDto);
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param id ID của tin nhắn
   * @returns Thông tin chi tiết tin nhắn ZNS
   */
  async getZnsMessageDetailByIntegrationId(
    userId: number,
    integrationId: string,
    id: number,
  ): Promise<ZaloZnsMessage> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsMessageDetail(userId, oaId, id);
  }

  /**
   * Upload ảnh cho ZNS template bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param file File ảnh
   * @param uploadDto DTO upload
   * @returns Thông tin ảnh đã upload
   */
  async uploadZnsImageByIntegrationId(
    userId: number,
    integrationId: string,
    file: Express.Multer.File,
    uploadDto: UploadZnsImageDto,
  ): Promise<UploadZnsImageResponseDto> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.uploadZnsImage(userId, oaId, file, uploadDto);
  }

  /**
   * Lấy danh sách ảnh ZNS đã upload bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param queryDto Tham số truy vấn
   * @returns Danh sách ảnh ZNS với phân trang
   */
  async getZnsImagesByIntegrationId(
    userId: number,
    integrationId: string,
    queryDto: ZnsImageQueryDto,
  ): Promise<PaginatedResult<ZnsImageListResponseDto>> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsImages(userId, oaId, queryDto);
  }

  /**
   * Lấy thông tin loại nội dung ZNS được phép gửi bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns Thông tin loại nội dung ZNS
   */
  async getZnsTemplateTagsByIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<ZnsTemplateTagResponseDto> {
    const oa = await this.zaloService.getOfficialAccountByIntegrationId(
      userId,
      integrationId,
    );
    return this.getZnsTemplateTags(userId, oa);
  }

  /**
   * Lấy thông tin đánh giá khách hàng bằng integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param queryDto Tham số query
   * @returns Thông tin đánh giá khách hàng với pagination chuẩn
   */
  async getZnsRatingByIntegrationId(
    userId: number,
    integrationId: string,
    queryDto: ZnsRatingQueryDto,
  ): Promise<PaginatedResult<ZnsRatingDetailDto>> {
    const oaId = await this.getOaIdFromIntegrationId(userId, integrationId);
    return this.getZnsRating(userId, oaId, queryDto);
  }

  /**
   * Kiểm tra trạng thái ZNS của Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Trạng thái ZNS của Official Account
   */
  async getZnsStatus(
    userId: number,
    oaId: string,
    messageId: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để lấy trạng thái tin nhắn ZNS
      const znsStatus = await this.zaloZnsInfoService.getZnsMessageStatus(
        accessToken,
        messageId,
      );

      return znsStatus;
    } catch (error) {
      this.logger.error(`Failed to get ZNS status: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi lấy trạng thái ZNS',
      );
    }
  }

  /**
   * Cập nhật trạng thái ZNS của Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param status Trạng thái mới
   * @param templateId ID của template (optional)
   * @returns Kết quả cập nhật
   */
  async changeZnsStatus(
    userId: number,
    oaId: string,
    status: string,
    templateId?: string,
  ): Promise<any> {
    try {
      if (templateId) {
        // Cập nhật trạng thái cho template cụ thể
        const result =
          await this.zaloZnsTemplateRepository.updateByOaIdAndTemplateId(
            oaId,
            templateId,
            { status, updatedAt: Date.now() },
          );
        this.logger.log(
          `Updated template ${templateId} status to ${status} for OA ${oaId}`,
        );
        return result;
      } else {
        // Cập nhật trạng thái cho tất cả templates của OA
        const templates = await this.zaloZnsTemplateRepository.findByOaId(oaId);
        const updatePromises = templates.map((template) =>
          this.zaloZnsTemplateRepository.update(template.id, {
            status,
            updatedAt: Date.now(),
          }),
        );

        const results = await Promise.all(updatePromises);
        this.logger.log(
          `Updated ${results.length} templates status to ${status} for OA ${oaId}`,
        );
        return { updated: results.length, templates: results };
      }
    } catch (error) {
      this.logger.error(`Failed to change ZNS status: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi cập nhật trạng thái ZNS',
      );
    }
  }

  /**
   * Lấy thông tin quota ZNS của Official Account (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thông tin quota ZNS của Official Account
   */
  async getZnsQuota(userId: number, oaId: string): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để lấy thông tin quota ZNS
      const znsQuota = await this.zaloZnsInfoService.getZnsQuota(accessToken);

      return znsQuota;
    } catch (error) {
      this.logger.error(`Failed to get ZNS quota: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi lấy thông tin quota ZNS',
      );
    }
  }

  /**
   * Lấy danh sách template ZNS từ database với phân trang (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS với phân trang
   */
  async getZnsTemplates(
    userId: number,
    oaId: string | undefined,
    queryDto: ZnsTemplateQueryDto,
  ): Promise<PaginatedResult<ZaloZnsTemplate>> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        templateName,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId };

      // Chỉ thêm oaId vào điều kiện nếu có giá trị
      if (oaId) {
        where.oaId = oaId;
      }

      if (status && status !== 'all') {
        where.status = status;
      }

      if (search) {
        where.templateName = ILike(`%${search}%`);
      }

      if (templateName) {
        where.templateName = ILike(`%${templateName}%`);
      }

      // Tìm kiếm template ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsTemplateRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsTemplateRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS templates: ${error.message}`);
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED,
        'Không thể lấy danh sách template ZNS',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @returns Thông tin chi tiết template ZNS
   */
  async getZnsTemplateDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<ZaloZnsTemplate> {
    try {
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      return template;
    } catch (error) {
      this.logger.error(`Failed to get ZNS template detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED,
        'Không thể lấy thông tin chi tiết template ZNS',
      );
    }
  }

  /**
   * Lấy danh sách template ZNS từ Zalo API (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template ZNS từ Zalo API với cấu trúc phân trang
   */
  async getZnsTemplatesFromZaloApi(
    userId: number,
    oaId: string,
    queryDto: ZnsTemplateQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để lấy danh sách template
      const { page, limit, status } = queryDto;
      const offset = (page - 1) * limit;

      // Chuyển đổi status từ enum sang số theo yêu cầu của Zalo API
      let zaloStatus: number | undefined;
      if (status) {
        switch (status) {
          case 'approved':
            zaloStatus = 1; // Enable
            break;
          case 'pending':
            zaloStatus = 2; // Pending review
            break;
          case 'rejected':
            zaloStatus = 3; // Reject
            break;
          default:
            zaloStatus = undefined; // All statuses
        }
      }

      // Gọi Zalo API để lấy danh sách template
      const zaloResult = await this.sharedZaloZnsService.getZnsTemplates(
        accessToken,
        offset,
        limit,
        zaloStatus,
      );

      // Chuyển đổi sang cấu trúc PaginatedResult
      const totalPages = Math.ceil(zaloResult.metadata.total / limit);

      return {
        items: zaloResult.data,
        meta: {
          totalItems: zaloResult.metadata.total,
          itemCount: zaloResult.data.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to get ZNS templates from Zalo API: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể lấy danh sách template ZNS từ Zalo API',
      );
    }
  }

  /** YES
   * Lấy thông tin chi tiết template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template từ Zalo
   * @returns Thông tin chi tiết template ZNS từ Zalo API
   */
  async getZnsTemplateDetailFromZaloApi(
    userId: number,
    oaId: string,
    templateId: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để lấy chi tiết template
      const templateDetail =
        await this.sharedZaloZnsService.getZnsTemplateDetail(
          accessToken,
          templateId,
        );

      return templateDetail;
    } catch (error) {
      this.logger.error(
        `Failed to get ZNS template detail from Zalo API: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể lấy thông tin chi tiết template ZNS từ Zalo API',
      );
    }
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template từ Zalo
   * @returns Dữ liệu mẫu của template ZNS
   */
  async getZnsTemplateSampleData(
    userId: number,
    oaId: string,
    templateId: string,
  ): Promise<any> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để lấy dữ liệu mẫu của template
      const sampleData =
        await this.sharedZaloZnsService.getZnsTemplateSampleData(
          accessToken,
          templateId,
        );

      return sampleData;
    } catch (error) {
      this.logger.error(
        `Failed to get ZNS template sample data: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể lấy dữ liệu mẫu template ZNS',
      );
    }
  }

  /**
   * Đồng bộ template ZNS từ Zalo API sang database
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Thống kê kết quả đồng bộ
   */
  async syncTemplatesFromZaloApi(
    userId: number,
    oaId: string,
  ): Promise<{
    totalTemplates: number;
    syncedTemplates: number;
    updatedTemplates: number;
    newTemplates: number;
    skippedTemplates: number;
    errors: Array<{ templateId: string; error: string }>;
  }> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Lấy tất cả template từ Zalo API với retry logic
      const allTemplates: any[] = [];
      let offset = 0;
      const limit = 50; // Lấy 50 template mỗi lần
      let hasMore = true;
      let totalFromMetadata: number | null = null;

      while (hasMore) {
        let retryCount = 0;
        const maxRetries = 3;
        let zaloResult: any = null;

        // Retry logic cho từng page
        while (retryCount < maxRetries) {
          try {
            zaloResult = await this.zaloZnsInfoService.getZnsTemplateList(
              accessToken,
              { offset, limit },
            );
            break; // Thành công, thoát khỏi retry loop
          } catch (error) {
            retryCount++;
            this.logger.warn(
              `Failed to get templates at offset ${offset}, retry ${retryCount}/${maxRetries}: ${error.message}`,
            );
            if (retryCount >= maxRetries) {
              throw error; // Throw error nếu đã retry hết
            }
            // Đợi 1 giây trước khi retry
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }

        // this.logger.log(`Zalo API response structure:`, JSON.stringify(zaloResult, null, 2));

        // Zalo API trả về object có structure: { data: ZaloZnsTemplate[], metadata: { total: number } }
        if (zaloResult && zaloResult.data && Array.isArray(zaloResult.data)) {
          allTemplates.push(...zaloResult.data);

          // Lưu total từ metadata ở lần đầu
          if (totalFromMetadata === null && zaloResult.metadata?.total) {
            totalFromMetadata = zaloResult.metadata.total;
          }

          // Kiểm tra còn template nào không
          hasMore = zaloResult.data.length === limit;
          this.logger.log(
            `Added ${zaloResult.data.length} templates, total: ${allTemplates.length}/${totalFromMetadata || 'unknown'}`,
          );

          // Double check: Nếu đã lấy đủ theo metadata thì dừng
          if (totalFromMetadata && allTemplates.length >= totalFromMetadata) {
            hasMore = false;
          }
        } else {
          this.logger.warn(
            `No data or invalid data structure from Zalo API at offset ${offset}. Response:`,
            zaloResult,
          );
          hasMore = false;
        }

        offset += limit;

        // Safety check: Tránh infinite loop
        if (offset > 10000) {
          // Giới hạn tối đa 10000 template
          this.logger.warn(
            `Reached maximum offset limit (10000) for OA ${oaId}`,
          );
          break;
        }
      }

      // Validation: Kiểm tra xem đã lấy đủ template chưa
      const expectedTotal = totalFromMetadata || allTemplates.length;

      this.logger.log(
        `Found ${allTemplates.length}/${expectedTotal} templates from Zalo API for OA ${oaId}`,
      );

      // Warning nếu không lấy đủ template
      if (totalFromMetadata && allTemplates.length < totalFromMetadata) {
        this.logger.warn(
          `Potential data loss: Expected ${totalFromMetadata} templates but only got ${allTemplates.length} for OA ${oaId}`,
        );
      }

      // Thống kê kết quả
      const stats = {
        totalTemplates: allTemplates.length,
        syncedTemplates: 0,
        updatedTemplates: 0,
        newTemplates: 0,
        skippedTemplates: 0,
        errors: [] as Array<{ templateId: string; error: string }>,
      };

      // Đồng bộ từng template
      for (const template of allTemplates) {
        try {
          // Đồng bộ tất cả template (bao gồm cả REJECT để cập nhật trạng thái)
          // Không skip template nào để đảm bảo trạng thái được cập nhật đầy đủ

          // Lấy chi tiết template từ SharedZaloZnsService
          const templateDetail =
            await this.sharedZaloZnsService.getZnsTemplateDetail(
              accessToken,
              template.templateId.toString(),
            );

          // Kiểm tra template đã tồn tại trong database chưa
          const existingTemplate = await this.zaloZnsTemplateRepository.findOne(
            {
              where: { oaId, templateId: template.templateId.toString() },
            },
          );

          const now = Date.now();
          const templateData: any = {
            userId,
            oaId,
            templateId: template.templateId.toString(),
            templateName: template.templateName,
            templateContent: JSON.stringify(templateDetail.listParams || []),
            listParams: (templateDetail.listParams || []).map(
              (param: any) => param.name || '',
            ),
            status: this.mapZaloStatusToDbStatus(template.status),
            updatedAt: now,
          };

          // Thêm các field bổ sung từ template detail
          if (templateDetail.template_type) {
            templateData.templateType = templateDetail.template_type;
          }
          if (templateDetail.timeout) {
            templateData.timeout = templateDetail.timeout;
          }
          if (templateDetail.previewUrl) {
            templateData.previewUrl = templateDetail.previewUrl;
          }
          if (templateDetail.templateQuality) {
            templateData.templateQuality = templateDetail.templateQuality;
          }
          if (templateDetail.templateTag) {
            templateData.templateTag = templateDetail.templateTag;
          }
          if (templateDetail.price) {
            templateData.price = templateDetail.price;
          }
          if (templateDetail.applyTemplateQuota !== undefined) {
            templateData.applyTemplateQuota = templateDetail.applyTemplateQuota;
          }
          if (templateDetail.reason) {
            templateData.reason = templateDetail.reason;
          }

          if (existingTemplate) {
            // Cập nhật template hiện có
            await this.zaloZnsTemplateRepository.update(
              existingTemplate.id,
              templateData,
            );
            stats.updatedTemplates++;
            this.logger.log(
              `Updated template ${template.templateId} for OA ${oaId}`,
            );
          } else {
            // Tạo template mới
            await this.zaloZnsTemplateRepository.create({
              ...templateData,
              createdAt: now,
            });
            stats.newTemplates++;
            this.logger.log(
              `Created new template ${template.templateId} for OA ${oaId}`,
            );
          }

          stats.syncedTemplates++;
        } catch (error) {
          this.logger.error(
            `Failed to sync template ${template.templateId}: ${error.message}`,
          );
          stats.errors.push({
            templateId: template.templateId,
            error: error.message,
          });
        }
      }

      this.logger.log(`Sync completed for OA ${oaId}. Stats:`, stats);
      return stats;
    } catch (error) {
      this.logger.error(
        `Failed to sync templates from Zalo API: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể đồng bộ template ZNS từ Zalo API',
      );
    }
  }

  /**
   * Gọi Zalo API để tạo template ZNS trực tiếp theo đúng format
   * @param accessToken Access token của Official Account
   * @param templateData Dữ liệu template
   * @returns Kết quả từ Zalo API
   */
  private async createZnsTemplateDirectly(
    accessToken: string,
    templateData: any,
  ): Promise<{
    template_id: string;
    template_status: string;
    template_name: string;
    price: string;
  }> {
    try {
      const url = 'https://business.openapi.zalo.me/template/create';
      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      // Transform data theo đúng format Zalo API
      const requestBody = {
        template_name: templateData.template_name,
        template_type: templateData.template_type,
        tag: templateData.tag, // Tag đã là string từ DTO
        layout: templateData.layout,
        tracking_id: templateData.tracking_id,
        ...(templateData.params && { params: templateData.params }),
        ...(templateData.note && { note: templateData.note }),
      };

      // Log request body để debug
      this.logger.debug(
        'Zalo ZNS Template Create Request:',
        JSON.stringify(requestBody, null, 2),
      );

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: {
            template_id: string;
            template_name: string;
            template_type: number;
            template_status: string;
            tag: number;
            app_id: string;
            oa_id: string;
            price: string;
            timeout: number;
            preview_url: string;
          };
        }>(url, requestBody, { headers }),
      );

      // Log response để debug
      this.logger.debug(
        'Zalo ZNS Template Create Response:',
        JSON.stringify(response.data, null, 2),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          `Lỗi khi tạo template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          'Lỗi khi tạo template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error calling Zalo create template API: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi gọi Zalo API tạo template',
      );
    }
  }

  /**
   * Gọi Zalo API để chỉnh sửa template ZNS trực tiếp theo đúng format
   * @param accessToken Access token của Official Account
   * @param templateData Dữ liệu template cần chỉnh sửa
   * @returns Kết quả từ Zalo API
   */
  private async editZnsTemplateDirectly(
    accessToken: string,
    templateData: any,
  ): Promise<{
    template_id: string;
    template_status: string;
    template_name: string;
    price: string;
  }> {
    try {
      // Log request data để debug
      this.logger.debug(
        'Zalo ZNS Template Edit Request:',
        JSON.stringify(templateData, null, 2),
      );

      // Sử dụng shared service để gọi API edit template
      const result = await this.sharedZaloZnsService.editZnsTemplate(
        accessToken,
        {
          template_id: templateData.template_id,
          template_name: templateData.template_name,
          template_type: templateData.template_type,
          tag: templateData.tag,
          layout: templateData.layout,
          tracking_id: templateData.tracking_id,
          params: templateData.params,
          note: templateData.note,
        },
      );

      // Log response để debug
      this.logger.debug(
        'Zalo ZNS Template Edit Response:',
        JSON.stringify(result, null, 2),
      );

      return {
        template_id: result.template_id,
        template_status: result.status,
        template_name: templateData.template_name,
        price: '0', // API edit không trả về price, set default
      };
    } catch (error) {
      this.logger.error(
        `Error calling Zalo edit template API: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi gọi Zalo API chỉnh sửa template',
      );
    }
  }

  /**
   * Đăng ký template ZNS mới (để tương thích ngược)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param registerDto Dữ liệu đăng ký template
   * @returns Template ZNS đã đăng ký
   */
  async registerZnsTemplate(
    userId: number,
    oaId: string,
    registerDto: RegisterZnsTemplateDto,
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để tạo template theo đúng format
      const createResult = await this.createZnsTemplateDirectly(accessToken, {
        template_name: registerDto.template_name,
        template_type: registerDto.template_type,
        tag: registerDto.tag,
        layout: registerDto.layout,
        tracking_id: registerDto.tracking_id,
        params: registerDto.params,
        note: registerDto.note,
      });

      // Lấy chi tiết template từ Zalo API để có đầy đủ thông tin
      let templateDetail: any = null;
      try {
        templateDetail = await this.sharedZaloZnsService.getZnsTemplateDetail(
          accessToken,
          createResult.template_id.toString(),
        );
        this.logger.debug(
          'Zalo ZNS Template Detail Response:',
          JSON.stringify(templateDetail, null, 2),
        );
      } catch (error) {
        this.logger.warn(`Failed to get template detail: ${error.message}`);
        // Không throw error, chỉ log warning vì template đã tạo thành công
      }

      // Lưu template vào database với thông tin chi tiết
      const now = Date.now();
      const templateData: any = {
        userId,
        oaId,
        templateId: createResult.template_id,
        templateName: registerDto.template_name,
        templateContent: JSON.stringify(registerDto.layout), // Lưu layout dưới dạng JSON string
        templateType: registerDto.template_type.toString(),
        status: createResult.template_status,
        createdAt: now,
        updatedAt: now,
      };

      // Thêm thông tin chi tiết nếu có
      if (templateDetail) {
        templateData.timeout = templateDetail.timeout;
        templateData.previewUrl = templateDetail.previewUrl;
        templateData.templateQuality = templateDetail.templateQuality;
        templateData.templateTag = templateDetail.templateTag;
        templateData.price = templateDetail.price
          ? parseFloat(templateDetail.price)
          : null;
        templateData.applyTemplateQuota =
          templateDetail.applyTemplateQuota || false;
        templateData.reason = templateDetail.reason;
        templateData.listParams = templateDetail.listParams;
      }

      return await this.zaloZnsTemplateRepository.create(templateData);
    } catch (error) {
      this.logger.error(`Failed to register ZNS template: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_REGISTER_TEMPLATE_FAILED,
        'Không thể đăng ký template ZNS',
      );
    }
  }

  /**
   * Chỉnh sửa template ZNS (chỉ cho template có trạng thái REJECT)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template trong database
   * @param editDto Dữ liệu chỉnh sửa template
   * @returns Template ZNS đã chỉnh sửa
   */
  async editZnsTemplate(
    userId: number,
    oaId: string,
    templateId: number,
    editDto: RegisterZnsTemplateDto,
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy template hiện tại từ database
      const existingTemplate = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId, userId, oaId },
      });

      if (!existingTemplate) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      // Kiểm tra trạng thái template - chỉ cho phép chỉnh sửa template có trạng thái REJECT
      if (existingTemplate.status !== 'REJECT') {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_INVALID_STATUS,
          'Chỉ có thể chỉnh sửa template có trạng thái REJECT (đã bị từ chối)',
        );
      }

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Gọi Zalo API để chỉnh sửa template theo đúng format
      const editResult = await this.editZnsTemplateDirectly(accessToken, {
        template_id: existingTemplate.templateId,
        template_name: editDto.template_name,
        template_type: editDto.template_type,
        tag: editDto.tag,
        layout: editDto.layout,
        tracking_id: editDto.tracking_id,
        params: editDto.params,
        note: editDto.note,
      });

      // Lấy chi tiết template từ Zalo API để có đầy đủ thông tin
      let templateDetail: any = null;
      try {
        templateDetail = await this.sharedZaloZnsService.getZnsTemplateDetail(
          accessToken,
          editResult.template_id.toString(),
        );
        this.logger.debug(
          'Zalo ZNS Template Detail Response after edit:',
          JSON.stringify(templateDetail, null, 2),
        );
      } catch (error) {
        this.logger.warn(
          `Failed to get template detail after edit: ${error.message}`,
        );
        // Không throw error, chỉ log warning vì template đã chỉnh sửa thành công
      }

      // Cập nhật template trong database với thông tin mới
      const now = Date.now();
      const updateData: any = {
        templateName: editDto.template_name,
        templateContent: JSON.stringify(editDto.layout), // Lưu layout dưới dạng JSON string
        templateType: editDto.template_type.toString(),
        status: editResult.template_status,
        updatedAt: now,
      };

      // Thêm thông tin chi tiết nếu có
      if (templateDetail) {
        updateData.timeout = templateDetail.timeout;
        updateData.previewUrl = templateDetail.previewUrl;
        updateData.templateQuality = templateDetail.templateQuality;
        updateData.templateTag = templateDetail.templateTag;
        updateData.price = templateDetail.price
          ? parseFloat(templateDetail.price)
          : null;
        updateData.applyTemplateQuota =
          templateDetail.applyTemplateQuota || false;
        updateData.reason = templateDetail.reason;
        updateData.listParams = templateDetail.listParams;
      }

      await this.zaloZnsTemplateRepository.update(templateId, updateData);

      // Trả về template đã cập nhật
      const updatedTemplate = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId },
      });

      if (!updatedTemplate) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS sau khi cập nhật',
        );
      }

      return updatedTemplate;
    } catch (error) {
      this.logger.error(`Failed to edit ZNS template: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_EDIT_TEMPLATE_FAILED,
        'Không thể chỉnh sửa template ZNS',
      );
    }
  }

  /**
   * Cập nhật trạng thái template ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param templateId ID của template
   * @param status Trạng thái mới
   * @returns Template ZNS đã cập nhật
   */
  async updateZnsTemplateStatus(
    userId: number,
    oaId: string,
    id: number,
    status: string,
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra template tồn tại trước khi cập nhật
      await this.getZnsTemplateDetail(userId, oaId, id);

      // Cập nhật trạng thái
      await this.zaloZnsTemplateRepository.update(id, {
        status,
        updatedAt: Date.now(),
      });

      return await this.getZnsTemplateDetail(userId, oaId, id);
    } catch (error) {
      this.logger.error(
        `Failed to update ZNS template status: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_UPDATE_TEMPLATE_STATUS_FAILED,
        'Không thể cập nhật trạng thái template ZNS',
      );
    }
  }

  /**
   * Kiểm tra trạng thái template ZNS từ Zalo API và cập nhật vào database
   * @param userId ID của người dùng
   * @param integrationId ID của integration
   * @param templateId ID của template trong database
   * @returns Template ZNS đã cập nhật
   */
  async checkAndUpdateTemplateStatusFromZalo(
    userId: number,
    integrationId: string,
    templateId: number,
  ): Promise<ZaloZnsTemplate> {
    try {
      // Lấy thông tin template từ database
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId, userId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      // Lấy access token từ integration
      const integration = await this.zaloOAAdapterService.findOne({
        where: { id: integrationId },
      });
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      // Kiểm tra quyền sở hữu
      if (integration.userId !== userId) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Không có quyền truy cập integration này',
        );
      }

      // Lấy access token đã giải mã
      const tokens =
        await this.zaloOAAdapterService.getDecryptedTokens(integrationId);

      // Lấy thông tin template từ Zalo API
      const zaloTemplateDetail =
        await this.sharedZaloZnsService.getZnsTemplateDetail(
          tokens.accessToken,
          template.templateId,
        );

      // Mapping trạng thái từ Zalo API sang database
      let dbStatus = template.status;
      if (zaloTemplateDetail.status) {
        switch (zaloTemplateDetail.status.toLowerCase()) {
          case 'enable':
          case 'approved':
            dbStatus = 'APPROVED';
            break;
          case 'disable':
          case 'rejected':
          case 'reject':
            dbStatus = 'REJECTED';
            break;
          case 'pending_review':
          case 'pending':
            dbStatus = 'PENDING';
            break;
          default:
            dbStatus = zaloTemplateDetail.status.toUpperCase();
        }
      }

      // Cập nhật thông tin template trong database
      const updateData: any = {
        status: dbStatus,
        updatedAt: Date.now(),
      };

      // Cập nhật các thông tin khác nếu có
      if (zaloTemplateDetail.templateQuality) {
        updateData.templateQuality = zaloTemplateDetail.templateQuality;
      }
      if (zaloTemplateDetail.price !== undefined) {
        updateData.price = zaloTemplateDetail.price;
      }
      if (zaloTemplateDetail.applyTemplateQuota !== undefined) {
        updateData.applyTemplateQuota = zaloTemplateDetail.applyTemplateQuota;
      }
      if (zaloTemplateDetail.listParams) {
        updateData.listParams = zaloTemplateDetail.listParams;
      }

      await this.zaloZnsTemplateRepository.update(templateId, updateData);

      // Lấy template đã cập nhật
      const updatedTemplate = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId, userId },
      });

      if (!updatedTemplate) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS sau khi cập nhật',
        );
      }

      this.logger.log(
        `Successfully updated template status from Zalo API: ${template.templateId} -> ${dbStatus}`,
      );

      return updatedTemplate;
    } catch (error) {
      this.logger.error(
        `Failed to check and update template status from Zalo: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_UPDATE_TEMPLATE_STATUS_FAILED,
        'Không thể kiểm tra và cập nhật trạng thái template ZNS từ Zalo',
      );
    }
  }

  /**
   * API test để kiểm tra trạng thái template ZNS từ Zalo API
   * @param userId ID của người dùng
   * @param integrationId ID của integration
   * @param templateId ID của template trong database
   * @returns Thông tin template từ Zalo API và database
   */
  async testCheckTemplateStatusFromZalo(
    userId: number,
    integrationId: string,
    templateId: number,
  ): Promise<{
    databaseTemplate: ZaloZnsTemplate;
    zaloApiTemplate: any;
    statusMapping: {
      currentStatus: string;
      zaloStatus: string;
      mappedStatus: string;
    };
  }> {
    try {
      // Lấy thông tin template từ database
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { id: templateId, userId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      // Lấy access token từ integration
      const integration = await this.zaloOAAdapterService.findOne({
        where: { id: integrationId },
      });
      if (!integration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      // Kiểm tra quyền sở hữu
      if (integration.userId !== userId) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Không có quyền truy cập integration này',
        );
      }

      // Lấy access token đã giải mã
      const tokens =
        await this.zaloOAAdapterService.getDecryptedTokens(integrationId);

      // Lấy thông tin template từ Zalo API
      const zaloTemplateDetail =
        await this.sharedZaloZnsService.getZnsTemplateDetail(
          tokens.accessToken,
          template.templateId,
        );

      // Mapping trạng thái từ Zalo API sang database
      let mappedStatus = template.status;
      if (zaloTemplateDetail.status) {
        switch (zaloTemplateDetail.status.toLowerCase()) {
          case 'enable':
          case 'approved':
            mappedStatus = 'approved';
            break;
          case 'disable':
          case 'rejected':
          case 'reject':
            mappedStatus = 'rejected';
            break;
          case 'pending_review':
          case 'pending':
            mappedStatus = 'pending';
            break;
          default:
            mappedStatus = zaloTemplateDetail.status.toLowerCase();
        }
      }

      return {
        databaseTemplate: template,
        zaloApiTemplate: zaloTemplateDetail,
        statusMapping: {
          currentStatus: template.status,
          zaloStatus: zaloTemplateDetail.status || 'unknown',
          mappedStatus,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to test check template status from Zalo: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_TEMPLATES_FAILED,
        'Không thể kiểm tra trạng thái template ZNS từ Zalo API',
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param sendDto Dữ liệu gửi tin nhắn
   * @returns Kết quả gửi tin nhắn
   */
  async sendZnsMessage(
    userId: number,
    oaId: string,
    sendDto: SendZnsMessageDto,
  ): Promise<ZaloZnsMessage> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ (tự động refresh nếu hết hạn)
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Kiểm tra template tồn tại
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { templateId: sendDto.templateId, oaId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          'Không tìm thấy template ZNS',
        );
      }

      if (template.status !== 'approved') {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_APPROVED,
          'Template ZNS chưa được phê duyệt',
        );
      }

      // Tạo tracking ID
      const trackingId = `zns_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Gửi tin nhắn ZNS qua Zalo API
      const sendResult = await this.sharedZaloZnsService.sendZnsMessage(
        accessToken,
        {
          phone: sendDto.phone,
          template_id: sendDto.templateId,
          template_data: sendDto.templateData,
          tracking_id: trackingId,
        },
      );

      // Lưu tin nhắn vào database
      const now = Date.now();
      const message = this.zaloZnsMessageRepository.create({
        userId,
        oaId,
        templateId: sendDto.templateId,
        phone: sendDto.phone,
        messageId: sendResult.message_id,
        trackingId,
        templateData: sendDto.templateData,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
      });

      return await this.zaloZnsMessageRepository.save(message);
    } catch (error) {
      this.logger.error(`Failed to send ZNS message: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_SEND_MESSAGE_FAILED,
        'Không thể gửi tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy lịch sử tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn ZNS với phân trang
   */
  async getZnsMessages(
    userId: number,
    oaId: string,
    queryDto: ZnsMessageQueryDto,
  ): Promise<PaginatedResult<ZaloZnsMessage>> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        phone,
        templateId,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (templateId) {
        where.templateId = templateId;
      }

      if (phone) {
        where.phone = phone;
      }

      if (search) {
        where.phone = ILike(`%${search}%`);
      }

      // Tìm kiếm tin nhắn ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsMessageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsMessageRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS messages: ${error.message}`);
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_MESSAGES_FAILED,
        'Không thể lấy lịch sử tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy tất cả lịch sử tin nhắn ZNS của user (không cần integrationId)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử tin nhắn ZNS với phân trang
   */
  async getAllZnsMessages(
    userId: number,
    queryDto: ZnsMessageQueryDto,
  ): Promise<PaginatedResult<ZaloZnsMessage>> {
    try {
      const {
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        phone,
        templateId,
        status,
      } = queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm - chỉ cần userId
      const where: any = { userId };

      if (status && status !== 'all') {
        where.status = status;
      }

      if (templateId) {
        where.templateId = templateId;
      }

      if (phone) {
        where.phone = phone;
      }

      if (search) {
        where.phone = ILike(`%${search}%`);
      }

      // Tìm kiếm tin nhắn ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsMessageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsMessageRepository.count({ where }),
      ]);

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get all ZNS messages: ${error.message}`);
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_MESSAGES_FAILED,
        'Không thể lấy lịch sử tin nhắn ZNS',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param id ID của tin nhắn
   * @returns Thông tin chi tiết tin nhắn ZNS
   */
  async getZnsMessageDetail(
    userId: number,
    oaId: string,
    id: number,
  ): Promise<ZaloZnsMessage> {
    try {
      const message = await this.zaloZnsMessageRepository.findOne({
        where: { id, userId, oaId },
      });

      if (!message) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_MESSAGE_NOT_FOUND,
          'Không tìm thấy tin nhắn ZNS',
        );
      }

      return message;
    } catch (error) {
      this.logger.error(`Failed to get ZNS message detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_MESSAGES_FAILED,
        'Không thể lấy thông tin chi tiết tin nhắn ZNS',
      );
    }
  }

  /**
   * Chuyển đổi trạng thái template từ Zalo sang database
   * @param zaloStatus Trạng thái từ Zalo API
   * @returns Trạng thái cho database
   */
  private mapZaloStatusToDbStatus(zaloStatus: string): string {
    switch (zaloStatus) {
      case 'ENABLE':
        return 'approved';
      case 'PENDING_REVIEW':
        return 'pending';
      case 'REJECT':
        return 'rejected';
      case 'DISABLE':
        return 'disabled';
      case 'DELETE':
        return 'deleted';
      default:
        return 'pending';
    }
  }

  /**
   * Upload ảnh cho ZNS template
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param file File ảnh
   * @param uploadDto DTO upload
   * @returns Thông tin ảnh đã upload
   */
  async uploadZnsImage(
    userId: number,
    oaId: string,
    file: Express.Multer.File,
    uploadDto: UploadZnsImageDto,
  ): Promise<UploadZnsImageResponseDto> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Kiểm tra file
      if (!file) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          'Không có file được upload',
        );
      }

      // Kiểm tra định dạng file
      if (!['image/jpeg', 'image/jpg', 'image/png'].includes(file.mimetype)) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          'File phải có định dạng JPG hoặc PNG',
        );
      }

      // Kiểm tra kích thước file (500KB = 512000 bytes)
      if (file.size > 512000) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          'File không được vượt quá 500KB',
        );
      }

      // Gọi Zalo API để upload ảnh
      const FormData = require('form-data');
      const formData = new FormData();
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { media_id: string };
        }>('https://business.openapi.zalo.me/upload/image', formData, {
          headers: {
            access_token: accessToken,
            ...formData.getHeaders(),
          },
        }),
      );

      if (response.data.error !== 0 || !response.data.data) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          `Lỗi khi upload ảnh: ${response.data.message}`,
        );
      }

      // Lưu thông tin ảnh vào database
      const now = Date.now();
      const imageData = {
        userId,
        oaId,
        mediaId: response.data.data.media_id,
        originalFilename: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype,
        description: uploadDto.description,
        createdAt: now,
        updatedAt: now,
      };

      const savedImage =
        await this.zaloZnsImageRepository.createImage(imageData);

      return {
        id: savedImage.id,
        mediaId: savedImage.mediaId,
        originalFilename: savedImage.originalFilename,
        fileSize: savedImage.fileSize,
        mimeType: savedImage.mimeType,
        width: savedImage.width,
        height: savedImage.height,
        description: savedImage.description,
        createdAt: savedImage.createdAt,
      };
    } catch (error) {
      this.logger.error(`Failed to upload ZNS image: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể upload ảnh ZNS',
      );
    }
  }

  /**
   * Lấy danh sách ảnh ZNS đã upload từ database
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số truy vấn
   * @returns Danh sách ảnh ZNS với phân trang
   */
  async getZnsImages(
    userId: number,
    oaId: string,
    queryDto: ZnsImageQueryDto,
  ): Promise<PaginatedResult<ZnsImageListResponseDto>> {
    try {
      const { page, limit, search, sortBy, sortDirection, filename, mimeType } =
        queryDto;
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện tìm kiếm
      const where: any = { userId, oaId };

      if (search) {
        where.originalFilename = ILike(`%${search}%`);
      }

      if (filename) {
        where.originalFilename = ILike(`%${filename}%`);
      }

      if (mimeType) {
        where.mimeType = mimeType;
      }

      // Tìm kiếm ảnh ZNS
      const [items, totalItems] = await Promise.all([
        this.zaloZnsImageRepository.find({
          where,
          skip,
          take: limit,
          order: {
            [sortBy || 'createdAt']: sortDirection || 'DESC',
          },
        }),
        this.zaloZnsImageRepository.count({ where }),
      ]);

      // Chuyển đổi entity sang DTO response
      const responseItems: ZnsImageListResponseDto[] = items.map((item) => ({
        id: item.id,
        mediaId: item.mediaId,
        originalFilename: item.originalFilename,
        fileSize: item.fileSize,
        mimeType: item.mimeType,
        width: item.width,
        height: item.height,
        description: item.description,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }));

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items: responseItems,
        meta: {
          totalItems,
          itemCount: responseItems.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS images: ${error.message}`);
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_GET_IMAGES_FAILED,
        'Không thể lấy danh sách ảnh ZNS',
      );
    }
  }

  /**
   * Lấy thông tin loại nội dung ZNS được phép gửi
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Danh sách loại nội dung ZNS được phép gửi
   */
  async getZnsTemplateTags(
    userId: number,
    oa: ZaloOfficialAccount,
  ): Promise<ZnsTemplateTagResponseDto> {
    try {
      // Gọi Zalo API để lấy thông tin loại nội dung ZNS
      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: string[];
        }>('https://business.openapi.zalo.me/message/template-tag', {
          headers: {
            access_token: oa.accessToken,
            'Content-Type': 'application/json',
          },
        }),
      );

      if (response.data.error !== 0 || !response.data.data) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          `Lỗi khi lấy thông tin loại nội dung ZNS: ${response.data.message}`,
        );
      }

      return {
        data: response.data.data,
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS template tags: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể lấy thông tin loại nội dung ZNS',
      );
    }
  }

  /**
   * Xử lý cập nhật trạng thái template ZNS từ webhook
   * @param webhookData Dữ liệu webhook từ Zalo
   */
  async handleZnsTemplateStatusChange(
    webhookData: ZaloWebhookTemplateStatusChangeDto,
  ): Promise<void> {
    const { oa_id, template_id, status, reason, timestamp } = webhookData;
    // Tìm template trong DB
    const template = await this.zaloZnsTemplateRepository.findOne({
      where: { oaId: oa_id, templateId: template_id },
    });
    if (!template) {
      this.logger.warn(
        `Không tìm thấy template ${template_id} cho OA ${oa_id} khi nhận webhook change_template_status`,
      );
      return;
    }
    // Cập nhật trạng thái mới
    const updateData: any = {
      status: this.mapZaloStatusToDbStatus(status.new_status),
      updatedAt: Number(timestamp) || Date.now(),
    };

    if (reason) {
      updateData.statusChangeReason = reason;
    }

    await this.zaloZnsTemplateRepository.update(template.id, updateData);
    this.logger.log(
      `Đã cập nhật trạng thái template ${template_id} cho OA ${oa_id} thành ${status.new_status}`,
    );
  }

  /**
   * Lấy thông tin đánh giá khách hàng
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param queryDto Tham số query
   * @returns Thông tin đánh giá khách hàng với pagination chuẩn
   */
  async getZnsRating(
    userId: number,
    oaId: string,
    queryDto: ZnsRatingQueryDto,
  ): Promise<PaginatedResult<ZnsRatingDetailDto>> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Lấy access token hợp lệ
      const accessToken = await this.zaloTokenService.getValidAccessToken(oaId);

      if (!accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Official Account chưa có access token',
        );
      }

      // Chuyển đổi pagination từ page/limit sang offset/limit cho Zalo API
      const offset = (queryDto.page - 1) * queryDto.limit;

      // Gọi Zalo API để lấy đánh giá khách hàng
      const response = await lastValueFrom(
        this.httpService.get<ZnsRatingResponseDto>(
          'https://business.openapi.zalo.me/rating/get',
          {
            params: {
              template_id: queryDto.template_id,
              from_time: queryDto.from_time,
              to_time: queryDto.to_time,
              offset: offset,
              limit: queryDto.limit,
            },
            headers: {
              access_token: accessToken,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_API_ERROR,
          `Lỗi khi lấy đánh giá khách hàng: ${response.data.message || 'Unknown error'}`,
        );
      }

      // Chuyển đổi response từ Zalo sang format PaginatedResult chuẩn
      const zaloData = response.data.data;
      const totalPages = Math.ceil(zaloData.total / queryDto.limit);

      return {
        items: zaloData.data || [],
        meta: {
          totalItems: zaloData.total || 0,
          itemCount: zaloData.data?.length || 0,
          itemsPerPage: queryDto.limit,
          totalPages: totalPages,
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS rating: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể lấy thông tin đánh giá khách hàng',
      );
    }
  }

  /**
   * Tạo template ZNS draft chỉ vào database (không đăng ký lên Zalo)
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @param createDto Dữ liệu tạo template draft
   * @returns Template ZNS draft đã tạo
   */
  async createZnsTemplateDraft(
    userId: number,
    oaId: string,
    createDto: CreateZnsTemplateDraftDto,
  ): Promise<ZaloZnsTemplate> {
    try {
      // Kiểm tra quyền truy cập Official Account
      await this.zaloService.getOfficialAccountDetail(userId, oaId);

      // Tạo template ID tạm thời cho draft (sẽ được thay thế khi đăng ký lên Zalo)
      const draftTemplateId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Lưu template vào database với trạng thái draft
      const now = Date.now();
      const templateData: any = {
        userId,
        oaId,
        templateId: draftTemplateId,
        templateName: createDto.template_name,
        templateContent: JSON.stringify(createDto.layout), // Lưu layout dưới dạng JSON string
        templateType: createDto.template_type.toString(),
        status: 'draft', // Trạng thái chưa public
        templateTag: createDto.tag,
        listParams: createDto.params || [],
        reason: createDto.note || null,
        createdAt: now,
        updatedAt: now,
      };

      return await this.zaloZnsTemplateRepository.create(templateData);
    } catch (error) {
      this.logger.error(
        `Failed to create ZNS template draft: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_REGISTER_TEMPLATE_FAILED,
        'Không thể tạo template ZNS draft',
      );
    }
  }

  /**
   * Đồng bộ template ZNS từ Zalo API cho tất cả integration của user
   * @param userId ID của người dùng
   * @returns Thống kê kết quả đồng bộ tổng hợp
   */
  async syncAllTemplatesFromZaloApi(userId: number): Promise<{
    totalIntegrations: number;
    processedIntegrations: number;
    totalTemplates: number;
    syncedTemplates: number;
    updatedTemplates: number;
    newTemplates: number;
    skippedTemplates: number;
    errors: Array<{ integrationId: string; oaId: string; error: string }>;
  }> {
    try {
      // Lấy tất cả Integration Zalo OA của user
      const integrations =
        await this.zaloOAIntegrationService.getZaloOAIntegrationsByUserId(
          userId,
        );

      this.logger.log(
        `Found ${integrations.length} Zalo OA integrations for user ${userId}`,
      );

      // Khởi tạo thống kê tổng hợp
      const stats = {
        totalIntegrations: integrations.length,
        processedIntegrations: 0,
        totalTemplates: 0,
        syncedTemplates: 0,
        updatedTemplates: 0,
        newTemplates: 0,
        skippedTemplates: 0,
        errors: [] as Array<{
          integrationId: string;
          oaId: string;
          error: string;
        }>,
      };

      // Xử lý từng integration tuần tự để tránh quá tải API
      for (const integration of integrations) {
        try {
          this.logger.debug(
            `Processing integration ${integration.id} (OA: ${integration.metadata.oaId})`,
          );

          // Đồng bộ template cho integration này
          const syncResult = await this.syncTemplatesFromZaloApiByIntegrationId(
            userId,
            integration.id,
          );

          // Cộng dồn thống kê
          stats.totalTemplates += syncResult.totalTemplates;
          stats.syncedTemplates += syncResult.syncedTemplates;
          stats.updatedTemplates += syncResult.updatedTemplates;
          stats.newTemplates += syncResult.newTemplates;
          stats.skippedTemplates += syncResult.skippedTemplates;

          // Thêm lỗi từ integration này (nếu có)
          if (syncResult.errors && syncResult.errors.length > 0) {
            syncResult.errors.forEach((error) => {
              stats.errors.push({
                integrationId: integration.id,
                oaId: integration.metadata.oaId,
                error: error.error,
              });
            });
          }

          stats.processedIntegrations++;

          this.logger.debug(
            `Successfully processed integration ${integration.id}: ${syncResult.syncedTemplates}/${syncResult.totalTemplates} templates synced`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to sync templates for integration ${integration.id}: ${error.message}`,
            error.stack,
          );

          // Thêm lỗi vào danh sách
          stats.errors.push({
            integrationId: integration.id,
            oaId: integration.metadata.oaId,
            error: error.message,
          });

          // Tiếp tục với integration tiếp theo
          continue;
        }
      }

      this.logger.log(
        `Completed sync for all integrations. Processed: ${stats.processedIntegrations}/${stats.totalIntegrations}, Total templates synced: ${stats.syncedTemplates}/${stats.totalTemplates}`,
      );

      return stats;
    } catch (error) {
      this.logger.error(
        `Failed to sync templates for all integrations: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Lỗi khi đồng bộ template ZNS cho tất cả integration',
      );
    }
  }

  /**
   * Cập nhật trạng thái tất cả template ZNS từ tất cả OA của user
   * @param userId ID của người dùng
   * @returns Thống kê kết quả cập nhật
   */
  async updateAllTemplateStatusFromZaloApi(userId: number): Promise<{
    totalIntegrations: number;
    processedIntegrations: number;
    totalTemplates: number;
    updatedTemplates: number;
    skippedTemplates: number;
    errors: Array<{ integrationId: string; oaId: string; error: string }>;
  }> {
    try {
      // Lấy tất cả Integration Zalo OA của user
      const integrations =
        await this.zaloOAIntegrationService.getZaloOAIntegrationsByUserId(
          userId,
        );

      this.logger.log(
        `Found ${integrations.length} Zalo OA integrations for user ${userId}`,
      );

      // Thống kê kết quả
      const stats = {
        totalIntegrations: integrations.length,
        processedIntegrations: 0,
        totalTemplates: 0,
        updatedTemplates: 0,
        skippedTemplates: 0,
        errors: [] as Array<{
          integrationId: string;
          oaId: string;
          error: string;
        }>,
      };

      // Xử lý từng Integration
      for (const integration of integrations) {
        try {
          const metadata = integration.metadata as any;
          const oaId = metadata?.oaId;

          if (!oaId) {
            this.logger.warn(
              `Integration ${integration.id} không có oaId trong metadata`,
            );
            stats.errors.push({
              integrationId: integration.id,
              oaId: 'unknown',
              error: 'Không tìm thấy oaId trong metadata',
            });
            continue;
          }

          this.logger.log(
            `Processing integration ${integration.id} with OA ${oaId}`,
          );

          // Lấy access token hợp lệ
          const accessToken =
            await this.zaloTokenService.getValidAccessToken(oaId);

          if (!accessToken) {
            this.logger.warn(`OA ${oaId} không có access token hợp lệ`);
            stats.errors.push({
              integrationId: integration.id,
              oaId,
              error: 'Không có access token hợp lệ',
            });
            continue;
          }

          // Lấy tất cả template từ database cho OA này
          const dbTemplates = await this.zaloZnsTemplateRepository.find({
            where: { userId, oaId },
          });

          this.logger.log(
            `Found ${dbTemplates.length} templates in database for OA ${oaId}`,
          );
          stats.totalTemplates += dbTemplates.length;

          // Cập nhật trạng thái từng template
          for (const dbTemplate of dbTemplates) {
            try {
              // Bỏ qua template draft (không có trên Zalo)
              if (
                dbTemplate.status === 'draft' ||
                dbTemplate.templateId.startsWith('draft_')
              ) {
                stats.skippedTemplates++;
                continue;
              }

              // Lấy chi tiết template từ Zalo API
              const templateDetail =
                await this.sharedZaloZnsService.getZnsTemplateDetail(
                  accessToken,
                  dbTemplate.templateId,
                );

              // Chuyển đổi trạng thái từ Zalo sang database
              const newStatus = this.mapZaloStatusToDbStatus(
                templateDetail.status,
              );

              // Chỉ cập nhật nếu trạng thái thay đổi
              if (dbTemplate.status !== newStatus) {
                await this.zaloZnsTemplateRepository.update(dbTemplate.id, {
                  status: newStatus,
                  updatedAt: Date.now(),
                });

                this.logger.log(
                  `Updated template ${dbTemplate.templateId} status from ${dbTemplate.status} to ${newStatus}`,
                );
                stats.updatedTemplates++;
              } else {
                stats.skippedTemplates++;
              }
            } catch (templateError) {
              this.logger.error(
                `Failed to update template ${dbTemplate.templateId}: ${templateError.message}`,
              );
              stats.skippedTemplates++;
              // Không thêm vào errors vì đây là lỗi template cụ thể, không phải lỗi integration
            }
          }

          stats.processedIntegrations++;
        } catch (integrationError) {
          const metadata = integration.metadata as any;
          const oaId = metadata?.oaId || 'unknown';

          this.logger.error(
            `Failed to process integration ${integration.id}: ${integrationError.message}`,
          );
          stats.errors.push({
            integrationId: integration.id,
            oaId,
            error: integrationError.message,
          });
        }
      }

      this.logger.log(`Update completed for user ${userId}. Stats:`, stats);
      return stats;
    } catch (error) {
      this.logger.error(
        `Failed to update all template status: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_API_ERROR,
        'Không thể cập nhật trạng thái template ZNS',
      );
    }
  }

  /**
   * Xóa nhiều ZNS template cùng lúc
   * @param userId ID của user
   * @param templateIds Danh sách ID template cần xóa
   * @returns Kết quả xóa chi tiết
   */
  async bulkDeleteZnsTemplates(
    userId: number,
    templateIds: number[],
  ): Promise<{
    totalRequested: number;
    totalSuccess: number;
    totalFailed: number;
    results: Array<{
      id: number;
      templateName: string;
      templateId: string;
      success: boolean;
      message: string;
      error?: string;
    }>;
    deletedIds: number[];
    failedIds: number[];
  }> {
    this.logger.debug(
      `Bulk deleting ${templateIds.length} ZNS templates for user ${userId}`,
    );

    // Lấy tất cả templates thuộc về user
    const templates = await this.zaloZnsTemplateRepository.findByIdsAndUserId(
      templateIds,
      userId,
    );

    // Kiểm tra templates không tồn tại hoặc không thuộc về user
    const foundIds = templates.map((template) => template.id);
    const notFoundIds = templateIds.filter((id) => !foundIds.includes(id));

    if (notFoundIds.length > 0) {
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
        `Không tìm thấy các template: ${notFoundIds.join(', ')}`,
      );
    }

    const results: Array<{
      id: number;
      templateName: string;
      templateId: string;
      success: boolean;
      message: string;
      error?: string;
    }> = [];
    const deletedIds: number[] = [];
    const failedIds: number[] = [];
    let totalSuccess = 0;
    let totalFailed = 0;

    // Xử lý từng template
    for (const template of templates) {
      const result = {
        id: template.id,
        templateName: template.templateName,
        templateId: template.templateId,
        success: false,
        message: '',
        error: undefined as string | undefined,
      };

      try {
        // Xóa template trong database
        const deleted = await this.zaloZnsTemplateRepository.delete(
          template.id,
        );

        if (deleted) {
          result.success = true;
          result.message = 'Xóa template thành công';
          deletedIds.push(template.id);
          totalSuccess++;
          this.logger.debug(
            `Successfully deleted template ${template.id} (${template.templateName})`,
          );
        } else {
          result.message = 'Không thể xóa template trong database';
          result.error = 'Database operation failed';
          failedIds.push(template.id);
          totalFailed++;
        }
      } catch (error) {
        result.message = `Lỗi khi xóa template: ${error.message}`;
        result.error = error.message;
        failedIds.push(template.id);
        totalFailed++;
        this.logger.error(
          `Error deleting template ${template.id}: ${error.message}`,
          error.stack,
        );
      }

      results.push(result);
    }

    this.logger.log(
      `Bulk delete completed. Success: ${totalSuccess}, Failed: ${totalFailed}`,
    );

    return {
      totalRequested: templateIds.length,
      totalSuccess,
      totalFailed,
      results,
      deletedIds,
      failedIds,
    };
  }
}
