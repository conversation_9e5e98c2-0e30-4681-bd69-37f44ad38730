import { Injectable, Logger } from '@nestjs/common';
import { BusinessReportRepository } from '@modules/business/repositories/business-report.repository';
import { BusinessReportHelper } from '@modules/business/user/helpers';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { plainToInstance } from 'class-transformer';
import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto,
  ProductsTimelineChartQueryDto,
  ProductsTimelineChartResponseDto,
  OrdersTimelineChartQueryDto,
  OrdersTimelineChartResponseDto,
  BusinessLineChartQueryDto,
  BusinessLineChartResponseDto,
  BusinessChartDataType,
  BusinessChartPeriodType,
} from '../dto/report';

/**
 * Service xử lý logic nghiệp vụ cho báo cáo business
 */
@Injectable()
export class BusinessReportService {
  private readonly logger = new Logger(BusinessReportService.name);

  constructor(
    private readonly businessReportRepository: BusinessReportRepository,
    private readonly businessReportHelper: BusinessReportHelper,
  ) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   * Tương tự như R-Point API
   */
  async getReportOverview(
    userId: number,
    query: ReportOverviewQueryDto,
  ): Promise<ReportOverviewResponseDto> {
    try {
      this.logger.log(
        `Lấy tổng quan báo cáo cho user ${userId} với query: ${JSON.stringify(query)}`,
      );

      // Xử lý thời gian mặc định giống R-Point API
      const now = Math.floor(Date.now() / 1000); // Current timestamp in seconds
      const endTimestamp = query.end ?? now;

      let beginTimestamp: number;
      if (query.begin) {
        beginTimestamp = query.begin;
      } else {
        // Mặc định lấy 30 ngày trước
        beginTimestamp = now - 30 * 24 * 60 * 60; // 30 days ago
      }

      const beginDate = new Date(beginTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);

      // Convert to string format for repository
      const startDate = beginDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Lấy dữ liệu hiện tại
      const currentData = await this.businessReportRepository.getReportOverview(
        userId,
        {
          ...query,
          startDate,
          endDate: endDateStr,
        },
      );

      // Lấy dữ liệu kỳ trước để so sánh
      const previousDateRange =
        this.businessReportHelper.getPreviousPeriodDateRange(
          startDate,
          endDateStr,
        );

      const previousData =
        await this.businessReportRepository.getReportOverview(userId, {
          ...query,
          startDate: previousDateRange.startDate,
          endDate: previousDateRange.endDate,
        });

      // Tính toán tỷ lệ tăng trưởng
      const revenueGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.totalRevenue,
        previousData.totalRevenue,
      );
      const ordersGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.totalOrders,
        previousData.totalOrders,
      );
      const customersGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.newCustomers,
        previousData.newCustomers,
      );

      const result = {
        ...currentData,
        period: query.period,
        previousPeriod: {
          totalRevenue: previousData.totalRevenue,
          totalOrders: previousData.totalOrders,
          newCustomers: previousData.newCustomers,
          revenueGrowth,
          ordersGrowth,
          customersGrowth,
        },
      };

      return plainToInstance(ReportOverviewResponseDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tổng quan báo cáo: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_OVERVIEW_FAILED,
        `Lỗi khi lấy tổng quan báo cáo: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ sản phẩm
   * Tương tự như R-Point API
   */
  async getProductsChartData(
    userId: number,
    query: ProductsChartQueryDto,
  ): Promise<ProductsChartResponseDto> {
    try {
      this.logger.log(
        `Lấy dữ liệu biểu đồ sản phẩm cho user ${userId} với query: ${JSON.stringify(query)}`,
      );

      // Xử lý thời gian mặc định giống R-Point API
      const now = Math.floor(Date.now() / 1000); // Current timestamp in seconds
      const endTimestamp = query.end ? this.normalizeTimestamp(query.end) : now;

      let beginTimestamp: number;
      if (query.begin) {
        beginTimestamp = this.normalizeTimestamp(query.begin);
      } else {
        // Mặc định lấy 30 ngày trước
        beginTimestamp = now - 30 * 24 * 60 * 60; // 30 days ago
      }

      const beginDate = new Date(beginTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);

      // Convert to string format for repository
      const startDate = beginDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Lấy dữ liệu từ repository (sử dụng top selling products logic)
      const data = await this.businessReportRepository.getTopSellingProducts(
        userId,
        query,
      );

      // Tạo summary
      const summary =
        this.businessReportHelper.createProductsChartSummary(data);

      const result = {
        data: data.map(
          (item: {
            productId: any;
            productName: any;
            categoryName: any;
            revenue: number;
            quantitySold: number;
            ordersCount: number;
            imageUrl: any;
          }) => {
            return {
              productId: item.productId,
              productName: item.productName,
              categoryName: item.categoryName,
              revenue: item.revenue,
              quantitySold: item.quantitySold,
              ordersCount: item.ordersCount,
              averagePrice:
                item.ordersCount > 0 ? item.revenue / item.quantitySold : 0,
              imageUrl: item.imageUrl,
            };
          },
        ),
        summary,
      };

      return plainToInstance(ProductsChartResponseDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu biểu đồ sản phẩm: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_PRODUCTS_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm bán chạy
   * Tương tự như R-Point API
   */
  async getTopSellingProducts(
    userId: number,
    query: TopSellingProductsQueryDto,
  ): Promise<TopSellingProductsResponseDto> {
    try {
      this.logger.log(
        `Lấy danh sách sản phẩm bán chạy cho user ${userId} với query: ${JSON.stringify(query)}`,
      );

      // Xử lý thời gian mặc định giống R-Point API
      const now = Math.floor(Date.now() / 1000); // Current timestamp in seconds
      const endTimestamp = query.end ?? now;

      let beginTimestamp: number;
      if (query.begin) {
        beginTimestamp = query.begin;
      } else {
        // Mặc định lấy 30 ngày trước
        beginTimestamp = now - 30 * 24 * 60 * 60; // 30 days ago
      }

      const beginDate = new Date(beginTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);

      // Convert to string format for repository
      const startDate = beginDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getTopSellingProducts(
        userId,
        {
          ...query,
          startDate,
          endDate: endDateStr,
        },
      );

      const period = `${startDate} đến ${endDateStr}`;

      const result = {
        data,
        meta: {
          totalItems: data.length,
          period,
        },
      };

      return plainToInstance(TopSellingProductsResponseDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm bán chạy: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_TOP_SELLING_PRODUCTS_FAILED,
        `Lỗi khi lấy danh sách sản phẩm bán chạy: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách khách hàng tiềm năng
   */
  async getPotentialCustomers(
    userId: number,
    query: PotentialCustomersQueryDto,
  ): Promise<PotentialCustomersResponseDto> {
    try {
      this.logger.log(
        `Lấy danh sách khách hàng tiềm năng cho user ${userId} với query: ${JSON.stringify(query)}`,
      );

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getPotentialCustomers(
        userId,
        query,
      );

      const averagePotentialScore =
        data.length > 0
          ? data.reduce((sum, customer) => sum + customer.potentialScore, 0) /
            data.length
          : 0;

      const result = {
        data,
        meta: {
          totalItems: data.length,
          averagePotentialScore: Math.round(averagePotentialScore * 10) / 10,
        },
      };

      return plainToInstance(PotentialCustomersResponseDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách khách hàng tiềm năng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_POTENTIAL_CUSTOMERS_FAILED,
        `Lỗi khi lấy danh sách khách hàng tiềm năng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đường business
   */
  async getLineChartData(
    userId: number,
    query: BusinessLineChartQueryDto,
  ): Promise<BusinessLineChartResponseDto> {
    try {
      this.logger.log(
        `Lấy dữ liệu biểu đồ đường business cho user ${userId} với query: ${JSON.stringify(query)}`,
      );

      // Xử lý thời gian mặc định
      const now = Math.floor(Date.now() / 1000); // Current timestamp in seconds
      const endTimestamp = query.end ? this.normalizeTimestamp(query.end) : now;

      let beginTimestamp: number;
      if (query.begin) {
        beginTimestamp = this.normalizeTimestamp(query.begin);
      } else {
        // Lấy từ dữ liệu đầu tiên của user (có thể từ orders hoặc products)
        // Mặc định lấy 30 ngày trước nếu không có dữ liệu
        beginTimestamp = now - 30 * 24 * 60 * 60; // 30 days ago
      }

      const beginDate = new Date(beginTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);
      const period = this.calculatePeriod(beginDate, endDate);
      const chartType = query.type ?? BusinessChartDataType.ORDER;

      // Lấy dữ liệu từ repository - hỗ trợ tất cả types từ customers-chart và sales-chart
      let data: Record<string, number>;

      // Xử lý các types từ customers-chart
      if (
        [
          BusinessChartDataType.NEW_CUSTOMERS,
          BusinessChartDataType.RETURNING_CUSTOMERS,
          BusinessChartDataType.TOTAL_CUSTOMERS,
        ].includes(chartType as any)
      ) {
        data = await this.businessReportRepository.getCustomersLineChartData(
          userId,
          {
            beginTimestamp,
            endTimestamp,
            chartType: chartType as any, // Cast to CustomersChartDataType
            period,
          },
        );
      }
      // Xử lý các types từ sales-chart
      else if (
        [
          BusinessChartDataType.REVENUE,
          BusinessChartDataType.AVERAGE_ORDER_VALUE,
        ].includes(chartType as any)
      ) {
        // Map BusinessChartDataType sang SalesChartDataType tương ứng
        const salesType =
          chartType === BusinessChartDataType.REVENUE
            ? 'REVENUE'
            : chartType === BusinessChartDataType.AVERAGE_ORDER_VALUE
              ? 'AVERAGE_ORDER_VALUE'
              : 'REVENUE';

        data = await this.businessReportRepository.getLineChartData(userId, {
          beginTimestamp,
          endTimestamp,
          chartType: salesType as any,
          period,
        });
      }
      // Xử lý các types gốc của line-chart
      else {
        data = await this.businessReportRepository.getLineChartData(userId, {
          beginTimestamp,
          endTimestamp,
          chartType,
          period,
        });
      }

      return {
        data,
        period,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu biểu đồ đường business: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_ORDERS_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ đường business: ${error.message}`,
      );
    }
  }

  /**
   * Helper function để normalize timestamp (tự động detect milliseconds vs seconds)
   * DTO đã convert string date thành milliseconds, nên cần convert về seconds
   */
  private normalizeTimestamp(timestamp: number): number {
    // Nếu timestamp có 13 chữ số thì là milliseconds, convert về seconds
    if (timestamp.toString().length === 13) {
      return Math.floor(timestamp / 1000);
    }
    // Nếu timestamp có 10 chữ số thì đã là seconds
    return timestamp;
  }

  /**
   * Tính toán period dựa trên khoảng thời gian cho Business Chart
   */
  private calculatePeriod(
    beginDate: Date,
    endDate: Date,
  ): BusinessChartPeriodType {
    const diffMs = endDate.getTime() - beginDate.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffDays <= 2) {
      return BusinessChartPeriodType.HOUR;
    } else if (diffDays <= 7) {
      return BusinessChartPeriodType.DAY;
    } else if (diffDays <= 90) {
      return BusinessChartPeriodType.WEEK;
    } else if (diffDays <= 730) {
      return BusinessChartPeriodType.MONTH;
    } else {
      return BusinessChartPeriodType.YEAR;
    }
  }
}
