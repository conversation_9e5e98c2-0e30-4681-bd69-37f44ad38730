import { ConfigService, ConfigType, GoogleConfig } from '@/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { GoogleOAuthService } from '@shared/services/google/auth/google-oauth.service';
import { GoogleDriveService } from '@shared/services/google/drive/google-drive.service';
import { GOOGLE_ERROR_CODES } from '@shared/services/google/exceptions/google.exception';
import { randomUUID } from 'crypto';
import {
  GooglePickerConfigDto,
  GooglePickerSessionData,
  GooglePickerUserInfoDto,
  createDefaultGooglePickerConfig,
  isSessionExpired
} from '../dto/google-picker-session.dto';

/**
 * Interface cho Google Picker configuration
 */
export interface GooglePickerConfig {
  clientId: string;
  accessToken?: string;
  appId?: string;
  apiKey?: string;
}

/**
 * Interface cho Google OAuth tokens
 */
export interface GoogleTokens {
  access_token: string;
  refresh_token?: string;
  id_token?: string;
  expiry_date?: number;
  token_type?: string;
  scope?: string;
}

/**
 * Interface cho user info từ Google
 */
export interface GoogleUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
}

/**
 * Service xử lý Google Picker và tích hợp với Google Drive
 * Sử dụng các shared services có sẵn
 */
@Injectable()
export class GooglePickerService {
  private readonly logger = new Logger(GooglePickerService.name);

  private readonly clientId: string;
  private readonly appId: string;
  private readonly redirectUri: string;
  private readonly apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly googleOAuthService: GoogleOAuthService,
    private readonly googleDriveService: GoogleDriveService,
  ) {
    // Lấy config từ ConfigService
    const googleConfig = this.configService.getConfig<GoogleConfig>(ConfigType.Google);
    this.clientId = googleConfig.clientId;
    this.appId = googleConfig.appId;
    this.redirectUri = googleConfig.redirectUri;
    this.apiKey = googleConfig.apiKey;
  }

  /**
   * Tạo URL để user click vào và xác thực với Google
   * @returns Authorization URL
   */
  getAuthorizationUrl(endpointCallback?: string): { url: string; state: string } {
    try {
      this.logger.log('Generating Google OAuth authorization URL for Picker');

      // Scopes cho Google Picker
      const scopes = [
        'https://www.googleapis.com/auth/drive.readonly', // Chỉ đọc file từ Drive
        'https://www.googleapis.com/auth/drive.metadata.readonly', // Đọc metadata
        'openid', // OpenID Connect
        'email', // Email của user
        'profile', // Profile của user
      ];

      // Tạo state token để xác thực callback
      const state = randomUUID().toString();

      // Tạo redirectUri an toàn với kiểm tra kiểu dữ liệu
      let finalRedirectUri: string;
      try {
        if (endpointCallback && typeof endpointCallback === 'string') {
          finalRedirectUri = `${String(this.redirectUri)}${String(endpointCallback)}`;
        } else {
          this.logger.warn('endpointCallback is not a valid string, using default redirectUri');
          finalRedirectUri = String(this.redirectUri);
        }

        this.logger.debug('Final redirect URI:', finalRedirectUri);
      } catch (error) {
        this.logger.error('Error creating redirect URI:', error);
        finalRedirectUri = String(this.redirectUri);
      }

      const authUrl = this.googleOAuthService.generateAuthUrl(scopes, state, finalRedirectUri);

      this.logger.log('Generated authorization URL successfully');
      return { url: authUrl, state };
    } catch (error) {
      this.logger.error(`Error generating authorization URL: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Xử lý callback từ Google, đổi authorization code lấy tokens và user info
   * @param code Authorization code từ Google
   * @param endpointCallback Endpoint callback để tạo redirect URI
   * @returns Tokens và user info
   */
  async exchangeCodeForTokens(code: string, endpointCallback?: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
    userInfo: GooglePickerUserInfoDto;
  }> {
    try {
      this.logger.log('Processing Google OAuth callback for Picker');

      if (!code) {
        throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      }

      const redirectUri = endpointCallback
        ? `${this.redirectUri}${endpointCallback}`
        : this.redirectUri;

      // Sử dụng GoogleOAuthService để lấy tokens
      const tokens = await this.googleOAuthService.getToken(code, redirectUri);

      // Lấy thông tin user
      const userInfo = await this.googleOAuthService.getUserInfo();

      return {
        access_token: tokens.access_token!,
        refresh_token: tokens.refresh_token!,
        expires_in: tokens.expiry_date ? Math.floor((tokens.expiry_date - Date.now()) / 1000) : 3600,
        userInfo: {
          id: userInfo.id,
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture || ''
        }
      };
    } catch (error) {
      this.logger.error(`Error handling OAuth callback: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
    }
  }

  /**
   * Legacy method - giữ để backward compatibility
   * @deprecated Use exchangeCodeForTokens instead
   */
  async handleCallback(code: string, endpointCallback: string): Promise<GoogleTokens> {
    const result = await this.exchangeCodeForTokens(code, endpointCallback);
    return {
      access_token: result.access_token,
      refresh_token: result.refresh_token,
      expiry_date: Date.now() + (result.expires_in * 1000)
    };
  }

  /**
   * Refresh access token sử dụng refresh token
   * @param refreshToken Refresh token đã lưu
   * @returns New access token với expires_in
   */
  async refreshAccessToken(refreshToken: string): Promise<{
    access_token: string;
    expires_in: number;
    expiry_date?: number;
  }> {
    try {
      this.logger.log('Refreshing Google access token');

      // Sử dụng GoogleOAuthService để refresh token
      const tokens = await this.googleOAuthService.refreshAccessToken(refreshToken);

      this.logger.log('Successfully refreshed access token');

      const expiryDate = tokens.expiry_date || (Date.now() + 3600 * 1000);
      const expiresIn = Math.floor((expiryDate - Date.now()) / 1000);

      return {
        access_token: tokens.access_token!,
        expires_in: expiresIn,
        expiry_date: expiryDate,
      };
    } catch (error) {
      this.logger.error(`Error refreshing access token: ${error.message}`, error.stack);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
    }
  }

  /**
   * Lấy config cho Google Picker với DTO mới
   * @param accessToken Access token (optional, có thể lấy từ session)
   * @returns Picker configuration DTO
   */
  getPickerConfigDto(accessToken?: string): GooglePickerConfigDto {
    try {
      this.logger.log('Getting Google Picker configuration DTO');

      const defaultConfig = createDefaultGooglePickerConfig();

      const config: GooglePickerConfigDto = {
        clientId: this.clientId,
        apiKey: this.apiKey,
        appId: this.appId,
        accessToken: accessToken,
        ...defaultConfig
      } as GooglePickerConfigDto;

      this.logger.log('Successfully retrieved picker configuration DTO');
      return config;
    } catch (error) {
      this.logger.error(`Error getting picker config DTO: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Legacy method - giữ để backward compatibility
   * @deprecated Use getPickerConfigDto instead
   */
  getPickerConfig(accessToken?: string): GooglePickerConfig {
    try {
      this.logger.log('Getting Google Picker configuration (legacy)');

      const config: GooglePickerConfig = {
        clientId: this.clientId,
        accessToken: accessToken,
        appId: this.appId,
        apiKey: this.apiKey,
      };

      this.logger.log('Successfully retrieved picker configuration (legacy)');
      return config;
    } catch (error) {
      this.logger.error(`Error getting picker config: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Validate access token
   * @param accessToken Access token to validate
   * @returns Token info if valid
   */
  async validateAccessToken(accessToken: string): Promise<{
    valid: boolean;
    expiry_date?: number;
    scope?: string;
  }> {
    try {
      this.logger.log(`Validating Google access token: ${accessToken ? 'Present' : 'Missing'}`);

      // Sử dụng GoogleOAuthService để validate token
      // Note: GoogleOAuthService không có validateToken method,
      // nên chúng ta sẽ thử getUserInfo để test token
      await this.googleOAuthService.getUserInfo();

      this.logger.log('Access token is valid');
      return { valid: true };
    } catch (error) {
      this.logger.warn(`Access token validation failed: ${error.message}`);
      return { valid: false };
    }
  }

  /**
   * Revoke access token
   * @param accessToken Access token to revoke
   */
  async revokeAccessToken(accessToken: string): Promise<void> {
    try {
      this.logger.log(`Revoking Google access token: ${accessToken ? 'Present' : 'Missing'}`);

      // Note: GoogleOAuthService không có revokeToken method
      // Chúng ta sẽ log warning và return success
      this.logger.warn('Token revocation not implemented in GoogleOAuthService');
      this.logger.log('Token revocation skipped');
    } catch (error) {
      this.logger.error(`Error revoking access token: ${error.message}`, error.stack);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
    }
  }

  /**
   * Lấy thông tin file từ Google Drive sử dụng file ID
   * @param fileId Google Drive file ID
   * @param accessToken Access token
   * @returns File information
   */
  async getFileInfo(fileId: string, accessToken: string): Promise<any> {
    try {
      this.logger.log(`Getting file info for file ID: ${fileId}`);

      // Sử dụng GoogleDriveService với accessToken
      const fileInfo = await this.googleDriveService.getFileInfo(accessToken, fileId);

      this.logger.log(`Successfully retrieved file info for: ${fileInfo.name}`);
      return fileInfo;
    } catch (error) {
      this.logger.error(`Error getting file info: ${error.message}`, error.stack);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_DRIVE_FILE_NOT_FOUND);
    }
  }

  /**
   * Download file content từ Google Drive
   * @param fileId Google Drive file ID
   * @param accessToken Access token
   * @returns File content as buffer
   */
  async downloadFile(fileId: string, accessToken: string): Promise<Buffer> {
    try {
      this.logger.log(`Downloading file with ID: ${fileId}`);

      // Sử dụng GoogleDriveService với accessToken
      const fileContent = await this.googleDriveService.downloadFile(accessToken, fileId);

      this.logger.log(`Successfully downloaded file: ${fileId}`);
      return fileContent;
    } catch (error) {
      this.logger.error(`Error downloading file: ${error.message}`, error.stack);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_DRIVE_DOWNLOAD_FAILED);
    }
  }
}
