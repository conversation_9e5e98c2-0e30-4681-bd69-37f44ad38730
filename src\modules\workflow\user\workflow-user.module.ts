import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { Workflow, Node, Connection, Execution, NodeDefinition } from '../entities';
import { WorkflowUserController, NodeDefinitionUserController, NodeUserController, ConnectionUserController, ExecutionUserController } from './controllers';
import { WorkflowUserService, NodeDefinitionUserService, NodeUserService, ConnectionUserService, ExecutionUserService } from './services';
import { WorkflowRepository, NodeDefinitionRepository, NodeRepository, ConnectionRepository } from '../repositories';
import { WorkflowValidationHelper, NodeDefinitionValidationHelper } from '../helpers';

/**
 * Module quản lý workflow cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      NodeDefinition,
    ]),
    ServicesModule,
  ],
  controllers: [
    WorkflowUserController,
    NodeDefinitionUserController,
    NodeUserController,
    ConnectionUserController,
    ExecutionUserController,
  ],
  providers: [
    WorkflowUserService,
    NodeDefinitionUserService,
    NodeUserService,
    ConnectionUserService,
    ExecutionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    NodeRepository,
    ConnectionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
  ],
  exports: [
    TypeOrmModule,
    WorkflowUserService,
    NodeDefinitionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
  ],
})
export class WorkflowUserModule {}
