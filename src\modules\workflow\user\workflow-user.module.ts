import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { ServicesModule } from '@shared/services/services.module';
import { Workflow, Node, Connection, Execution, NodeDefinition } from '../entities';
import { WorkflowUserController, NodeDefinitionUserController, NodeUserController, ConnectionUserController, ExecutionUserController } from './controllers';
import { WorkflowUserService, NodeDefinitionUserService, NodeUserService, ConnectionUserService, ExecutionUserService } from './services';
import { WorkflowRepository, NodeDefinitionRepository, NodeRepository, ConnectionRepository } from '../repositories';
import { WorkflowValidationHelper, NodeDefinitionValidationHelper } from '../helpers';

/**
 * Module quản lý workflow cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      NodeDefinition,
    ]),
    // Import ClientsModule để có thể sử dụng BE_WORKER_SERVICE
    ClientsModule.registerAsync([
      {
        name: 'BE_WORKER_SERVICE',
        useFactory: (configService: ConfigService) => {
          const redisUrl = new URL(configService.get<string>('REDIS_URL') || 'redis://localhost:6379');

          return {
            transport: Transport.REDIS,
            options: {
              host: redisUrl.hostname,
              port: parseInt(redisUrl.port, 10) || 6379,
              password: redisUrl.password,
              retryAttempts: 5,
              retryDelay: 2000,
              tls:
                redisUrl.protocol === 'rediss:'
                  ? { rejectUnauthorized: false }
                  : undefined,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
    ServicesModule,
  ],
  controllers: [
    WorkflowUserController,
    NodeDefinitionUserController,
    NodeUserController,
    ConnectionUserController,
    ExecutionUserController,
  ],
  providers: [
    WorkflowUserService,
    NodeDefinitionUserService,
    NodeUserService,
    ConnectionUserService,
    ExecutionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    NodeRepository,
    ConnectionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
  ],
  exports: [
    TypeOrmModule,
    WorkflowUserService,
    NodeDefinitionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
  ],
})
export class WorkflowUserModule {}
