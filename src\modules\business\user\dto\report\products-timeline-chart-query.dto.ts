import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Enum cho loại dữ liệu biểu đồ timeline sản phẩm
 */
export enum ProductsTimelineChartDataType {
  PRODUCTS_COUNT = 'PRODUCTS_COUNT',
  TOTAL_REVENUE = 'TOTAL_REVENUE',
  TOTAL_QUANTITY = 'TOTAL_QUANTITY',
}

/**
 * DTO cho query parameters của API biểu đồ thống kê sản phẩm theo thời gian tạo
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class ProductsTimelineChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: ProductsTimelineChartDataType,
    example: ProductsTimelineChartDataType.PRODUCTS_COUNT,
    required: false,
    default: ProductsTimelineChartDataType.PRODUCTS_COUNT,
  })
  @IsOptional()
  @IsEnum(ProductsTimelineChartDataType)
  type?: ProductsTimelineChartDataType;
}
