import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WorkflowResponseDto } from './workflow-response.dto';
import { NodeResponseDto } from './node-response.dto';
import { ConnectionResponseDto } from './connection-response.dto';

/**
 * DTO response cho workflow detail - bao gồm workflow info + nodes + connections
 * Sử dụng cho API getWorkflowById để trả về đầy đủ thông tin trong 1 call
 */
export class WorkflowDetailResponseDto extends WorkflowResponseDto {
  @ApiProperty({
    description: 'Danh sách nodes trong workflow',
    type: [NodeResponseDto],
    example: [
      {
        id: 'node-1',
        name: 'HTTP Request',
        typeName: 'http-request',
        position: { x: 100, y: 200 },
        properties: [
          {
            name: 'url',
            displayName: 'URL',
            type: 'string',
            value: 'https://api.example.com'
          }
        ]
      }
    ]
  })
  @Expose()
  @Type(() => NodeResponseDto)
  nodes: NodeResponseDto[];

  @ApiProperty({
    description: 'Danh sách connections trong workflow',
    type: [ConnectionResponseDto],
    example: [
      {
        id: 'connection-1',
        sourceNodeId: 'node-1',
        targetNodeId: 'node-2',
        sourceOutput: 'main',
        targetInput: 'main'
      }
    ]
  })
  @Expose()
  @Type(() => ConnectionResponseDto)
  connections: ConnectionResponseDto[];
}
