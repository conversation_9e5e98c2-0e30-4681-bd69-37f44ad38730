import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Enum cho loại biểu đồ
 */
export enum ChartDataType {
  TRANSACTION = 'TRANSACTION',
  AMOUNT = 'AMOUNT',
  POINT = 'POINT',
}

/**
 * DTO cho truy vấn biểu đồ dashboard
 */
export class DashboardChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: ChartDataType,
    example: ChartDataType.TRANSACTION,
    required: false,
    default: ChartDataType.TRANSACTION,
  })
  @IsOptional()
  @IsEnum(ChartDataType)
  type?: ChartDataType;
}
