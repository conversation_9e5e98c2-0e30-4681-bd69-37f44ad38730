import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, Length, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { IWorkflowSettings } from '../../interfaces';

/**
 * DTO cho việc tạo workflow mới
 */
export class CreateWorkflowDto {
  @ApiProperty({
    description: 'Tên workflow',
    example: 'Workflow xử lý đơn hàng tự động',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @Length(1, 255, { message: 'Tên workflow phải từ 1 đến 255 ký tự' })
  name: string;

  @ApiPropertyOptional({
    description: 'Cấu hình workflow',
    example: {
      notification_type: 'email',
      timeout: 3600,
      retry_policy: {
        enabled: true,
        max_retries: 3,
        delay: 1000
      },
      variables: {
        api_key: 'secret_key',
        base_url: 'https://api.example.com'
      }
    },
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  settings?: IWorkflowSettings | null;
}
