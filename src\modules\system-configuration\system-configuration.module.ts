import { Global, Module, forwardRef } from '@nestjs/common';
import { SystemConfigurationAdminModule } from './admin/system-configuration-admin.module';

/**
 * Module chính cho system configuration
 * ServicesModule không cần import vì đã là @Global()
 * RedisService, S3Service đã available globally
 */
@Global()
@Module({
    imports: [
        forwardRef(() => SystemConfigurationAdminModule), // Sử dụng forwardRef để tránh circular dependency
    ],
    providers: [
        // Không có providers riêng, tất cả được handle trong SystemConfigurationAdminModule
    ],
    exports: [
        forwardRef(() => SystemConfigurationAdminModule), // Export admin module với forwardRef
    ],
})
export class SystemConfigurationModule { }
