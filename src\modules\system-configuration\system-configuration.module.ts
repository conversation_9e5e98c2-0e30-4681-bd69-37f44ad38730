import { Global, Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from './entities/system-configuration.entity';
import { ContractTemplateService } from './services/contract-template.service';
import { SystemConfigurationAdminModule } from './admin/system-configuration-admin.module';

/**
 * Module chính cho system configuration
 * ServicesModule không cần import vì đã là @Global()
 * RedisService, S3Service đã available globally
 */
@Global()
@Module({
    imports: [
        forwardRef(() => SystemConfigurationAdminModule), // Sử dụng forwardRef để tránh circular dependency
    ],
    providers: [
        ContractTemplateService, // Chỉ giữ lại service riêng của module này
    ],
    exports: [
        forwardRef(() => SystemConfigurationAdminModule), // Export admin module với forwardRef
        ContractTemplateService,
    ],
})
export class SystemConfigurationModule {}
