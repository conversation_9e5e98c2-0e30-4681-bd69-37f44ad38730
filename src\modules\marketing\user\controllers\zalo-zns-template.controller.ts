import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  getSchemaPath,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloZnsService } from '../services/zalo-zns.service';
import { ZaloZnsTemplate } from '../entities';
import {
  RegisterZnsTemplateDto,
  CreateZnsTemplateDraftDto,
  ZnsTemplateQueryDto,
  ZnsTemplateResponseDto,
  BulkDeleteZnsTemplatesDto,
  BulkDeleteZnsTemplatesResponseDto,
} from '../dto/zalo';

/**
 * Controller xử lý tất cả API liên quan đến Zalo ZNS Templates
 * Bao gồm quản lý templates, đăng ký template mới và đồng bộ từ Zalo API
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ZNS_TEMPLATE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/zns')
export class ZaloZnsTemplateController {
  constructor(private readonly zaloZnsService: ZaloZnsService) {}

  /**
   * Lấy tất cả ZNS templates từ database
   */
  @Get('templates/all')
  @ApiOperation({
    summary: 'Lấy tất cả ZNS templates từ database',
    description: `Lấy tất cả các ZNS templates của người dùng từ database:
    - Phân trang với page và limit
    - Tìm kiếm theo tên template
    - Filter theo trạng thái (pending, approved, rejected)
    - Filter theo loại template (TRANSACTION, PROMOTION, OTP)
    - Filter theo integrationId (nếu cần)
    - Thông tin chi tiết: parameters, sample content`,
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: 'Tìm kiếm theo tên template',
    example: 'OTP',
    required: false,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter theo trạng thái template',
    enum: ['pending', 'approved', 'rejected', 'all'],
    example: 'approved',
    required: false,
  })
  @ApiQuery({
    name: 'templateTag',
    description: 'Filter theo tag template',
    enum: ['TRANSACTION', 'CUSTOMER_CARE', 'PROMOTION'],
    example: 'TRANSACTION',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sắp xếp theo trường',
    enum: ['templateName', 'status', 'createdAt', 'updatedAt'],
    example: 'createdAt',
    required: false,
  })
  @ApiQuery({
    name: 'sortDirection',
    description: 'Hướng sắp xếp',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiQuery({
    name: 'integrationId',
    description: 'Filter theo Integration ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZaloZnsTemplate) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 401, description: 'Chưa xác thực' })
  async getAllTemplates(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZnsTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsTemplate>>> {
    const result = await this.zaloZnsService.getAllZnsTemplates(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(result, 'Lấy tất cả template ZNS thành công');
  }

  /**
   * Lấy danh sách template ZNS từ Zalo API
   */
  @Get('templates/zalo-api/:integrationId')
  @ApiOperation({ summary: 'Lấy danh sách template ZNS từ Zalo API' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách template ZNS từ Zalo API thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      templateId: {
                        type: 'string',
                        description: 'ID template',
                      },
                      templateName: {
                        type: 'string',
                        description: 'Tên template',
                      },
                      status: {
                        type: 'string',
                        description: 'Trạng thái template',
                      },
                      createdTime: {
                        type: 'number',
                        description: 'Thời gian tạo',
                      },
                      templateQuality: {
                        type: 'string',
                        description: 'Chất lượng template',
                      },
                      reason: {
                        type: 'string',
                        description: 'Lý do trạng thái',
                      },
                      listParams: {
                        type: 'array',
                        description: 'Danh sách tham số',
                      },
                      listButtons: {
                        type: 'array',
                        description: 'Danh sách button',
                      },
                      templateTag: {
                        type: 'string',
                        description: 'Tag template',
                      },
                      price: { type: 'number', description: 'Giá template' },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: {
                      type: 'number',
                      description: 'Tổng số template',
                    },
                    itemCount: {
                      type: 'number',
                      description: 'Số lượng template trên trang hiện tại',
                    },
                    itemsPerPage: {
                      type: 'number',
                      description: 'Số lượng template trên mỗi trang',
                    },
                    totalPages: {
                      type: 'number',
                      description: 'Tổng số trang',
                    },
                    currentPage: {
                      type: 'number',
                      description: 'Trang hiện tại',
                    },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getTemplatesFromZaloApi(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Query() queryDto: ZnsTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const result =
      await this.zaloZnsService.getZnsTemplatesFromZaloApiByIntegrationId(
        user.id,
        integrationId,
        queryDto,
      );
    return ApiResponseDto.success(
      result,
      'Lấy danh sách template ZNS từ Zalo API thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database
   */
  @Get('templates/:integrationId/:templateId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết template ZNS từ database' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template ZNS',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async getTemplateDetail(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId', ParseIntPipe) templateId: number,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result =
      await this.zaloZnsService.getZnsTemplateDetailByIntegrationId(
        user.id,
        integrationId,
        templateId,
      );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết template ZNS thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ database (không cần integrationId)
   */
  @Get('template/:templateId')
  @ApiOperation({
    summary:
      'Lấy thông tin chi tiết template ZNS từ database (không cần integrationId)',
    description: `Lấy thông tin chi tiết template ZNS từ database dựa trên templateId:
    - Không cần truyền integrationId
    - Tự động tìm template thuộc về user hiện tại
    - Trả về đầy đủ thông tin template: tên, nội dung, parameters, trạng thái
    - Bao gồm thông tin về quality, tag, price và các thông số khác`,
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template ZNS trong database',
    example: 123,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền truy cập template này',
  })
  async getTemplateDetailById(
    @CurrentUser() user: JwtPayload,
    @Param('templateId', ParseIntPipe) templateId: number,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.getZnsTemplateDetailById(
      user.id,
      templateId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết template ZNS thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết template ZNS từ Zalo API
   */
  @Get('templates/zalo-api/:integrationId/:templateId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết template ZNS từ Zalo API' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template ZNS từ Zalo',
    example: '123456',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết template ZNS từ Zalo API thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                templateId: { type: 'string', description: 'ID template' },
                templateName: { type: 'string', description: 'Tên template' },
                status: { type: 'string', description: 'Trạng thái template' },
                reason: { type: 'string', description: 'Lý do trạng thái' },
                listParams: {
                  type: 'array',
                  description: 'Danh sách tham số',
                  items: {
                    type: 'object',
                    properties: {
                      name: { type: 'string', description: 'Tên thuộc tính' },
                      require: {
                        type: 'boolean',
                        description: 'Tính bắt buộc',
                      },
                      type: {
                        type: 'string',
                        description: 'Định dạng validate',
                      },
                      maxLength: {
                        type: 'number',
                        description: 'Số ký tự tối đa',
                      },
                      minLength: {
                        type: 'number',
                        description: 'Số ký tự tối thiểu',
                      },
                      acceptNull: {
                        type: 'boolean',
                        description: 'Có thể nhận giá trị rỗng',
                      },
                    },
                  },
                },
                listButtons: {
                  type: 'array',
                  description: 'Danh sách button',
                  items: {
                    type: 'object',
                    properties: {
                      type: { type: 'string', description: 'Loại button' },
                      title: { type: 'string', description: 'Tiêu đề button' },
                      payload: {
                        type: 'string',
                        description: 'Payload của button',
                      },
                      image_icon: {
                        type: 'string',
                        description: 'URL hình ảnh icon',
                      },
                    },
                  },
                },
                timeout: {
                  type: 'number',
                  description: 'Thời gian timeout (milliseconds)',
                },
                previewUrl: {
                  type: 'string',
                  description: 'URL hình ảnh preview',
                },
                createdTime: {
                  type: 'number',
                  description: 'Thời gian tạo (Unix timestamp)',
                },
                updatedTime: {
                  type: 'number',
                  description: 'Thời gian cập nhật (Unix timestamp)',
                },
                templateQuality: {
                  type: 'string',
                  description: 'Chất lượng template',
                },
                templateTag: { type: 'string', description: 'Tag template' },
                price: { type: 'number', description: 'Giá template' },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  async getTemplateDetailFromZaloApi(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId') templateId: string,
  ): Promise<ApiResponseDto<any>> {
    const result =
      await this.zaloZnsService.getZnsTemplateDetailFromZaloApiByIntegrationId(
        user.id,
        integrationId,
        templateId,
      );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết template ZNS từ Zalo API thành công',
    );
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS từ Zalo API
   */
  @Get('templates/zalo-api/:integrationId/:templateId/sample-data')
  @ApiOperation({ summary: 'Lấy dữ liệu mẫu của template ZNS từ Zalo API' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template ZNS từ Zalo',
    example: '123456',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu mẫu template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                template_id: { type: 'string', description: 'ID template' },
                sample_data: {
                  type: 'object',
                  description: 'Dữ liệu mẫu cho template',
                  additionalProperties: { type: 'string' },
                },
                preview_content: {
                  type: 'string',
                  description: 'Nội dung template sau khi thay thế dữ liệu mẫu',
                },
                preview_url: {
                  type: 'string',
                  description: 'URL preview (nếu có hình ảnh)',
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  async getTemplateSampleData(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId') templateId: string,
  ): Promise<ApiResponseDto<any>> {
    const result =
      await this.zaloZnsService.getZnsTemplateSampleDataByIntegrationId(
        user.id,
        integrationId,
        templateId,
      );
    return ApiResponseDto.success(
      result,
      'Lấy dữ liệu mẫu template ZNS thành công',
    );
  }

  /**
   * Đồng bộ template ZNS từ Zalo API sang database
   */
  @Post(':integrationId/templates/sync')
  @ApiOperation({
    summary: 'Đồng bộ template ZNS từ Zalo API sang database',
    description: `
Đồng bộ tất cả template ZNS từ Zalo API vào database local:
- Lấy danh sách template từ Zalo API
- Lấy chi tiết từng template
- Lưu/cập nhật vào database
- Trả về thống kê số lượng template đã đồng bộ

**Lưu ý:**
- Template đã tồn tại sẽ được cập nhật thông tin mới nhất
- Template mới sẽ được thêm vào database
- Chỉ đồng bộ template có trạng thái ENABLE hoặc PENDING_REVIEW
    `,
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Đồng bộ template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                totalTemplates: {
                  type: 'number',
                  description: 'Tổng số template từ Zalo API',
                },
                syncedTemplates: {
                  type: 'number',
                  description: 'Số template đã đồng bộ thành công',
                },
                updatedTemplates: {
                  type: 'number',
                  description: 'Số template đã cập nhật',
                },
                newTemplates: {
                  type: 'number',
                  description: 'Số template mới được thêm',
                },
                skippedTemplates: {
                  type: 'number',
                  description: 'Số template bị bỏ qua',
                },
                errors: {
                  type: 'array',
                  description: 'Danh sách lỗi nếu có',
                  items: {
                    type: 'object',
                    properties: {
                      templateId: { type: 'string' },
                      error: { type: 'string' },
                    },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  @ApiResponse({ status: 500, description: 'Lỗi khi đồng bộ template' })
  async syncTemplatesFromZaloApi(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
  ): Promise<ApiResponseDto<any>> {
    const result =
      await this.zaloZnsService.syncTemplatesFromZaloApiByIntegrationId(
        user.id,
        integrationId,
      );
    return ApiResponseDto.success(
      result,
      'Đồng bộ template ZNS từ Zalo API thành công',
    );
  }

  /**
   * Đồng bộ template ZNS từ Zalo API cho tất cả integration
   */
  @Post('templates/sync-all')
  @ApiOperation({
    summary: 'Đồng bộ template ZNS từ Zalo API cho tất cả integration',
    description: `
Đồng bộ tất cả template ZNS từ Zalo API vào database cho tất cả integration Zalo OA của user:
- Tự động lấy danh sách tất cả integration Zalo OA của user
- Đồng bộ template cho từng integration một cách tuần tự
- Lấy danh sách template từ Zalo API cho mỗi OA
- Lấy chi tiết từng template và lưu/cập nhật vào database
- Trả về thống kê tổng hợp cho tất cả integration

**Ưu điểm:**
- Không cần truyền integrationId, tự động xử lý tất cả
- Xử lý song song để tăng hiệu suất
- Báo cáo chi tiết lỗi cho từng integration
- Thống kê tổng hợp toàn bộ quá trình

**Lưu ý:**
- Template đã tồn tại sẽ được cập nhật thông tin mới nhất
- Template mới sẽ được thêm vào database
- Chỉ đồng bộ template có trạng thái ENABLE hoặc PENDING_REVIEW
- Nếu một integration gặp lỗi, các integration khác vẫn tiếp tục được xử lý
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Đồng bộ template ZNS thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Đồng bộ template ZNS cho tất cả integration thành công',
        },
        result: {
          type: 'object',
          properties: {
            totalIntegrations: { type: 'number', example: 3 },
            processedIntegrations: { type: 'number', example: 2 },
            totalTemplates: { type: 'number', example: 25 },
            syncedTemplates: { type: 'number', example: 20 },
            updatedTemplates: { type: 'number', example: 15 },
            newTemplates: { type: 'number', example: 5 },
            skippedTemplates: { type: 'number', example: 3 },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  integrationId: {
                    type: 'string',
                    example: '123e4567-e89b-12d3-a456-************',
                  },
                  oaId: { type: 'string', example: '1234567890' },
                  error: {
                    type: 'string',
                    example: 'Access token không hợp lệ',
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi đồng bộ template cho tất cả integration',
  })
  async syncAllTemplatesFromZaloApi(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.syncAllTemplatesFromZaloApi(
      user.id,
    );
    return ApiResponseDto.success(
      result,
      'Đồng bộ template ZNS cho tất cả integration thành công',
    );
  }

  /**
   * Đăng ký template ZNS
   */
  @Post(':integrationId/templates')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: false, // Tắt whitelist validation cho endpoint này
      forbidNonWhitelisted: false,
    }),
  )
  @ApiOperation({
    summary: 'Đăng ký template ZNS',
    description: `
Đăng ký template ZNS mới theo chuẩn Zalo API.

**Lưu ý quan trọng:**
- template_name: 10-60 ký tự
- template_type: 1=ZNS tùy chỉnh, 2=ZNS xác thực, 3=ZNS yêu cầu thanh toán, 4=ZNS voucher, 5=ZNS đánh giá dịch vụ
- tag: 1=Transaction, 2=Customer care, 3=Promotion
- layout: Bắt buộc có body, tùy chọn header và footer
- tracking_id: Mã tracking do đối tác tự định nghĩa
- note: Ghi chú kiểm duyệt (1-400 ký tự)

**Param Types:**
- 1: Tên khách hàng (30 ký tự)
- 2: Số điện thoại (15 ký tự)
- 3: Địa chỉ (200 ký tự)
- 4: Mã số (30 ký tự)
- 5: Nhãn tùy chỉnh (30 ký tự)
- 6: Trạng thái giao dịch (30 ký tự)
- 7: Thông tin liên hệ (50 ký tự)
- 8: Giới tính/Danh xưng (5 ký tự)
- 9: Tên sản phẩm/Thương hiệu (200 ký tự)
- 10: Số lượng/Số tiền (20 ký tự)
- 11: Thời gian (20 ký tự)
- 12: OTP (10 ký tự)
- 13: URL (200 ký tự)
- 14: Tiền tệ VNĐ (12 ký tự)
- 15: Bank transfer note (90 ký tự)
    `,
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    description: 'Dữ liệu đăng ký template ZNS',
    examples: {
      'Thông báo đơn hàng cơ bản': {
        summary: 'Template thông báo đơn hàng đơn giản',
        description: 'Template cơ bản với title và paragraph',
        value: {
          template_name: 'Thông báo đơn hàng cơ bản',
          template_type: 1,
          tag: '1',
          layout: {
            body: {
              components: [
                {
                  TITLE: {
                    value: 'Xác nhận đơn hàng',
                  },
                },
                {
                  PARAGRAPH: {
                    value:
                      'Cảm ơn <customer_name> đã mua hàng tại cửa hàng. Đơn hàng <order_code> đã được xác nhận với tổng tiền <total_amount>.',
                  },
                },
              ],
            },
          },
          params: [
            {
              name: 'customer_name',
              type: 1,
              sample_value: 'Nguyễn Văn A',
            },
            {
              name: 'order_code',
              type: 4,
              sample_value: 'DH001',
            },
            {
              name: 'total_amount',
              type: 14,
              sample_value: '500000',
            },
          ],
          tracking_id: 'order_basic_001',
          note: 'Template thông báo đơn hàng cơ bản',
        },
      },
      'Template đơn giản nhất': {
        summary: 'Template chỉ có paragraph',
        description: 'Template ZNS tùy chỉnh đơn giản nhất chỉ có 1 paragraph',
        value: {
          template_name: 'Thông báo đơn giản',
          template_type: 1,
          tag: '2',
          layout: {
            body: {
              components: [
                {
                  PARAGRAPH: {
                    value:
                      'Xin chào <customer_name>, cảm ơn bạn đã sử dụng dịch vụ của chúng tôi.',
                  },
                },
              ],
            },
          },
          params: [
            {
              name: 'customer_name',
              type: 1,
              sample_value: 'Nguyễn Văn A',
            },
          ],
          tracking_id: 'simple_001',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      example: {
        code: 9999,
        message:
          'Lỗi khi tạo template ZNS: Data is invalid | Template tracking id must not be null;Template tag must not be null;Template note must not be null;Template name must not be null;Template type must not be null;Layout must not be null',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  async registerTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Body() registerDto: RegisterZnsTemplateDto,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.registerZnsTemplateByIntegrationId(
      user.id,
      integrationId,
      registerDto,
    );
    return ApiResponseDto.success(result, 'Đăng ký template ZNS thành công');
  }

  /**
   * Tạo template ZNS draft chỉ vào database (không đăng ký lên Zalo)
   */
  @Post(':integrationId/templates/draft')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: false, // Tắt whitelist validation cho endpoint này
      forbidNonWhitelisted: false,
    }),
  )
  @ApiOperation({
    summary: 'Tạo template ZNS draft chỉ vào database',
    description:
      'Tạo template ZNS với trạng thái draft, chỉ lưu vào database mà không đăng ký lên Zalo. Template có thể được đăng ký lên Zalo sau này.',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: CreateZnsTemplateDraftDto,
    description: 'Thông tin template ZNS draft cần tạo',
    examples: {
      'template-draft': {
        summary: 'Template draft cơ bản',
        description: 'Ví dụ tạo template draft với layout đơn giản',
        value: {
          template_name: 'Mẫu thông báo đơn hàng draft',
          template_type: 1,
          tag: 'TRANSACTION',
          layout: {
            header: {
              components: [
                {
                  LOGO: {
                    light: { type: 'IMAGE', media_id: '1ashjkdbkj' },
                    dark: { type: 'IMAGE', media_id: '1ashjkdbkj' },
                  },
                },
              ],
            },
            body: {
              components: [
                { TITLE: { value: 'Xác nhận đơn hàng' } },
                {
                  PARAGRAPH: {
                    value: 'Cảm ơn <n> đã mua hàng tại <shop_name>',
                  },
                },
              ],
            },
          },
          tracking_id: 'draft_order_template_001',
          params: [
            { name: 'n', require: true, type: 'STRING' },
            { name: 'shop_name', require: true, type: 'STRING' },
          ],
          note: 'Template draft cho thông báo đơn hàng',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo template ZNS draft thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZaloZnsTemplate) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Validation failed' },
        errors: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'template_name must be longer than or equal to 10 characters',
          ],
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Integration' })
  async createTemplateDraft(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Body() createDto: CreateZnsTemplateDraftDto,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result =
      await this.zaloZnsService.createZnsTemplateDraftByIntegrationId(
        user.id,
        integrationId,
        createDto,
      );
    return ApiResponseDto.success(result, 'Tạo template ZNS draft thành công');
  }

  /**
   * Cập nhật trạng thái template ZNS
   */
  @Put('templates/:integrationId/:id/status/:status')
  @ApiOperation({ summary: 'Cập nhật trạng thái template ZNS' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của template ZNS trong database',
    example: 1,
  })
  @ApiParam({
    name: 'status',
    description: 'Trạng thái mới của template',
    example: 'approved',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  async updateTemplateStatus(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: string,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result =
      await this.zaloZnsService.updateZnsTemplateStatusByIntegrationId(
        user.id,
        integrationId,
        id,
        status,
      );
    return ApiResponseDto.success(
      result,
      'Cập nhật trạng thái template ZNS thành công',
    );
  }

  /**
   * Cập nhật trạng thái tất cả template ZNS từ tất cả OA của user
   */
  @Put('templates/sync-all-status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái tất cả template ZNS từ Zalo API',
    description:
      'Tự động lấy trạng thái mới nhất từ Zalo API và cập nhật vào database cho tất cả template ZNS của tất cả OA mà user đã tích hợp',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Cập nhật trạng thái tất cả template ZNS thành công',
        },
        data: {
          type: 'object',
          properties: {
            totalIntegrations: {
              type: 'number',
              example: 3,
              description: 'Tổng số Integration Zalo OA',
            },
            processedIntegrations: {
              type: 'number',
              example: 2,
              description: 'Số Integration đã xử lý thành công',
            },
            totalTemplates: {
              type: 'number',
              example: 15,
              description: 'Tổng số template đã kiểm tra',
            },
            updatedTemplates: {
              type: 'number',
              example: 5,
              description: 'Số template đã cập nhật trạng thái',
            },
            skippedTemplates: {
              type: 'number',
              example: 10,
              description: 'Số template bỏ qua (không thay đổi hoặc draft)',
            },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  integrationId: {
                    type: 'string',
                    example: '123e4567-e89b-12d3-a456-************',
                  },
                  oaId: { type: 'string', example: 'oa_123456' },
                  error: {
                    type: 'string',
                    example: 'Không có access token hợp lệ',
                  },
                },
              },
              description: 'Danh sách lỗi xảy ra trong quá trình xử lý',
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi server',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Không thể cập nhật trạng thái template ZNS',
        },
      },
    },
  })
  async updateAllTemplateStatusFromZaloApi(
    @CurrentUser() user: JwtPayload,
  ): Promise<
    ApiResponseDto<{
      totalIntegrations: number;
      processedIntegrations: number;
      totalTemplates: number;
      updatedTemplates: number;
      skippedTemplates: number;
      errors: Array<{ integrationId: string; oaId: string; error: string }>;
    }>
  > {
    const result = await this.zaloZnsService.updateAllTemplateStatusFromZaloApi(
      user.id,
    );
    return ApiResponseDto.success(
      result,
      'Cập nhật trạng thái tất cả template ZNS thành công',
    );
  }

  /**
   * Chỉnh sửa template ZNS (chỉ cho template có trạng thái REJECT)
   */
  @Put(':integrationId/templates/:templateId/edit')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: false, // Tắt whitelist validation cho endpoint này
      forbidNonWhitelisted: false,
    }),
  )
  @ApiOperation({
    summary: 'Chỉnh sửa template ZNS',
    description: `
Chỉnh sửa template ZNS đã bị từ chối (REJECT) theo chuẩn Zalo API.

**Lưu ý quan trọng:**
- Chỉ có thể chỉnh sửa template có trạng thái REJECT (đã bị từ chối)
- Sử dụng API edit template mới của Zalo: /template/edit
- template_name: 10-60 ký tự
- template_type: 1=ZNS tùy chỉnh, 2=ZNS xác thực, 3=ZNS yêu cầu thanh toán, 4=ZNS voucher, 5=ZNS đánh giá dịch vụ
- tag: 1=Transaction, 2=Customer care, 3=Promotion
- layout: Bắt buộc có body, tùy chọn header và footer
- tracking_id: Mã tracking do đối tác tự định nghĩa
- note: Ghi chú kiểm duyệt (1-400 ký tự)

**Param Types:**
- 1: Tên khách hàng (30 ký tự)
- 2: Số điện thoại (15 ký tự)
- 3: Địa chỉ (200 ký tự)
- 4: Mã số (30 ký tự)
- 5: Nhãn tùy chỉnh (30 ký tự)
- 6: Trạng thái giao dịch (30 ký tự)
- 7: Thông tin liên hệ (50 ký tự)
- 8: Giới tính/Danh xưng (5 ký tự)
- 9: Tên sản phẩm/Thương hiệu (200 ký tự)
- 10: Số lượng/Số tiền (20 ký tự)
- 11: Thời gian (20 ký tự)
- 12: OTP (10 ký tự)
- 13: URL (200 ký tự)
- 14: Tiền tệ VNĐ (12 ký tự)
- 15: Bank transfer note (90 ký tự)
    `,
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template ZNS trong database',
    example: 123,
  })
  @ApiBody({
    description: 'Dữ liệu chỉnh sửa template ZNS',
    examples: {
      'Chỉnh sửa template đơn hàng': {
        summary: 'Template chỉnh sửa thông báo đơn hàng',
        description: 'Template cập nhật với layout mới',
        value: {
          template_name: 'Thông báo đơn hàng đã chỉnh sửa',
          template_type: 1,
          tag: '1',
          layout: {
            body: {
              components: [
                {
                  TITLE: {
                    value: 'Xác nhận đơn hàng mới',
                  },
                },
                {
                  PARAGRAPH: {
                    value:
                      'Cảm ơn <customer_name> đã mua hàng tại cửa hàng. Đơn hàng <order_code> đã được xác nhận với tổng tiền <total_amount>. Đơn hàng sẽ được giao trong 2-3 ngày làm việc.',
                  },
                },
              ],
            },
          },
          params: [
            {
              name: 'customer_name',
              type: 1,
              sample_value: 'Nguyễn Văn A',
            },
            {
              name: 'order_code',
              type: 4,
              sample_value: 'DH001',
            },
            {
              name: 'total_amount',
              type: 14,
              sample_value: '500000',
            },
          ],
          tracking_id: 'order_edit_001',
          note: 'Template đã chỉnh sửa theo yêu cầu kiểm duyệt',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Chỉnh sửa template ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsTemplateResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ hoặc template không ở trạng thái REJECT',
    schema: {
      example: {
        code: 9999,
        message: 'Template không ở trạng thái REJECT hoặc dữ liệu không hợp lệ',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template' })
  async editTemplate(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId', ParseIntPipe) templateId: number,
    @Body() editDto: RegisterZnsTemplateDto,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result = await this.zaloZnsService.editZnsTemplateByIntegrationId(
      user.id,
      integrationId,
      templateId,
      editDto,
    );
    return ApiResponseDto.success(result, 'Chỉnh sửa template ZNS thành công');
  }

  /**
   * Xóa nhiều ZNS template cùng lúc
   */
  @Delete('templates/bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều ZNS template cùng lúc',
    description: `Xóa nhiều ZNS template dựa trên danh sách ID từ database:
    - Chỉ xóa template thuộc về user hiện tại
    - Xóa trực tiếp trong database (không gọi Zalo API)
    - Trả về chi tiết kết quả xóa từng template
    - Tối đa 100 template mỗi lần
    - Template đã được sử dụng trong campaign vẫn có thể xóa`,
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa template thành công',
    type: BulkDeleteZnsTemplatesResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Xử lý xong 3 template. Thành công: 2, Thất bại: 1',
        data: {
          totalRequested: 3,
          totalSuccess: 2,
          totalFailed: 1,
          results: [
            {
              id: 1,
              templateName: 'Thông báo đơn hàng',
              templateId: 'template_123',
              success: true,
              message: 'Xóa template thành công',
            },
            {
              id: 2,
              templateName: 'Xác nhận thanh toán',
              templateId: 'template_456',
              success: true,
              message: 'Xóa template thành công',
            },
            {
              id: 3,
              templateName: 'Template lỗi',
              templateId: 'template_789',
              success: false,
              message: 'Lỗi khi xóa template: Database connection error',
              error: 'Database connection error',
            },
          ],
          deletedIds: [1, 2],
          failedIds: [3],
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy template',
  })
  async bulkDeleteTemplates(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteZnsTemplatesDto,
  ): Promise<ApiResponseDto<BulkDeleteZnsTemplatesResponseDto>> {
    const result = await this.zaloZnsService.bulkDeleteZnsTemplates(
      user.id,
      bulkDeleteDto.templateIds,
    );

    return ApiResponseDto.success(
      result,
      `Xử lý xong ${result.totalRequested} template. Thành công: ${result.totalSuccess}, Thất bại: ${result.totalFailed}`,
    );
  }

  /**
   * Kiểm tra trạng thái template ZNS từ Zalo API và cập nhật vào database
   */
  @Put(':integrationId/templates/:templateId/check-status')
  @ApiOperation({
    summary:
      'Kiểm tra trạng thái template ZNS từ Zalo API và cập nhật vào database',
    description:
      'API này sẽ gọi Zalo API để lấy trạng thái mới nhất của template và cập nhật vào database',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template trong database',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Kiểm tra và cập nhật trạng thái template thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Kiểm tra và cập nhật trạng thái template ZNS thành công',
        },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            templateId: { type: 'string', example: 'template_123' },
            templateName: { type: 'string', example: 'Thông báo đơn hàng' },
            status: { type: 'string', example: 'approved' },
            templateQuality: { type: 'string', example: 'HIGH' },
            price: { type: 'number', example: 500 },
            applyTemplateQuota: { type: 'boolean', example: true },
            updatedAt: { type: 'number', example: ************* },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy template ZNS hoặc integration',
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi kiểm tra trạng thái template từ Zalo API',
  })
  async checkTemplateStatusFromZalo(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId') templateId: number,
  ): Promise<ApiResponseDto<ZaloZnsTemplate>> {
    const result =
      await this.zaloZnsService.checkAndUpdateTemplateStatusFromZalo(
        user.id,
        integrationId,
        templateId,
      );
    return ApiResponseDto.success(
      result,
      'Kiểm tra và cập nhật trạng thái template ZNS thành công',
    );
  }

  /**
   * API test để kiểm tra trạng thái template ZNS từ Zalo API (không cập nhật database)
   */
  @Get(':integrationId/templates/:templateId/test-check-status')
  @ApiOperation({
    summary: 'Test kiểm tra trạng thái template ZNS từ Zalo API',
    description:
      'API test để xem thông tin template từ database và Zalo API, không cập nhật database',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'templateId',
    description: 'ID của template trong database',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin template thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Lấy thông tin template ZNS thành công',
        },
        data: {
          type: 'object',
          properties: {
            databaseTemplate: {
              type: 'object',
              description: 'Thông tin template từ database',
            },
            zaloApiTemplate: {
              type: 'object',
              description: 'Thông tin template từ Zalo API',
            },
            statusMapping: {
              type: 'object',
              properties: {
                currentStatus: { type: 'string', example: 'pending' },
                zaloStatus: { type: 'string', example: 'ENABLE' },
                mappedStatus: { type: 'string', example: 'approved' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy template ZNS hoặc integration',
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin template từ Zalo API',
  })
  async testCheckTemplateStatusFromZalo(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('templateId') templateId: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.testCheckTemplateStatusFromZalo(
      user.id,
      integrationId,
      templateId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin template ZNS thành công',
    );
  }
}
