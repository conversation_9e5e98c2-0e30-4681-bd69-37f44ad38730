import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { WorkflowUserModule } from '../user/workflow-user.module';
import { Workflow, Node, Connection, Execution, NodeDefinition } from '../entities';
import { WorkflowAdminController, NodeDefinitionAdminController, NodeAdminController, ConnectionAdminController, ExecutionAdminController } from './controllers';
import { WorkflowAdminService, NodeDefinitionAdminService, NodeAdminService, ConnectionAdminService, ExecutionAdminService } from './services';
import { ConnectionRepository, NodeDefinitionRepository, NodeRepository, WorkflowRepository } from '../repositories';

/**
 * Module quản lý workflow cho admin
 */
@Module({
  imports: [
    // Import WorkflowUserModule để có thể sử dụng shared dependencies
    WorkflowUserModule,
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      NodeDefinition,
    ]),
    ServicesModule,
  ],
  controllers: [
    WorkflowAdminController,
    NodeDefinitionAdminController,
    NodeAdminController,
    ConnectionAdminController,
    ExecutionAdminController,
  ],
  providers: [
    WorkflowAdminService,
    NodeDefinitionAdminService,
    NodeAdminService,
    ConnectionAdminService,
    ExecutionAdminService,
    WorkflowRepository,
    NodeDefinitionRepository,
    NodeRepository,
    ConnectionRepository,
  ],
  exports: [
    TypeOrmModule,
    WorkflowAdminService,
    NodeDefinitionAdminService,
  ],
})
export class WorkflowAdminModule {}
