import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { ServicesModule } from '@shared/services/services.module';
import { WorkflowUserModule } from '../user/workflow-user.module';
import { Workflow, Node, Connection, Execution, NodeDefinition } from '../entities';
import { WorkflowAdminController, NodeDefinitionAdminController, NodeAdminController, ConnectionAdminController, ExecutionAdminController } from './controllers';
import { WorkflowAdminService, NodeDefinitionAdminService, NodeAdminService, ConnectionAdminService, ExecutionAdminService } from './services';
import { ConnectionRepository, NodeDefinitionRepository, NodeRepository, WorkflowRepository } from '../repositories';

/**
 * Module quản lý workflow cho admin
 */
@Module({
  imports: [
    // Import WorkflowUserModule để có thể sử dụng shared dependencies
    WorkflowUserModule,
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      NodeDefinition,
    ]),
    // Import ClientsModule để có thể sử dụng BE_WORKER_SERVICE
    ClientsModule.registerAsync([
      {
        name: 'BE_WORKER_SERVICE',
        useFactory: (configService: ConfigService) => {
          const redisUrl = new URL(configService.get<string>('REDIS_URL') || 'redis://localhost:6379');

          return {
            transport: Transport.REDIS,
            options: {
              host: redisUrl.hostname,
              port: parseInt(redisUrl.port, 10) || 6379,
              password: redisUrl.password,
              retryAttempts: 5,
              retryDelay: 2000,
              tls:
                redisUrl.protocol === 'rediss:'
                  ? { rejectUnauthorized: false }
                  : undefined,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
    ServicesModule,
  ],
  controllers: [
    WorkflowAdminController,
    NodeDefinitionAdminController,
    NodeAdminController,
    ConnectionAdminController,
    ExecutionAdminController,
  ],
  providers: [
    WorkflowAdminService,
    NodeDefinitionAdminService,
    NodeAdminService,
    ConnectionAdminService,
    ExecutionAdminService,
    WorkflowRepository,
    NodeDefinitionRepository,
    NodeRepository,
    ConnectionRepository,
  ],
  exports: [
    TypeOrmModule,
    WorkflowAdminService,
    NodeDefinitionAdminService,
  ],
})
export class WorkflowAdminModule {}
