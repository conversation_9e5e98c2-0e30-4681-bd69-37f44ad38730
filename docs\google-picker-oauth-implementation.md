# Google Picker OAuth Implementation Guide

## 📋 **Tổng quan**

Hướng dẫn chi tiết cách implement Google Picker với OAuth flow sử dụng popup, session storage, DTOs type-safe và script auto-close.

## 🔄 **Luồng hoạt động cập nhật**

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant Controller as GooglePickerController
    participant Session as Session Store
    participant Service as GooglePickerService
    participant Google as Google APIs

    FE->>Controller: GET /auth/google-picker/url
    Controller->>FE: Return { url, state }
    FE->>FE: Open popup with Google OAuth URL
    FE->>Google: User authenticates
    Google->>Controller: Redirect to callback with code
    Controller->>Service: Exchange code for tokens
    Service->>Google: Token exchange
    Controller->>Session: Store session data
    Controller->>FE: Return script to close popup

    FE->>Controller: GET /auth/google-picker/session
    Controller->>Session: Lấy session.googlePicker
    Controller->>Controller: Validate & refresh token if needed
    Controller->>Controller: Get picker config
    Controller->>FE: Return { accessToken, userInfo, config }
    FE->>FE: Initialize Google Picker with data
```

## 🛠️ **Backend Implementation**

### **1. DTOs và Interfaces**

```typescript
// interfaces/session.interface.ts
export interface GooglePickerSessionData {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  userInfo: {
    id: string;
    email: string;
    name: string;
    picture: string;
  };
  state: string;
}

export interface SessionData {
  googlePicker?: GooglePickerSessionData;
}

// dto/google-picker-config.dto.ts
export class GooglePickerConfigDto {
  @ApiProperty({ description: 'Google API Key' })
  apiKey: string;

  @ApiProperty({ description: 'Google Client ID' })
  clientId: string;

  @ApiProperty({ description: 'Google App ID' })
  appId: string;

  @ApiProperty({ description: 'OAuth scopes' })
  scopes: string[];

  @ApiProperty({ description: 'Picker view IDs' })
  viewIds: {
    docs: string;
    images: string;
    videos: string;
    folders: string;
  };

  @ApiProperty({ description: 'Picker features' })
  features: {
    multiselect: boolean;
    nav_hidden: boolean;
    upload: boolean;
  };

  @ApiProperty({ description: 'Locale setting' })
  locale: string;
}

// dto/google-picker-session-response.dto.ts
export class GooglePickerSessionResponseDto {
  @ApiProperty({ description: 'Access token for Google APIs' })
  accessToken: string;

  @ApiProperty({ description: 'User information' })
  userInfo: GooglePickerUserInfoDto;

  @ApiProperty({ description: 'Authentication status' })
  isAuthenticated: boolean;

  @ApiProperty({ description: 'Google Picker configuration' })
  config: GooglePickerConfigDto;

  @ApiProperty({ description: 'Token expiry time', required: false })
  expiresAt?: number;

  @ApiProperty({ description: 'Token expires in seconds', required: false })
  expiresIn?: number;
}
```

### **2. Service - Authorization URL Generation**

```typescript
// google-picker.service.ts
export class GooglePickerService {
  
  getAuthorizationUrl(endpointCallback?: string): { url: string; state: string } {
    try {
      this.logger.log('Generating Google OAuth authorization URL for Picker');

      // Scopes cho Google Picker
      const scopes = [
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.metadata.readonly',
        'openid',
        'email',
        'profile',
      ];

      const state = randomUUID().toString();

      // redirectUri sẽ là endpoint BE để xử lý callback
      let finalRedirectUri: string;
      if (endpointCallback && typeof endpointCallback === 'string') {
        finalRedirectUri = `${this.redirectUri}${endpointCallback}`;
      } else {
        finalRedirectUri = this.redirectUri; // VD: /api/auth/google-picker/callback
      }

      const authUrl = this.googleOAuthService.generateAuthUrl(scopes, state, finalRedirectUri);

      return { url: authUrl, state };
    } catch (error) {
      this.logger.error(`Error generating authorization URL: ${error.message}`);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  async exchangeCodeForTokens(code: string) {
    try {
      // Exchange authorization code for tokens
      const tokenResponse = await this.googleOAuthService.exchangeCodeForTokens(code);
      
      // Lấy thông tin user
      const userInfo = await this.googleOAuthService.getUserInfo(tokenResponse.access_token);
      
      return {
        access_token: tokenResponse.access_token,
        refresh_token: tokenResponse.refresh_token,
        expires_in: tokenResponse.expires_in,
        userInfo: {
          id: userInfo.id,
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture
        }
      };
    } catch (error) {
      this.logger.error('Error exchanging code for tokens:', error);
      throw new AppException(ErrorCode.GOOGLE_AUTH_FAILED);
    }
  }

  async refreshAccessToken(refreshToken: string) {
    try {
      return await this.googleOAuthService.refreshAccessToken(refreshToken);
    } catch (error) {
      this.logger.error('Error refreshing access token:', error);
      throw new AppException(ErrorCode.GOOGLE_TOKEN_REFRESH_FAILED);
    }
  }
}
```

### **2. Controller - OAuth Endpoints với DTOs**

```typescript
// google-picker.controller.ts
@Controller('auth/google-picker')
export class GooglePickerController {

  @Get('url')
  @ApiOperation({
    summary: 'Lấy URL xác thực Google OAuth',
    description: 'Tạo URL để redirect user đến Google OAuth cho Google Picker'
  })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực được tạo thành công',
    type: GooglePickerAuthUrlResponseDto
  })
  async getAuthorizationUrl(
  ): Promise<ApiResponseDto<GooglePickerAuthUrlResponseDto>> {
    try {
      const result = this.googlePickerService.getAuthorizationUrl();
      return ApiResponseDto.success(result, 'URL xác thực được tạo thành công');
    } catch (error) {
      this.logger.error('Error getting authorization URL:', error);
      throw new AppException(
        GooglePickerErrorCode.URL_GENERATION_FAILED,
        'Lỗi khi tạo URL xác thực Google'
      );
    }
  }

  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Query('error') error?: string,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      if (error) {
        return this.sendClosePopupScript(res, { error: error });
      }

      if (!code || !state) {
        return this.sendClosePopupScript(res, { error: 'Missing code or state' });
      }

      // Exchange code for tokens
      const tokens = await this.googlePickerService.exchangeCodeForTokens(code);
      
      // Lưu vào session
      req.session.googlePicker = {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: Date.now() + (tokens.expires_in * 1000),
        userInfo: tokens.userInfo,
        state: state
      };

      // Trả về script đóng popup và thông báo thành công
      return this.sendClosePopupScript(res, { 
        success: true, 
        message: 'Authentication successful' 
      });

    } catch (error) {
      this.logger.error('Google Picker callback error:', error);
      return this.sendClosePopupScript(res, { 
        error: 'Authentication failed' 
      });
    }
  }

  private sendClosePopupScript(res: Response, data: any) {
    const script = `
      <script>
        try {
          // Gửi message về parent window (FE)
          if (window.opener) {
            window.opener.postMessage({
              type: 'GOOGLE_PICKER_AUTH_RESULT',
              data: ${JSON.stringify(data)}
            }, '*');
          }
          
          // Đóng popup
          window.close();
        } catch (error) {
          console.error('Error closing popup:', error);
          // Fallback: redirect về trang chính
          window.location.href = '/';
        }
      </script>
    `;
    
    res.setHeader('Content-Type', 'text/html');
    res.send(script);
  }

  @Get('config')
  async getSession(@Req() req: Request): Promise<ApiResponseDto<any>> {
    try {
      const session = req.session.googlePicker;

      if (!session) {
        throw new AppException(ErrorCode.GOOGLE_PICKER_NOT_AUTHENTICATED);
      }

      // Check token expiry
      if (Date.now() >= session.expiresAt) {
        // Refresh token if needed
        const newTokens = await this.googlePickerService.refreshAccessToken(session.refreshToken);

        // Update session
        req.session.googlePicker = {
          ...session,
          accessToken: newTokens.access_token,
          expiresAt: Date.now() + (newTokens.expires_in * 1000)
        };
      }

      // Lấy config từ API config
      const config = await this.getPickerConfig();

      return ApiResponseDto.success({
        // Session data
        accessToken: req.session.googlePicker.accessToken,
        userInfo: req.session.googlePicker.userInfo,
        isAuthenticated: true,

        // Config data
        config: {
          apiKey: config.apiKey,
          clientId: config.clientId,
          appId: config.appId,
          scopes: config.scopes,
          viewIds: config.viewIds,
          features: config.features,
          locale: config.locale
        }
      }, 'Google Picker session and config retrieved successfully');

    } catch (error) {
      this.logger.error('Error getting Google Picker session:', error);
      throw new AppException(ErrorCode.GOOGLE_PICKER_SESSION_ERROR);
    }
  }
}
```

## 🌐 **Frontend Implementation**

### **3. Frontend Service**

```typescript
// google-picker.service.ts (Frontend)
class GooglePickerService {
  private popup: Window | null = null;

  async authenticate(): Promise<{ accessToken: string; userInfo: any; config: any }> {
    return new Promise((resolve, reject) => {
      // Lấy authorization URL từ BE
      fetch('/api/auth/google-picker/url')
        .then(res => res.json())
        .then(data => {
          const { url } = data.data;

          // Mở popup
          this.popup = window.open(
            url,
            'google-picker-auth',
            'width=500,height=600,scrollbars=yes,resizable=yes'
          );

          // Listen for message từ popup
          const messageHandler = (event: MessageEvent) => {
            if (event.data.type === 'GOOGLE_PICKER_AUTH_RESULT') {
              window.removeEventListener('message', messageHandler);

              if (event.data.data.success) {
                // Lấy session data + config từ BE
                this.getSessionData()
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(new Error(event.data.data.error || 'Authentication failed'));
              }
            }
          };

          window.addEventListener('message', messageHandler);

          // Check if popup was closed manually
          const checkClosed = setInterval(() => {
            if (this.popup?.closed) {
              clearInterval(checkClosed);
              window.removeEventListener('message', messageHandler);
              reject(new Error('Authentication cancelled'));
            }
          }, 1000);
        })
        .catch(reject);
    });
  }

  private async getSessionData() {
    const response = await fetch('/api/auth/google-picker/session');
    const result = await response.json();
    return result.data; // Bao gồm cả session và config
  }

  async initializePicker(sessionData: any): Promise<void> {
    const { accessToken, config } = sessionData;

    return new Promise((resolve, reject) => {
      gapi.load('picker', () => {
        const picker = new google.picker.PickerBuilder()
          .addView(google.picker.ViewId[config.viewIds.docs])
          .setOAuthToken(accessToken)
          .setDeveloperKey(config.apiKey)
          .setAppId(config.appId)
          .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
          .setCallback((data) => {
            if (data.action === google.picker.Action.PICKED) {
              console.log('Selected files:', data.docs);
              // Handle picked files
              this.handlePickedFiles(data.docs);
            }
          })
          .build();

        picker.setVisible(true);
        resolve();
      });
    });
  }

  private handlePickedFiles(files: any[]) {
    files.forEach(file => {
      console.log('File:', {
        id: file.id,
        name: file.name,
        url: file.url,
        mimeType: file.mimeType,
        sizeBytes: file.sizeBytes
      });
    });
  }

  async clearSession() {
    await fetch('/api/auth/google-picker/session', { method: 'DELETE' });
  }
}
```

### **4. Frontend Usage**

```typescript
// Usage example
const pickerService = new GooglePickerService();

async function openGooglePicker() {
  try {
    // Authenticate và lấy session + config
    const sessionData = await pickerService.authenticate();

    // Initialize Google Picker với config
    await pickerService.initializePicker(sessionData);

  } catch (error) {
    console.error('Google Picker error:', error);
    alert('Lỗi khi mở Google Picker: ' + error.message);
  }
}

// Clear session khi logout
async function logout() {
  await pickerService.clearSession();
}

// HTML Button
// <button onclick="openGooglePicker()">Chọn file từ Google Drive</button>
```

## ⚙️ **Configuration**

### **5. Environment Variables**

```bash
# .env
GOOGLE_CLIENT_ID=123456789-abc.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abc123def456
GOOGLE_API_KEY=AIzaSyC_abc123def456
GOOGLE_APP_ID=123456789
GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google-picker/callback
SESSION_SECRET=your-super-secret-session-key
```

### **6. Session Configuration**

```typescript
// app.module.ts hoặc main.ts
import * as session from 'express-session';

app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));
```

## 📊 **API Response Examples**

### **7. Session + Config Response**

```json
{
  "success": true,
  "message": "Google Picker session and config retrieved successfully",
  "data": {
    "accessToken": "ya29.a0AfH6SMC...",
    "userInfo": {
      "id": "123456789",
      "email": "<EMAIL>",
      "name": "John Doe",
      "picture": "https://lh3.googleusercontent.com/..."
    },
    "isAuthenticated": true,
    "config": {
      "apiKey": "AIzaSyC...",
      "clientId": "123456789-abc.apps.googleusercontent.com",
      "appId": "123456789",
      "scopes": [
        "https://www.googleapis.com/auth/drive.readonly",
        "https://www.googleapis.com/auth/drive.metadata.readonly"
      ],
      "viewIds": {
        "docs": "DOCS",
        "images": "DOCS_IMAGES",
        "videos": "DOCS_VIDEOS",
        "folders": "FOLDERS"
      },
      "features": {
        "multiselect": true,
        "nav_hidden": false,
        "upload": false
      },
      "locale": "vi"
    }
  }
}
```

## 🎯 **Lợi ích**

### **8. Ưu điểm của phương pháp này**

1. **🔒 Security**
   - Tokens được lưu server-side, không expose ra client
   - Session-based authentication
   - State parameter để chống CSRF

2. **👤 User Experience**
   - Popup tự động đóng sau khi auth thành công
   - Không cần redirect toàn trang
   - Error handling đầy đủ

3. **⚡ Performance**
   - Kết hợp session + config trong 1 API call
   - Tự động refresh token khi hết hạn
   - Cache config trong session

4. **🛠️ Maintainability**
   - Code tách biệt rõ ràng
   - Dễ dàng test và debug
   - Flexible configuration

## 🔧 **Troubleshooting**

### **9. Common Issues**

#### **Popup bị block**
```javascript
// Kiểm tra popup blocker
if (!this.popup || this.popup.closed) {
  alert('Popup bị chặn. Vui lòng cho phép popup cho trang này.');
  return;
}
```

#### **CORS Issues**
```typescript
// Backend CORS config
app.enableCors({
  origin: ['http://localhost:3000', 'https://yourdomain.com'],
  credentials: true
});
```

#### **Session not persisting**
```typescript
// Đảm bảo session middleware được config đúng
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set true cho HTTPS
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000
  }
}));
```

## 📝 **Testing**

### **10. Test Cases**

```typescript
// Test authentication flow
describe('Google Picker OAuth', () => {
  it('should generate authorization URL', async () => {
    const result = await googlePickerService.getAuthorizationUrl();
    expect(result.url).toContain('accounts.google.com');
    expect(result.state).toBeDefined();
  });

  it('should handle callback successfully', async () => {
    const mockCode = 'test-code';
    const mockState = 'test-state';

    // Mock session
    const req = { session: {} };
    const res = { setHeader: jest.fn(), send: jest.fn() };

    await controller.handleCallback(mockCode, mockState, undefined, req, res);

    expect(req.session.googlePicker).toBeDefined();
    expect(res.send).toHaveBeenCalledWith(expect.stringContaining('window.close'));
  });
});
```

## 🚀 **Deployment Notes**

### **11. Production Considerations**

1. **HTTPS Required**: Google OAuth yêu cầu HTTPS trong production
2. **Domain Whitelist**: Thêm domain vào Google Console
3. **Session Store**: Sử dụng Redis cho session trong production
4. **Rate Limiting**: Implement rate limiting cho auth endpoints
5. **Monitoring**: Log và monitor auth failures

```typescript
// Production session config
app.use(session({
  store: new RedisStore({ client: redisClient }),
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: true, // HTTPS only
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000,
    sameSite: 'strict'
  }
}));
```

## 🚀 **Kết quả cuối cùng - Implementation hoàn toàn mới:**

### **📊 API Endpoints:**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/chat/google-picker/url` | Lấy authorization URL |
| `GET` | `/chat/google-picker/callback` | Xử lý callback với popup auto-close |
| `GET` | `/chat/google-picker/session` | Lấy session + config combined |
| `GET` | `/chat/google-picker/config` | Lấy config only (không cần auth) |
| `DELETE` | `/chat/google-picker/session` | Clear session |

### **🎯 Cải tiến so với version cũ:**

| Aspect | Cũ | Mới |
|--------|-----|-----|
| **Session Type** | `Record<string, any>` | `ChatSessionData` (type-safe) |
| **Response Structure** | Mixed formats | Consistent DTOs |
| **API Calls** | 3+ separate calls | 1-2 combined calls |
| **Popup Handling** | Manual | Auto-close với script |
| **Error Handling** | Basic | Comprehensive với validation |
| **Token Refresh** | Manual | Automatic trong session call |
| **Type Safety** | Minimal | Full TypeScript support |
| **Code Maintenance** | Complex legacy code | Clean, modern implementation |

### **✅ Đã loại bỏ hoàn toàn:**
- ❌ Legacy endpoints và DTOs
- ❌ Mixed error handling
- ❌ Inconsistent response formats
- ❌ Manual token management
- ❌ Complex callback handling

### **✅ Implementation mới:**
- ✅ **Type-safe DTOs** cho tất cả requests/responses
- ✅ **Session-based authentication** với ChatSessionData
- ✅ **Popup auto-close** với script injection
- ✅ **Combined APIs** (session + config trong 1 call)
- ✅ **Automatic token refresh** trong session endpoint
- ✅ **Consistent error handling** với GOOGLE_ERROR_CODES
- ✅ **Clean code structure** với helper methods

---

**📚 Tài liệu tham khảo:**
- [Google Picker API Documentation](https://developers.google.com/picker)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [NestJS Session Documentation](https://docs.nestjs.com/techniques/session)
```
