import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserConvertCustomer, UserOrder, CustomerProduct } from '../entities';
import {
  PotentialCustomersQueryDto,
  ReportOverviewQueryDto,
  TopSellingProductsQueryDto,
  ProductsChartQueryDto,
} from '../user/dto/report';

/**
 * Repository xử lý các truy vấn báo cáo business
 */
@Injectable()
export class BusinessReportRepository {
  private readonly logger = new Logger(BusinessReportRepository.name);

  constructor(
    @InjectRepository(UserOrder)
    private readonly userOrderRepository: Repository<UserOrder>,

    @InjectRepository(CustomerProduct)
    private readonly customerProductEntityRepository: Repository<CustomerProduct>,

    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
  ) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   */
  async getReportOverview(
    userId: number,
    query: ReportOverviewQueryDto & { startDate?: string; endDate?: string },
  ) {
    try {
      const { startDate, endDate } = this.getDateRange(
        query.startDate,
        query.endDate,
      );

      // Tính toán doanh thu và đơn hàng
      const revenueQuery = this.userOrderRepository
        .createQueryBuilder('user_order')
        .select([
          'COUNT(user_order.id) as total_orders',
          'COALESCE(SUM(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as total_revenue',
        ])
        .where('user_order.user_id = :userId', { userId })
        .andWhere('user_order.created_at >= :startDate', { startDate })
        .andWhere('user_order.created_at <= :endDate', { endDate });

      // Tính toán khách hàng mới
      const customersQuery = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .select('COUNT(customer.id) as new_customers')
        .where('customer.user_id = :userId', { userId })
        .andWhere('customer.created_at >= :startDate', { startDate })
        .andWhere('customer.created_at <= :endDate', { endDate });

      const [revenueResult, customersResult] = await Promise.all([
        revenueQuery.getRawOne(),
        customersQuery.getRawOne(),
      ]);

      return {
        totalRevenue: parseFloat(revenueResult?.total_revenue || '0'),
        totalOrders: parseInt(revenueResult?.total_orders || '0'),
        newCustomers: parseInt(customersResult?.new_customers || '0'),
        startDate: this.formatDate(startDate),
        endDate: this.formatDate(endDate),
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu tổng quan: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private getDateRange(startDate?: string, endDate?: string) {
    const now = new Date();
    const start = startDate
      ? new Date(startDate).getTime()
      : new Date(now.getFullYear(), 0, 1).getTime();
    const end = endDate
      ? new Date(endDate).getTime() + 24 * 60 * 60 * 1000 - 1
      : now.getTime();

    return { startDate: start, endDate: end };
  }

  /**
   * Lấy dữ liệu sản phẩm bán chạy
   */
  async getTopSellingProducts(
    userId: number,
    query: (TopSellingProductsQueryDto | ProductsChartQueryDto) & {
      startDate?: string;
      endDate?: string;
    },
  ) {
    try {
      const { startDate, endDate } = this.getDateRange(
        query.startDate,
        query.endDate,
      );

      // Sử dụng raw query để xử lý JSONB array elements
      let sql = `
        WITH product_data AS (
          SELECT
            (product_element->>'productId')::bigint as product_id,
            product_element->>'name' as product_name,
            COALESCE(product_element->>'categoryName', 'Chưa phân loại') as category_name,
            product_element->>'imageUrl' as image_url,
            (product_element->>'quantity')::integer as quantity,
            (product_element->>'totalPrice')::decimal as total_price,
            (product_element->>'categoryId')::bigint as category_id,
            uo.id as order_id
          FROM user_orders uo,
               jsonb_array_elements(uo.product_info) as product_element
          WHERE uo.user_id = $1
            AND uo.created_at >= $2
            AND uo.created_at <= $3
            AND uo.product_info IS NOT NULL
            AND jsonb_typeof(uo.product_info) = 'array'
        )
        SELECT
          product_id,
          product_name,
          category_name,
          image_url,
          SUM(quantity) as quantity_sold,
          SUM(total_price) as revenue,
          COUNT(DISTINCT order_id) as orders_count
        FROM product_data
        WHERE product_id IS NOT NULL
      `;

      const params = [userId, startDate, endDate];

      if (query.categoryId) {
        sql += ` AND category_id = $${params.length + 1}`;
        params.push(query.categoryId);
      }

      sql += `
        GROUP BY product_id, product_name, category_name, image_url
      `;

      // Thêm ORDER BY dựa trên sortBy (nếu có)
      const sortBy = (query as any).sortBy || 'revenue';
      switch (sortBy) {
        case 'quantity':
          sql += ` ORDER BY quantity_sold DESC`;
          break;
        case 'orders':
          sql += ` ORDER BY orders_count DESC`;
          break;
        case 'revenue':
        default:
          sql += ` ORDER BY revenue DESC`;
          break;
      }

      sql += ` LIMIT $${params.length + 1}`;
      params.push(query.limit || 10);

      const results = await this.userOrderRepository.query(sql, params);

      return results.map((result: any, index: number) => ({
        rank: index + 1,
        productId: parseInt(result.product_id),
        productName: result.product_name || 'Sản phẩm không tên',
        categoryName: result.category_name || 'Chưa phân loại',
        imageUrl: result.image_url,
        quantitySold: parseInt(result.quantity_sold || '0'),
        revenue: parseFloat(result.revenue || '0'),
        ordersCount: parseInt(result.orders_count || '0'),
        averageRating: null, // TODO: Implement rating system
        growthRate: 0, // TODO: Implement growth rate calculation
      }));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu sản phẩm bán chạy: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy dữ liệu khách hàng tiềm năng
   */
  async getPotentialCustomers(
    userId: number,
    query: PotentialCustomersQueryDto,
  ) {
    try {
      const queryBuilder = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .leftJoin(
          (subQuery) => {
            return subQuery
              .select([
                "CONCAT(user_order.convert_customer_email, '|', user_order.convert_customer_phone) as customer_key",
                'COUNT(user_order.id) as total_orders',
                'COALESCE(SUM(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as total_spent',
                'COALESCE(AVG(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as average_order_value',
                'EXTRACT(EPOCH FROM MAX(TO_TIMESTAMP(user_order.created_at / 1000))) * 1000 as last_order_date',
              ])
              .from(UserOrder, 'user_order')
              .where('user_order.user_id = :userId', { userId })
              .andWhere(
                '(user_order.convert_customer_email IS NOT NULL OR user_order.convert_customer_phone IS NOT NULL)',
              )
              .groupBy(
                "CONCAT(user_order.convert_customer_email, '|', user_order.convert_customer_phone)",
              );
          },
          'order_stats',
          "CONCAT(customer.email, '|', customer.phone) = order_stats.customer_key",
        )
        .select([
          'customer.id as customer_id',
          'customer.name as customer_name',
          'customer.email as email',
          'customer.phone as phone',
          'customer.avatar as avatar_url',
          'COALESCE(order_stats.total_orders, 0) as total_orders',
          'COALESCE(order_stats.total_spent, 0) as total_spent',
          'COALESCE(order_stats.average_order_value, 0) as average_order_value',
          'order_stats.last_order_date as last_order_date',
          'CASE WHEN order_stats.last_order_date IS NOT NULL THEN EXTRACT(EPOCH FROM NOW() - TO_TIMESTAMP(order_stats.last_order_date / 1000)) / 86400 ELSE NULL END as days_since_last_order',
        ])
        .where('customer.user_id = :userId', { userId })
        .andWhere('order_stats.total_orders > 0');

      if (query.minOrderValue) {
        queryBuilder.andWhere('order_stats.total_spent >= :minOrderValue', {
          minOrderValue: query.minOrderValue,
        });
      }

      if (query.minOrderCount) {
        queryBuilder.andWhere('order_stats.total_orders >= :minOrderCount', {
          minOrderCount: query.minOrderCount,
        });
      }

      if (query.lastOrderDays) {
        queryBuilder.andWhere(
          'EXTRACT(EPOCH FROM NOW() - TO_TIMESTAMP(order_stats.last_order_date / 1000)) / 86400 <= :lastOrderDays',
          { lastOrderDays: query.lastOrderDays },
        );
      }

      const results = await queryBuilder
        .orderBy('order_stats.total_spent', 'DESC')
        .addOrderBy('days_since_last_order', 'ASC')
        .limit(query.limit || 20)
        .getRawMany();

      return results.map((result) => ({
        customerId: parseInt(result.customer_id),
        customerName: result.customer_name || 'Khách hàng',
        email: this.extractPrimaryEmail(result.email),
        phone: result.phone,
        avatarUrl: result.avatar_url,
        totalOrders: parseInt(result.total_orders || '0'),
        totalSpent: parseFloat(result.total_spent || '0'),
        averageOrderValue: parseFloat(result.average_order_value || '0'),
        lastOrderDate: result.last_order_date
          ? this.formatDate(result.last_order_date)
          : null,
        daysSinceLastOrder: Math.floor(
          parseFloat(result.days_since_last_order || '0'),
        ),
        potentialScore: this.calculatePotentialScore(result),
        tags: [], // TODO: Implement customer tags
      }));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu khách hàng tiềm năng: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private extractPrimaryEmail(emailJson: any): string {
    if (!emailJson) return '';
    if (typeof emailJson === 'string') return emailJson;
    if (typeof emailJson === 'object') {
      return (
        emailJson.primary ||
        emailJson.email ||
        Object.values(emailJson)[0] ||
        ''
      );
    }
    return '';
  }

  private calculatePotentialScore(customerData: any): number {
    const totalSpent = parseFloat(customerData.total_spent || '0');
    const totalOrders = parseInt(customerData.total_orders || '0');
    const daysSinceLastOrder = parseFloat(
      customerData.days_since_last_order || '0',
    );

    // Simple scoring algorithm (can be improved)
    let score = 0;

    // Score based on total spent (0-40 points)
    if (totalSpent > 5000000) score += 40;
    else if (totalSpent > 2000000) score += 30;
    else if (totalSpent > 1000000) score += 20;
    else if (totalSpent > 500000) score += 10;

    // Score based on order frequency (0-30 points)
    if (totalOrders > 10) score += 30;
    else if (totalOrders > 5) score += 20;
    else if (totalOrders > 2) score += 10;

    // Score based on recency (0-30 points)
    if (daysSinceLastOrder <= 7) score += 30;
    else if (daysSinceLastOrder <= 30) score += 20;
    else if (daysSinceLastOrder <= 90) score += 10;

    return Math.min(score, 100);
  }

  private formatDate(timestamp: number | Date | string): string | null {
    try {
      let date: Date;

      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      } else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else {
        throw new Error('Invalid timestamp format');
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date value');
      }

      return date.toISOString().split('T')[0];
    } catch (error) {
      this.logger.warn(
        `Lỗi khi format date: ${error.message}, timestamp: ${timestamp}`,
      );
      return null;
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đường business
   */
  async getLineChartData(
    userId: number,
    params: {
      beginTimestamp: number;
      endTimestamp: number;
      chartType: string;
      period: string;
    },
  ): Promise<Record<string, number>> {
    try {
      const { beginTimestamp, endTimestamp, chartType, period } = params;
      // Convert to milliseconds for bigint comparison
      const beginMs = beginTimestamp * 1000;
      const endMs = endTimestamp * 1000;

      let queryBuilder: SelectQueryBuilder<any>;
      let selectClause: string;
      let groupByClause: string;

      // Xác định group by clause dựa trên period
      // Convert bigint timestamp to timestamp before using DATE_TRUNC
      switch (period) {
        case 'hour':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('hour', to_timestamp(created_at / 1000)), 'YYYY-MM-DD HH24:00')";
          break;
        case 'day':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('day', to_timestamp(created_at / 1000)), 'YYYY-MM-DD')";
          break;
        case 'week':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('week', to_timestamp(created_at / 1000)), 'YYYY-\"W\"IW')";
          break;
        case 'month':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('month', to_timestamp(created_at / 1000)), 'YYYY-MM')";
          break;
        case 'year':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('year', to_timestamp(created_at / 1000)), 'YYYY')";
          break;
        default:
          groupByClause =
            "TO_CHAR(DATE_TRUNC('day', to_timestamp(created_at / 1000)), 'YYYY-MM-DD')";
      }

      // Xác định select clause và table dựa trên chartType
      switch (chartType) {
        case 'ORDER':
          selectClause = 'COUNT(id)';
          queryBuilder = this.userOrderRepository
            .createQueryBuilder('user_order')
            .select(selectClause, 'value')
            .addSelect(groupByClause, 'period_key')
            .where('user_order.user_id = :userId', { userId })
            .andWhere('user_order.created_at >= :beginMs', { beginMs })
            .andWhere('user_order.created_at <= :endMs', { endMs });
          break;

        case 'AMOUNT':
          selectClause =
            "COALESCE(SUM(CAST(user_order.bill_info->>'totalAmount' AS DECIMAL)), 0)";
          queryBuilder = this.userOrderRepository
            .createQueryBuilder('user_order')
            .select(selectClause, 'value')
            .addSelect(groupByClause, 'period_key')
            .where('user_order.user_id = :userId', { userId })
            .andWhere('user_order.created_at >= :beginMs', { beginMs })
            .andWhere('user_order.created_at <= :endMs', { endMs });
          break;

        case 'CUSTOMER':
          selectClause = 'COUNT(DISTINCT user_order.convert_customer_id)';
          queryBuilder = this.userOrderRepository
            .createQueryBuilder('user_order')
            .select(selectClause, 'value')
            .addSelect(groupByClause, 'period_key')
            .where('user_order.user_id = :userId', { userId })
            .andWhere('user_order.created_at >= :beginMs', { beginMs })
            .andWhere('user_order.created_at <= :endMs', { endMs });
          break;

        case 'PRODUCT':
          selectClause = 'COUNT(id)';
          queryBuilder = this.customerProductEntityRepository
            .createQueryBuilder('customer_product')
            .select(selectClause, 'value')
            .addSelect(
              groupByClause.replace(
                'created_at',
                'customer_product.created_at',
              ),
              'period_key',
            )
            .where('customer_product.user_id = :userId', { userId })
            .andWhere('customer_product.created_at >= :beginMs', { beginMs })
            .andWhere('customer_product.created_at <= :endMs', { endMs });
          break;

        default: // TRANSACTION
          selectClause = 'COUNT(id)';
          queryBuilder = this.userOrderRepository
            .createQueryBuilder('user_order')
            .select(selectClause, 'value')
            .addSelect(groupByClause, 'period_key')
            .where('user_order.user_id = :userId', { userId })
            .andWhere('user_order.created_at >= :beginMs', { beginMs })
            .andWhere('user_order.created_at <= :endMs', { endMs });
      }

      queryBuilder.groupBy('period_key').orderBy('period_key', 'ASC');

      const rawResults = await queryBuilder.getRawMany();

      // Xử lý dữ liệu thành format mong muốn
      const beginDate = new Date(beginMs);
      const endDate = new Date(endMs);
      const data = this.processLineChartData(
        rawResults,
        beginDate,
        endDate,
        period,
      );

      return data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu biểu đồ đường business: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý dữ liệu thô thành format chart
   */
  private processLineChartData(
    rawResults: any[],
    beginDate: Date,
    endDate: Date,
    period: string,
  ): Record<string, number> {
    const data: Record<string, number> = {};

    // Tạo tất cả các key trong khoảng thời gian với giá trị 0
    const allKeys = this.generateAllKeys(beginDate, endDate, period);
    allKeys.forEach((key) => {
      data[key] = 0;
    });

    // Điền dữ liệu thực tế
    rawResults.forEach((row) => {
      const key = this.formatKey(row.period_key);
      data[key] = parseFloat(row.value) || 0;
    });

    return data;
  }

  /**
   * Tạo tất cả các key trong khoảng thời gian
   */
  private generateAllKeys(
    beginDate: Date,
    endDate: Date,
    period: string,
  ): string[] {
    const keys: string[] = [];
    const current = new Date(beginDate);

    while (current <= endDate) {
      let key: string;
      switch (period) {
        case 'hour':
          key = current.toISOString().substring(0, 13) + ':00';
          current.setHours(current.getHours() + 1);
          break;
        case 'day':
          key = current.toISOString().substring(0, 10);
          current.setDate(current.getDate() + 1);
          break;
        case 'week':
          const year = current.getFullYear();
          const week = this.getWeekNumber(current);
          key = `${year}-W${week.toString().padStart(2, '0')}`;
          current.setDate(current.getDate() + 7);
          break;
        case 'month':
          key = current.toISOString().substring(0, 7);
          current.setMonth(current.getMonth() + 1);
          break;
        case 'year':
          key = current.getFullYear().toString();
          current.setFullYear(current.getFullYear() + 1);
          break;
        default:
          key = current.toISOString().substring(0, 10);
          current.setDate(current.getDate() + 1);
      }
      keys.push(key);
    }

    return keys;
  }

  /**
   * Lấy số tuần trong năm
   */
  private getWeekNumber(date: Date): number {
    const d = new Date(
      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
    );
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
  }

  /**
   * Format key từ database
   */
  private formatKey(key: string): string {
    return key;
  }

  /**
   * Lấy timestamp của khách hàng đầu tiên
   * Học hỏi từ R-Point API - trực tiếp select cột bigint timestamp
   */
  async getFirstCustomerTimestamp(
    userId: number,
  ): Promise<{ created_at: number } | null> {
    try {
      const firstCustomer = await this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .select('customer.created_at', 'created_at')
        .where('customer.user_id = :userId', { userId })
        .orderBy('customer.created_at', 'ASC')
        .limit(1)
        .getRawOne();

      return firstCustomer
        ? { created_at: Math.floor(parseInt(firstCustomer.created_at) / 1000) } // Convert milliseconds to seconds
        : null;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy timestamp khách hàng đầu tiên: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đường khách hàng
   */
  async getCustomersLineChartData(
    userId: number,
    params: {
      beginTimestamp: number;
      endTimestamp: number;
      chartType: string;
      period: string;
    },
  ): Promise<Record<string, number>> {
    try {
      const { beginTimestamp, endTimestamp, chartType, period } = params;
      // Convert to milliseconds for bigint comparison
      const beginMs = beginTimestamp * 1000;
      const endMs = endTimestamp * 1000;

      let selectClause: string;
      let groupByClause: string;

      // Xác định group by clause dựa trên period
      // Convert bigint timestamp to timestamp before using DATE_TRUNC
      switch (period) {
        case 'hour':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('hour', to_timestamp(created_at / 1000)), 'YYYY-MM-DD HH24:00')";
          break;
        case 'day':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('day', to_timestamp(created_at / 1000)), 'YYYY-MM-DD')";
          break;
        case 'week':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('week', to_timestamp(created_at / 1000)), 'YYYY-\"W\"IW')";
          break;
        case 'month':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('month', to_timestamp(created_at / 1000)), 'YYYY-MM')";
          break;
        case 'year':
          groupByClause =
            "TO_CHAR(DATE_TRUNC('year', to_timestamp(created_at / 1000)), 'YYYY')";
          break;
        default:
          groupByClause =
            "TO_CHAR(DATE_TRUNC('day', to_timestamp(created_at / 1000)), 'YYYY-MM-DD')";
      }

      // Xác định select clause dựa trên chartType
      switch (chartType) {
        case 'NEW_CUSTOMERS':
          selectClause = 'COUNT(id)';
          break;
        case 'RETURNING_CUSTOMERS':
          // TODO: Implement logic for returning customers
          selectClause = '0';
          break;
        case 'TOTAL_CUSTOMERS':
          selectClause = 'COUNT(id)';
          break;
        default:
          selectClause = 'COUNT(id)';
      }

      const queryBuilder = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .select(selectClause, 'value')
        .addSelect(groupByClause, 'period_key')
        .where('customer.user_id = :userId', { userId })
        .andWhere('customer.created_at >= :beginMs', { beginMs })
        .andWhere('customer.created_at <= :endMs', { endMs })
        .groupBy('period_key')
        .orderBy('period_key', 'ASC');

      const rawResults = await queryBuilder.getRawMany();

      // Xử lý dữ liệu thành format mong muốn
      const beginDate = new Date(beginMs);
      const endDate = new Date(endMs);
      const data = this.processLineChartData(
        rawResults,
        beginDate,
        endDate,
        period,
      );

      return data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu biểu đồ đường khách hàng: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
