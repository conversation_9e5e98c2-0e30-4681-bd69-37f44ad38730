# Workflow Execution API Endpoints

Tài liệu này mô tả các API endpoints mới được tạo cho việc thực thi workflow và node.

## Tổng quan

Các API execution được thiết kế để:
- Thực thi workflow hoàn chỉnh
- Thực thi node đơn lẻ (để test/debug)
- Hỗ trợ cả user và admin
- <PERSON>h<PERSON>ng yêu cầu body hoặc query parameters
- Trả về execution ID để theo dõi tiến trình

## API Endpoints

### User APIs

#### 1. Thực thi Workflow (User)
```
POST /user/workflow/:workflowId/execute
```

**Mô tả:** Thực thi một workflow cho user hiện tại

**Parameters:**
- `workflowId` (path, UUID): ID của workflow cần thực thi

**Response:**
```json
{
  "success": true,
  "message": "Workflow execution đã được khởi tạo thành công",
  "data": {
    "executionId": "123e4567-e89b-12d3-a456-************",
    "workflowId": "123e4567-e89b-12d3-a456-************",
    "status": "running",
    "startedAt": 1640995200000
  }
}
```

#### 2. Thực thi Node (User)
```
POST /user/workflow/:workflowId/node/:nodeId/execute
```

**Mô tả:** Thực thi một node cụ thể trong workflow

**Parameters:**
- `workflowId` (path, UUID): ID của workflow chứa node
- `nodeId` (path, UUID): ID của node cần thực thi

**Response:**
```json
{
  "success": true,
  "message": "Node execution đã được khởi tạo thành công",
  "data": {
    "executionId": "123e4567-e89b-12d3-a456-************",
    "workflowId": "123e4567-e89b-12d3-a456-************",
    "nodeId": "123e4567-e89b-12d3-a456-************",
    "nodeName": "HTTP Request Node",
    "status": "running",
    "startedAt": 1640995200000
  }
}
```

### Admin APIs

#### 3. Thực thi Workflow (Admin)
```
POST /admin/workflow/:workflowId/execute
```

**Mô tả:** Admin thực thi một workflow bất kỳ

**Parameters:**
- `workflowId` (path, UUID): ID của workflow cần thực thi

**Response:** Giống như user API

#### 4. Thực thi Node (Admin)
```
POST /admin/workflow/:workflowId/node/:nodeId/execute
```

**Mô tả:** Admin thực thi một node cụ thể trong workflow

**Parameters:**
- `workflowId` (path, UUID): ID của workflow chứa node
- `nodeId` (path, UUID): ID của node cần thực thi

**Response:** Giống như user API

## Status Codes

### Success Responses
- `200 OK`: Execution đã được khởi tạo thành công

### Error Responses
- `400 Bad Request`: Workflow/Node không thể thực thi (inactive, invalid config)
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền thực thi (chỉ user APIs)
- `404 Not Found`: Không tìm thấy workflow hoặc node
- `500 Internal Server Error`: Lỗi server

## Execution Status

Các trạng thái execution có thể có:
- `running`: Đang thực thi
- `succeeded`: Thực thi thành công
- `failed`: Thực thi thất bại
- `cancelled`: Đã hủy
- `waiting`: Đang chờ
- `error`: Có lỗi xảy ra

## Implementation Notes

### Service Layer
- `ExecutionUserService`: Xử lý logic execution cho user
- `ExecutionAdminService`: Xử lý logic execution cho admin
- Cả hai service đều có method `executeWorkflow()` và `executeNode()`
- Logic thực tế sẽ được implement sau (hiện tại throw Error)

### Controller Layer
- `ExecutionUserController`: Endpoints cho user
- `ExecutionAdminController`: Endpoints cho admin
- Sử dụng guards phù hợp (JwtUserGuard, JwtEmployeeGuard)
- Validation UUID cho parameters

### DTO Layer
- `WorkflowExecutionResponseDto`: Response cho workflow execution
- `NodeExecutionResponseDto`: Response cho node execution
- Cả hai đều extend từ base execution response structure

## Security

### User APIs
- Yêu cầu JWT token của user
- Chỉ có thể thực thi workflow thuộc về user đó
- Kiểm tra quyền truy cập workflow

### Admin APIs
- Yêu cầu JWT token của employee
- Có thể thực thi bất kỳ workflow nào
- Không giới hạn quyền truy cập

## Next Steps

1. **Implement Service Logic**: Hoàn thiện logic trong các service methods
2. **Queue Integration**: Tích hợp với queue system để xử lý execution
3. **Database Operations**: Tạo execution records trong database
4. **Error Handling**: Xử lý các trường hợp lỗi cụ thể
5. **Validation**: Thêm validation cho workflow/node state
6. **Monitoring**: Thêm logging và monitoring cho execution process
