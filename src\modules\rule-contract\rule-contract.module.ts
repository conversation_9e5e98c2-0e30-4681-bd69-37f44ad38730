import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RuleContract } from './entities/rule-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { AffiliateAccount } from '@modules/affiliate/entities/affiliate-account.entity';
import { RuleContractRepository } from './repositories';
import { RuleContractAdminService } from './admin/services';
// import { RuleContractAdminController } from './admin/controllers';
import { UserModule } from '@modules/user/user.module';
import { ServicesModule } from '@shared/services/services.module';

import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { EmailModule } from '@modules/email/email.module';
import { AuthModule } from '@modules/auth/auth.module';
import { forwardRef } from '@nestjs/common';

import { RuleContractXStateModule } from './state-machine/rule-contract-xstate.module';

/**
 * <PERSON>du<PERSON> quản lý hợp đồng nguyên tắc
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([RuleContract, BusinessInfo, AffiliateAccount]),
    UserModule,
    ServicesModule,
    forwardRef(() => SystemConfigurationModule),
    EmailModule,
    AuthModule,
    RuleContractXStateModule,
  ],
  providers: [
    RuleContractRepository,
    RuleContractAdminService,
  ],
  controllers: [
    // RuleContractAdminController, // Uncomment khi cần
  ],
  exports: [
    RuleContractRepository,
    RuleContractAdminService,
  ],
})
export class RuleContractModule {}