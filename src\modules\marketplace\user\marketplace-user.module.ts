import { Agent, AgentMemories, TypeAgent } from '@modules/agent/entities';
import { AgentMemoriesRepository, AgentRepository, TypeAgentRepository } from '@modules/agent/repositories';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AdminDataFineTune, UserDataFineTune } from '@modules/models/entities';
import { AdminDataFineTuneRepository, UserDataFineTuneRepository } from '@modules/models/repositories';
import { SystemConfigurationModule } from '@modules/system-configuration';
import { AdminTool, UserTool } from '@modules/tools/entities';
import { AdminToolRepository, UserToolRepository } from '@modules/tools/repositories';
import { ToolsModule } from '@modules/tools/tools.module';
import { User } from '@modules/user/entities';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { Module, forwardRef } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CdnService } from '@shared/services/cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { S3Service } from '@shared/services/s3.service';
import { Cart, CartItem, FlashSale, MarketOrder, MarketOrderLine, Product, UserBuyProductMarketplace } from '../entities';
import { CartHelper, FlashSalePriceHelper, FlashSaleValidationHelper, ProductAutoHelper, ProductHelper, PurchaseHistoryHelper, ValidationHelper } from '../helpers';
import { CartItemRepository, CartRepository, FlashSaleRepository, MarketOrderLineRepository, MarketOrderRepository, ProductRepository, UserBuyProductMarketplaceRepository } from '../repositories';
import { AgentSharingService } from '../services/agent-sharing.service';
import { ResourceSharingService } from '../shares/resource-sharing.service';
import { AgentValidator, FineTuneValidator, KnowledgeFileValidator } from '../validation';
import { CartUserController, FlashSaleUserController, OrderUserController, PaymentController, ProductUserController } from './controllers';
import {
  CartUserService,
  FlashSaleUserService,
  OrderUserService,
  PaymentService,
  ProductUserService
} from './services';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product, Cart, CartItem, MarketOrder, MarketOrderLine, UserBuyProductMarketplace, FlashSale, User,
      KnowledgeFile, Agent, AgentMemories, TypeAgent,
      UserDataFineTune, AdminDataFineTune, UserTool, AdminTool
    ]),
    ScheduleModule.forRoot(),
    forwardRef(() => SystemConfigurationModule),
    ToolsModule,
  ],
  controllers: [ProductUserController, CartUserController, OrderUserController, PaymentController, FlashSaleUserController],
  providers: [
    ProductUserService,
    CartUserService,
    OrderUserService,
    PaymentService,
    FlashSaleUserService,
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserBuyProductMarketplaceRepository,
    FlashSaleRepository,
    UserRepository,
    KnowledgeFileRepository,
    AgentRepository,
    TypeAgentRepository,
    AgentMemoriesRepository,
    UserDataFineTuneRepository,
    AdminDataFineTuneRepository,
    UserToolRepository,
    AdminToolRepository,
    S3Service,
    RedisService,
    CdnService,
    ProductHelper,
    ProductAutoHelper,
    ValidationHelper,
    CartHelper,
    PurchaseHistoryHelper,
    FlashSaleValidationHelper,
    FlashSalePriceHelper,
    ResourceSharingService,
    AgentSharingService,
    // Validators
    AgentValidator,
    KnowledgeFileValidator,
    FineTuneValidator
  ],
  exports: [ProductUserService, CartUserService, OrderUserService, PaymentService],
})
export class MarketplaceUserModule { }
