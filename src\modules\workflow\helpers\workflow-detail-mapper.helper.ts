/**
 * @file Helper để map Workflow + Nodes + Connections thành WorkflowDetailResponseDto
 * 
 * <PERSON><PERSON><PERSON> hợp dữ liệu từ:
 * - Workflow entity
 * - Nodes với NodeDefinitions
 * - Connections
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { ConnectionResponseDto } from '../dto/response/connection-response.dto';
import { NodeResponseDto } from '../dto/response/node-response.dto';
import { WorkflowDetailResponseDto } from '../dto/response/workflow-detail-response.dto';
import { Connection } from '../entities/connection.entity';
import { NodeDefinition } from '../entities/node-definition.entity';
import { Node } from '../entities/node.entity';
import { Workflow } from '../entities/workflow.entity';
import {
    mapToNodeResponseDtoWithSharedDefinitions
} from './node-response-mapper.helper';

// =================================================================
// SECTION 1: MAIN MAPPER FUNCTION
// =================================================================

/**
 * Map Workflow + Nodes + Connections thành WorkflowDetailResponseDto
 * 
 * @param workflow - Workflow entity
 * @param nodes - Array of Node entities
 * @param nodeDefinitionsMap - Map từ nodeDefinitionId đến NodeDefinition
 * @param connections - Array of Connection entities
 * @returns WorkflowDetailResponseDto đã được map đầy đủ
 */
export function mapToWorkflowDetailResponseDto(
    workflow: Workflow,
    nodes: Node[],
    nodeDefinitionsMap: Map<string, NodeDefinition>,
    connections: Connection[]
): WorkflowDetailResponseDto {
    // 1. Map nodes với node definitions
    const nodeResponseDtos = mapToNodeResponseDtoWithSharedDefinitions(
        nodes,
        nodeDefinitionsMap
    );

    // 2. Map connections
    const connectionResponseDtos = connections.map(connection => mapToConnectionResponseDto(connection));

    // 4. Tạo WorkflowDetailResponseDto
    const workflowDetail: WorkflowDetailResponseDto = {
        // Workflow base info (từ WorkflowResponseDto)
        id: workflow.id,
        name: workflow.name,
        isActive: false,
        settings: workflow.settings,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,

        // Extended info
        nodes: nodeResponseDtos,
        connections: connectionResponseDtos,
    };

    return workflowDetail;
}

// =================================================================
// SECTION 2: CONNECTION MAPPING
// =================================================================

/**
 * Map Connection entity thành ConnectionResponseDto
 */
function mapToConnectionResponseDto(connection: Connection): ConnectionResponseDto {
    return {
        id: connection.id,
        sourceNodeId: connection.sourceNodeId,
        targetNodeId: connection.targetNodeId,
        sourceHandle: connection.sourceHandle,
        sourceHandleIndex: connection.sourceHandleIndex,
        targetHandle: connection.targetHandle,
        targetHandleIndex: connection.targetHandleIndex,
    };
}

// =================================================================
// SECTION 3: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate toàn bộ workflow detail
 */
function validateWorkflowDetail(nodes: NodeResponseDto[]): {
    isValid: boolean;
    errors: Array<{
        nodeId: string;
        errors: string[];
    }>;
} {
    const errors: Array<{ nodeId: string; errors: string[] }> = [];
    let isValid = true;

    nodes.forEach(node => {
        if (node.hasValidationErrors && node.validationErrors) {
            isValid = false;
            errors.push({
                nodeId: node.id,
                errors: node.validationErrors.map(err => err.errorMessage)
            });
        }
    });

    return { isValid, errors };
}

// =================================================================
// SECTION 4: UTILITY FUNCTIONS
// =================================================================

/**
 * Tạo empty WorkflowDetailResponseDto (cho testing hoặc default values)
 */
export function createEmptyWorkflowDetailResponseDto(workflow: Workflow): WorkflowDetailResponseDto {
    return {
        id: workflow.id,
        name: workflow.name,
        settings: workflow.settings,
        isActive: workflow.isActive,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,
        nodes: [],
        connections: [],
    };
}

/**
 * Filter nodes theo type
 */
export function filterNodesByType(
    workflowDetail: WorkflowDetailResponseDto,
    nodeType: string
): NodeResponseDto[] {
    return workflowDetail.nodes.filter(node => node.typeName === nodeType);
}

/**
 * Find node by ID
 */
export function findNodeById(
    workflowDetail: WorkflowDetailResponseDto,
    nodeId: string
): NodeResponseDto | undefined {
    return workflowDetail.nodes.find(node => node.id === nodeId);
}

/**
 * Find connections by node ID
 */
export function findConnectionsByNodeId(
    workflowDetail: WorkflowDetailResponseDto,
    nodeId: string
): {
    incoming: ConnectionResponseDto[];
    outgoing: ConnectionResponseDto[];
} {
    const incoming = workflowDetail.connections.filter(conn => conn.targetNodeId === nodeId);
    const outgoing = workflowDetail.connections.filter(conn => conn.sourceNodeId === nodeId);

    return { incoming, outgoing };
}
