### Workflow Execution API Test File
### Sử dụng với REST Client extension trong VS Code

### Variables
@baseUrl = http://localhost:3000
@userToken = YOUR_USER_JWT_TOKEN_HERE
@adminToken = YOUR_ADMIN_JWT_TOKEN_HERE
@workflowId = 123e4567-e89b-12d3-a456-426614174000
@nodeId = 123e4567-e89b-12d3-a456-426614174001

### ===== USER APIs =====

### 1. User - Execute Workflow
POST {{baseUrl}}/user/workflow/{{workflowId}}/execute
Authorization: Bearer {{userToken}}
Content-Type: application/json

### 2. User - Execute Node
POST {{baseUrl}}/user/workflow/{{workflowId}}/node/{{nodeId}}/execute
Authorization: Bearer {{userToken}}
Content-Type: application/json

### ===== ADMIN APIs =====

### 3. Admin - Execute Workflow
POST {{baseUrl}}/admin/workflow/{{workflowId}}/execute
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### 4. Admin - Execute Node
POST {{baseUrl}}/admin/workflow/{{workflowId}}/node/{{nodeId}}/execute
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### ===== ERROR CASES =====

### 5. Invalid Workflow ID (404)
POST {{baseUrl}}/user/workflow/invalid-uuid/execute
Authorization: Bearer {{userToken}}
Content-Type: application/json

### 6. Invalid Node ID (404)
POST {{baseUrl}}/user/workflow/{{workflowId}}/node/invalid-uuid/execute
Authorization: Bearer {{userToken}}
Content-Type: application/json

### 7. No Authorization (401)
POST {{baseUrl}}/user/workflow/{{workflowId}}/execute
Content-Type: application/json

### 8. Invalid Token (401)
POST {{baseUrl}}/user/workflow/{{workflowId}}/execute
Authorization: Bearer invalid-token
Content-Type: application/json
