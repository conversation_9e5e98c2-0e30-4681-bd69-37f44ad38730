import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho cách sắp xếp sản phẩm
 */
export enum ProductSortByEnum {
  REVENUE = 'revenue',
  QUANTITY = 'quantity',
  ORDERS = 'orders',
}

/**
 * Enum cho loại dữ liệu biểu đồ sản phẩm
 */
export enum ProductsChartDataType {
  PRODUCTS_COUNT = 'PRODUCTS_COUNT',
  TOTAL_REVENUE = 'TOTAL_REVENUE',
  TOTAL_QUANTITY = 'TOTAL_QUANTITY',
}

/**
 * DTO cho query parameters của API biểu đồ sản phẩm
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class ProductsChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: ProductsChartDataType,
    example: ProductsChartDataType.PRODUCTS_COUNT,
    required: false,
    default: ProductsChartDataType.PRODUCTS_COUNT,
  })
  @IsOptional()
  @IsEnum(ProductsChartDataType)
  type?: ProductsChartDataType;

  /**
   * ID danh mục sản phẩm
   */
  @ApiProperty({
    description: 'ID danh mục sản phẩm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID danh mục phải là số' })
  @Min(1, { message: 'ID danh mục phải lớn hơn 0' })
  @Type(() => Number)
  categoryId?: number;

  /**
   * Số lượng sản phẩm tối đa trả về
   */
  @ApiProperty({
    description: 'Số lượng sản phẩm tối đa trả về',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Cách sắp xếp sản phẩm
   */
  @ApiProperty({
    description: 'Cách sắp xếp sản phẩm',
    enum: ProductSortByEnum,
    example: ProductSortByEnum.REVENUE,
    default: ProductSortByEnum.REVENUE,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductSortByEnum, { message: 'Cách sắp xếp không hợp lệ' })
  sortBy?: ProductSortByEnum = ProductSortByEnum.REVENUE;
}
