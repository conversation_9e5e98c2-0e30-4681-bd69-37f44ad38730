import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Enum cho loại dữ liệu biểu đồ business
 * Đã mở rộng để thay thế customers-chart và sales-chart APIs
 */
export enum BusinessChartDataType {
  // Original types
  TRANSACTION = 'TRANSACTION',
  AMOUNT = 'AMOUNT',
  ORDER = 'ORDER',
  CUSTOMER = 'CUSTOMER',
  PRODUCT = 'PRODUCT',

  // From CustomersChartDataType (thay thế customers-chart)
  NEW_CUSTOMERS = 'NEW_CUSTOMERS',
  RETURNING_CUSTOMERS = 'RETURNING_CUSTOMERS',
  TOTAL_CUSTOMERS = 'TOTAL_CUSTOMERS',

  // From SalesChartDataType (thay thế sales-chart)
  REVENUE = 'REVENUE',
  AVERAGE_ORDER_VALUE = 'AVERAGE_ORDER_VALUE',
}

/**
 * DTO cho truy vấn biểu đồ đường business
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class BusinessLineChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds/milliseconds hoặc date string YYYY-MM-DD - tự động detect)
   */
  @ApiProperty({
    description:
      'Thời gian bắt đầu (Unix timestamp seconds/milliseconds hoặc date string YYYY-MM-DD - tự động detect)',
    example: '2024-08-21 hoặc 1724169600000',
    required: false,
    type: 'string',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;

    // Nếu là số (timestamp)
    const num = Number(value);
    if (!isNaN(num)) {
      return num;
    }

    // Nếu là string date (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm:ss)
    if (typeof value === 'string') {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date.getTime(); // Trả về milliseconds
      }
    }

    return undefined;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds/milliseconds hoặc date string YYYY-MM-DD - tự động detect)
   */
  @ApiProperty({
    description:
      'Thời gian kết thúc (Unix timestamp seconds/milliseconds hoặc date string YYYY-MM-DD - tự động detect)',
    example: '2024-08-23 hoặc 1724342400000',
    required: false,
    type: 'string',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;

    // Nếu là số (timestamp)
    const num = Number(value);
    if (!isNaN(num)) {
      return num;
    }

    // Nếu là string date (YYYY-MM-DD hoặc YYYY-MM-DD HH:mm:ss)
    if (typeof value === 'string') {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date.getTime(); // Trả về milliseconds
      }
    }

    return undefined;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: BusinessChartDataType,
    example: BusinessChartDataType.ORDER,
    required: false,
    default: BusinessChartDataType.ORDER,
  })
  @IsOptional()
  @IsEnum(BusinessChartDataType)
  type?: BusinessChartDataType;
}
