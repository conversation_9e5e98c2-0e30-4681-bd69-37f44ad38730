import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RuleContractXStateController } from './rule-contract-xstate.controller';
import { RuleContractAdminController } from './rule-contract-admin.controller';
import { RuleContractXStateService } from './rule-contract-xstate.service';
import { RuleContractActionsService } from './rule-contract-actions.service';
import { RuleContractStateRepository } from './repositories/rule-contract-state.repository';
import { RuleContractStateEncryptedRepository } from './repositories/rule-contract-state-encrypted.repository';
import { RuleContractStateEntity } from './entities/rule-contract-state.entity';
import { RuleContract } from '../entities/rule-contract.entity';
import { RuleContractRepository } from '../repositories/rule-contract.repository';
import { EncryptionMigrationService } from '@/shared/services/encryption-migration.service';
import { RuleContractContextEncryptionService } from './services/rule-contract-context-encryption.service';

import { ServicesModule } from '@shared/services/services.module';
import { UserModule } from '@modules/user/user.module';
import { EmailModule } from '@modules/email/email.module';
import { AuthModule } from '@modules/auth/auth.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { BusinessInfo } from '@/modules/user/entities';
import { RedisService } from '@/shared/services/redis.service';
import { EmailPlaceholderService } from '@/modules/email/services/email-placeholder.service';
import { SignatureModule } from '@/modules/signature/signature.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RuleContractStateEntity,
      RuleContract,
      BusinessInfo,
    ]),
    ServicesModule,
    UserModule,
    EmailModule,
    AuthModule,
    EmployeeModule,
    forwardRef(() => SystemConfigurationModule),
    SignatureModule,
  ],
  controllers: [
    RuleContractXStateController,
    RuleContractAdminController,
  ],
  providers: [
    RuleContractXStateService,
    RuleContractActionsService,
    RuleContractStateRepository,
    RuleContractStateEncryptedRepository,
    RuleContractRepository,
    EncryptionMigrationService,
    RuleContractContextEncryptionService,
    RedisService,
    EmailPlaceholderService,
  ],
  exports: [
    RuleContractXStateService,
    RuleContractActionsService,
  ],
})
export class RuleContractXStateModule {}
