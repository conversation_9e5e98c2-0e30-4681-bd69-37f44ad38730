/**
 * @file Google Picker Controller for Chat Module
 * 
 * Controller xử lý OAuth flow cho Google Picker trong chat module
 * Sử dụng DTOs type-safe và session-based authentication
 * 
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import { 
  Controller, 
  Get, 
  Delete,
  Query, 
  Res, 
  Session, 
  UseGuards, 
  Logger 
} from '@nestjs/common';
import { Response } from 'express';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';

import { GooglePickerService } from '../services/google-picker.service';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES } from '@shared/services/google/exceptions/google.exception';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@/common/response';

import {
  GooglePickerConfigDto,
  GooglePickerSessionResponseDto,
  GooglePickerAuthUrlResponseDto,
  createSessionResponse,
  ChatSessionData,
  GooglePickerSessionData,
  isValidGooglePickerSession,
  isSessionExpired
} from '../dto/google-picker-session.dto';

@ApiTags(SWAGGER_API_TAGS.GOOGLE_PICKER)
@Controller('chat/google-picker')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class GooglePickerController {
  private readonly logger = new Logger(GooglePickerController.name);

  constructor(
    private readonly googlePickerService: GooglePickerService,
  ) {}

  /**
   * Lấy URL xác thực Google OAuth
   */
  @Get('url')
  @ApiOperation({ 
    summary: 'Lấy URL xác thực Google OAuth',
    description: 'Tạo URL để redirect user đến Google OAuth cho Google Picker trong chat'
  })
  @ApiQuery({
    name: 'callback',
    description: 'Endpoint callback tùy chọn',
    required: false,
    example: '/chat-callback'
  })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực được tạo thành công',
    type: GooglePickerAuthUrlResponseDto
  })
  async getAuthorizationUrl(
    @Query('callback') endpointCallback?: string
  ): Promise<ApiResponseDto<GooglePickerAuthUrlResponseDto>> {
    try {
      const result = this.googlePickerService.getAuthorizationUrl(endpointCallback);
      return ApiResponseDto.success(result, 'URL xác thực được tạo thành công');
    } catch (error) {
      this.logger.error('Error getting authorization URL:', error);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Xử lý callback từ Google OAuth
   */
  @Get('callback')
  @ApiOperation({ 
    summary: 'Xử lý callback từ Google OAuth',
    description: 'Endpoint nhận callback từ Google sau khi user xác thực cho chat'
  })
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Session() session: ChatSessionData,
    @Res() res: Response,
    @Query('error') error?: string
  ) {
    try {
      if (error) {
        return this.sendClosePopupScript(res, { error: error });
      }

      if (!code || !state) {
        return this.sendClosePopupScript(res, { 
          error: 'Missing authorization code or state' 
        });
      }

      // Exchange code for tokens
      const tokens = await this.googlePickerService.exchangeCodeForTokens(code);
      
      // Lưu vào session
      session.googlePicker = {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: Date.now() + (tokens.expires_in * 1000),
        userInfo: tokens.userInfo,
        state: state
      };

      // Trả về script đóng popup
      return this.sendClosePopupScript(res, { 
        success: true, 
        message: 'Xác thực thành công cho chat' 
      });

    } catch (error) {
      this.logger.error('Chat Google Picker callback error:', error);
      return this.sendClosePopupScript(res, { 
        error: 'Xác thực thất bại cho chat' 
      });
    }
  }

  /**
   * Lấy session và config cho Google Picker
   */
  @Get('session')
  @ApiOperation({ 
    summary: 'Lấy session và config cho Google Picker',
    description: 'Trả về access token và cấu hình cần thiết để khởi tạo Google Picker cho chat'
  })
  @ApiResponse({
    status: 200,
    description: 'Session và config được lấy thành công',
    type: GooglePickerSessionResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa được xác thực với Google cho chat'
  })
  async getSession(
    @Session() session: ChatSessionData
  ): Promise<ApiResponseDto<GooglePickerSessionResponseDto>> {
    try {
      // Kiểm tra session tồn tại
      if (!session.googlePicker || !isValidGooglePickerSession(session.googlePicker)) {
        throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      }

      const pickerSession = session.googlePicker;

      // Kiểm tra và refresh token nếu cần
      const validatedSession = await this.validateAndRefreshToken(pickerSession, session);

      // Lấy cấu hình Picker
      const config = this.googlePickerService.getPickerConfigDto(validatedSession.accessToken);

      // Tạo response DTO
      const responseData = createSessionResponse(
        validatedSession.accessToken,
        validatedSession.userInfo,
        config,
        validatedSession.expiresAt
      );

      return ApiResponseDto.success(
        responseData,
        'Lấy session và config Google Picker cho chat thành công'
      );

    } catch (error) {
      this.logger.error('Error getting Chat Google Picker session:', error);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Lấy cấu hình Google Picker
   */
  @Get('config')
  @ApiOperation({ 
    summary: 'Lấy cấu hình Google Picker',
    description: 'Trả về cấu hình cần thiết để khởi tạo Google Picker cho chat (không cần authentication)'
  })
  @ApiResponse({
    status: 200,
    description: 'Cấu hình được lấy thành công',
    type: GooglePickerConfigDto
  })
  async getConfigOnly(): Promise<ApiResponseDto<GooglePickerConfigDto>> {
    try {
      const config = this.googlePickerService.getPickerConfigDto();
      return ApiResponseDto.success(config, 'Lấy cấu hình Google Picker cho chat thành công');
    } catch (error) {
      this.logger.error('Error getting Chat Google Picker config:', error);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Xóa session Google Picker
   */
  @Delete('session')
  @ApiOperation({ 
    summary: 'Xóa session Google Picker',
    description: 'Đăng xuất và xóa thông tin session Google Picker cho chat'
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được xóa thành công'
  })
  async clearSession(
    @Session() session: ChatSessionData
  ): Promise<ApiResponseDto<null>> {
    try {
      delete session.googlePicker;
      return ApiResponseDto.success(null, 'Đã xóa session Google Picker cho chat');
    } catch (error) {
      this.logger.error('Error clearing Chat Google Picker session:', error);
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  // ========================================================================
  // PRIVATE HELPER METHODS
  // ========================================================================

  /**
   * Kiểm tra và refresh token nếu cần thiết
   */
  private async validateAndRefreshToken(
    pickerSession: GooglePickerSessionData,
    session: ChatSessionData
  ): Promise<GooglePickerSessionData> {
    // Kiểm tra token còn hạn không (buffer 5 phút)
    if (isSessionExpired(pickerSession, 5) && pickerSession.refreshToken) {
      try {
        this.logger.log('Refreshing Google access token for chat...');
        
        // Refresh token
        const newTokens = await this.googlePickerService.refreshAccessToken(
          pickerSession.refreshToken
        );

        // Cập nhật session
        const updatedSession: GooglePickerSessionData = {
          ...pickerSession,
          accessToken: newTokens.access_token,
          expiresAt: Date.now() + (newTokens.expires_in * 1000)
        };

        session.googlePicker = updatedSession;
        
        this.logger.log('Google access token refreshed successfully for chat');
        return updatedSession;

      } catch (error) {
        this.logger.error('Failed to refresh Google token for chat:', error);
        
        // Xóa session không hợp lệ
        delete session.googlePicker;
        
        throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      }
    }

    return pickerSession;
  }

  /**
   * Gửi script để đóng popup và thông báo kết quả
   */
  private sendClosePopupScript(res: Response, data: any) {
    const script = `
      <script>
        try {
          // Gửi message về parent window (FE)
          if (window.opener) {
            window.opener.postMessage({
              type: 'CHAT_GOOGLE_PICKER_AUTH_RESULT',
              data: ${JSON.stringify(data)}
            }, '*');
          }
          
          // Đóng popup
          window.close();
        } catch (error) {
          console.error('Error closing popup:', error);
          // Fallback: redirect về trang chính
          window.location.href = '/';
        }
      </script>
    `;
    
    res.setHeader('Content-Type', 'text/html');
    res.send(script);
  }
}
