import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Enum cho loại biểu đồ admin
 */
export enum AdminChartDataType {
  TRANSACTION = 'TRANSACTION',
  AMOUNT = 'AMOUNT',
  POINT = 'POINT',
  USER = 'USER'
}

/**
 * DTO cho truy vấn biểu đồ dashboard admin
 */
export class AdminDashboardChartQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thờ<PERSON> gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu biểu đồ
   */
  @ApiProperty({
    description: 'Loại dữ liệu biểu đồ',
    enum: AdminChartDataType,
    example: AdminChartDataType.TRANSACTION,
    required: false,
    default: AdminChartDataType.TRANSACTION
  })
  @IsOptional()
  @IsEnum(AdminChartDataType)
  type?: AdminChartDataType;

  /**
   * ID người dùng cụ thể (optional, nếu muốn xem của 1 user)
   */
  @ApiProperty({
    description: 'ID người dùng cụ thể (optional)',
    example: 123,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  userId?: number;
}
