import {
  Controller,
  Post,
  Param,
  UseGuards,
  ParseU<PERSON><PERSON>ip<PERSON>,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ExecutionUserService } from '../services/execution-user.service';
import { WorkflowExecutionResponseDto, NodeExecutionResponseDto } from '../../dto/response/execution-response.dto';

/**
 * Controller xử lý các API execution cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  WorkflowExecutionResponseDto,
  NodeExecutionResponseDto,
  ApiErrorResponseDto,
)
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('user/workflow')
export class ExecutionUserController {
  constructor(private readonly executionUserService: ExecutionUserService) {}

  /**
   * Thực thi workflow
   */
  @Post(':workflowId/execute')
  @ApiOperation({ 
    summary: 'Thực thi workflow',
    description: 'Bắt đầu thực thi một workflow. API này sẽ tạo một execution record và queue job để xử lý workflow.'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần thực thi',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution đã được khởi tạo thành công',
    schema: ApiResponseDto.getSchema(WorkflowExecutionResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy workflow',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền thực thi workflow này',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Workflow không thể thực thi (inactive hoặc invalid)',
    type: ApiErrorResponseDto,
  })
  async executeWorkflow(
    @CurrentUser('id') userId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
  ): Promise<ApiResponseDto<WorkflowExecutionResponseDto>> {
    const result = await this.executionUserService.executeWorkflow(userId, workflowId);
    return ApiResponseDto.success(result, 'Workflow execution đã được khởi tạo thành công');
  }

  /**
   * Thực thi node đơn lẻ
   */
  @Post(':workflowId/node/:nodeId/execute')
  @ApiOperation({ 
    summary: 'Thực thi node đơn lẻ',
    description: 'Thực thi một node cụ thể trong workflow. Thường được sử dụng để test hoặc debug node.'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow chứa node',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'nodeId',
    description: 'ID của node cần thực thi',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node execution đã được khởi tạo thành công',
    schema: ApiResponseDto.getSchema(NodeExecutionResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy workflow hoặc node',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền thực thi node này',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Node không thể thực thi (disabled hoặc invalid configuration)',
    type: ApiErrorResponseDto,
  })
  async executeNode(
    @CurrentUser('id') userId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
  ): Promise<ApiResponseDto<NodeExecutionResponseDto>> {
    const result = await this.executionUserService.executeNode(userId, workflowId, nodeId);
    return ApiResponseDto.success(result, 'Node execution đã được khởi tạo thành công');
  }
}
