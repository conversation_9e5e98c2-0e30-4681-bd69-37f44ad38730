import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError, AxiosResponse } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Interface cho response từ automation-web API
 */
export interface AutomationWebResponse<T = any> {
  result?: T;
  code?: number;
  message?: string;
  error?: string;
}

/**
 * Interface cho request options
 */
export interface AutomationWebRequestOptions {
  timeout?: number;
  headers?: Record<string, string>;
}

/**
 * Service để tương tác với automation-web API
 * Cung cấp các phương thức để gọi API từ automation-web service
 */
@Injectable()
export class AutomationWebService {
  private readonly logger = new Logger(AutomationWebService.name);
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly timeout: number;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl =
      this.configService.get<string>('AUTOMATION_WEB_API_URL') ||
      'http://localhost:8080';
    this.apiKey =
      this.configService.get<string>('AUTOMATION_WEB_API_KEY') || '';
    this.timeout =
      this.configService.get<number>('AUTOMATION_WEB_TIMEOUT') || 30000;

    this.logger.log(
      `AutomationWebService initialized with baseUrl: ${this.baseUrl}`,
    );
  }

  /**
   * Tạo headers mặc định cho request
   */
  private getDefaultHeaders(
    customHeaders?: Record<string, string>,
  ): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
      // Hoặc sử dụng API Key header tùy theo automation-web API
      headers['X-API-Key'] = this.apiKey;
    }

    return { ...headers, ...customHeaders };
  }

  /**
   * Xử lý lỗi từ automation-web API
   */
  private handleError(error: AxiosError, context: string): never {
    this.logger.error(`AutomationWeb API Error in ${context}:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });

    if (error.response) {
      if (error.response.status === 401) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Automation-web API authentication failed',
        );
      }
      if (error.response.status === 403) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'Automation-web API access forbidden',
        );
      }
      if (error.response.status === 404) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Automation-web API endpoint not found',
        );
      }
      if (error.response.status >= 500) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Automation-web API server error',
        );
      }
    }
    throw new AppException(
      ErrorCode.BAD_REQUEST,
      `Automation-web API error: ${error.message}`,
    );
  }

  /**
   * Thực hiện GET request
   */
  async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    options?: AutomationWebRequestOptions,
  ): Promise<AutomationWebResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const headers = this.getDefaultHeaders(options?.headers);
      const timeout = options?.timeout || this.timeout;

      this.logger.debug(`GET request to: ${url}`, { params });

      const response: AxiosResponse<AutomationWebResponse<T>> =
        await lastValueFrom(
          this.httpService.get(url, {
            params,
            headers,
            timeout,
          }),
        );

      this.logger.debug(`GET response from ${url}:`, response.data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError, `GET ${endpoint}`);
    }
  }

  /**
   * Thực hiện POST request
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    options?: AutomationWebRequestOptions,
  ): Promise<AutomationWebResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const headers = this.getDefaultHeaders(options?.headers);
      const timeout = options?.timeout || this.timeout;

      this.logger.debug(`POST request to: ${url}`, { data });

      const response: AxiosResponse<AutomationWebResponse<T>> =
        await lastValueFrom(
          this.httpService.post(url, data, {
            headers,
            timeout,
          }),
        );

      this.logger.debug(`POST response from ${url}:`, response.data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError, `POST ${endpoint}`);
    }
  }

  /**
   * Thực hiện PUT request
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    options?: AutomationWebRequestOptions,
  ): Promise<AutomationWebResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const headers = this.getDefaultHeaders(options?.headers);
      const timeout = options?.timeout || this.timeout;

      this.logger.debug(`PUT request to: ${url}`, { data });

      const response: AxiosResponse<AutomationWebResponse<T>> =
        await lastValueFrom(
          this.httpService.put(url, data, {
            headers,
            timeout,
          }),
        );

      this.logger.debug(`PUT response from ${url}:`, response.data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError, `PUT ${endpoint}`);
    }
  }

  /**
   * Thực hiện DELETE request
   */
  async delete<T = any>(
    endpoint: string,
    options?: AutomationWebRequestOptions,
  ): Promise<AutomationWebResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const headers = this.getDefaultHeaders(options?.headers);
      const timeout = options?.timeout || this.timeout;

      this.logger.debug(`DELETE request to: ${url}`);

      const response: AxiosResponse<AutomationWebResponse<T>> =
        await lastValueFrom(
          this.httpService.delete(url, {
            headers,
            timeout,
          }),
        );

      this.logger.debug(`DELETE response from ${url}:`, response.data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError, `DELETE ${endpoint}`);
    }
  }

  /**
   * Kiểm tra kết nối với automation-web API
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.get('/health');
      return response.code === 200;
    } catch (error) {
      this.logger.warn('Automation-web health check failed:', error.message);
      return false;
    }
  }

  /**
   * Tạo QR code session cho Zalo Personal login
   */
  async createZaloQRCodeSession(sessionId: string): Promise<
    AutomationWebResponse<{
      qr_code_base64: string;
      session_id: string;
      expires_at: number;
    }>
  > {
    try {
      this.logger.log(
        `Creating Zalo QR code session - sessionId: ${sessionId}`,
      );

      const response = await this.post<{
        qr_code_base64: string;
        session_id: string;
        expires_at: number;
      }>('/zalo/qr-code', {
        integration_id: sessionId,
      });

      this.logger.log('Zalo QR code session created successfully:', {
        sessionId: response.result?.session_id,
        hasQrCodeUrl: !!response.result?.qr_code_base64,
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to create Zalo QR code session:', {
        sessionId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Lấy thông tin cấu hình hiện tại
   */
  getConfig() {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      hasApiKey: !!this.apiKey,
    };
  }
}
