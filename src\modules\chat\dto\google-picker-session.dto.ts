/**
 * @file Google Picker Session DTOs for Chat Module
 * 
 * DTOs và interfaces cho Google Picker session trong chat module
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsNumber, IsObject, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Interface cho Google Picker session data
 */
export interface GooglePickerSessionData {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  userInfo: {
    id: string;
    email: string;
    name: string;
    picture: string;
  };
  state: string;
}

/**
 * Interface cho session data của chat module
 */
export interface ChatSessionData {
  googlePicker?: GooglePickerSessionData;
}

/**
 * DTO cho user info trong Google Picker
 */
export class GooglePickerUserInfoDto {
  @ApiProperty({ 
    description: 'Google user ID',
    example: '123456789'
  })
  @IsString()
  id: string;

  @ApiProperty({ 
    description: '<PERSON>ail của user',
    example: '<EMAIL>'
  })
  @IsString()
  email: string;

  @ApiProperty({ 
    description: 'Tên hiển thị của user',
    example: '<PERSON>e'
  })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: 'URL avatar của user',
    example: 'https://lh3.googleusercontent.com/...'
  })
  @IsString()
  picture: string;
}

/**
 * DTO cho Google Picker configuration
 */
export class GooglePickerConfigDto {
  @ApiProperty({ 
    description: 'Google Client ID',
    example: '123456789-abc.apps.googleusercontent.com'
  })
  @IsString()
  clientId: string;

  @ApiProperty({ 
    description: 'Google API Key',
    example: 'AIzaSyC...'
  })
  @IsString()
  apiKey: string;

  @ApiProperty({ 
    description: 'Google App ID',
    example: '123456789'
  })
  @IsString()
  appId: string;

  @ApiProperty({ 
    description: 'Access token (optional)',
    required: false
  })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiProperty({ 
    description: 'OAuth scopes',
    example: [
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/drive.metadata.readonly'
    ]
  })
  scopes: string[];

  @ApiProperty({ 
    description: 'Picker view IDs',
    example: {
      docs: 'DOCS',
      images: 'DOCS_IMAGES',
      videos: 'DOCS_VIDEOS',
      folders: 'FOLDERS'
    }
  })
  viewIds: {
    docs: string;
    images: string;
    videos: string;
    folders: string;
  };

  @ApiProperty({ 
    description: 'Picker features',
    example: {
      multiselect: true,
      nav_hidden: false,
      upload: false
    }
  })
  features: {
    multiselect: boolean;
    nav_hidden: boolean;
    upload: boolean;
  };

  @ApiProperty({ 
    description: 'Locale setting',
    example: 'vi'
  })
  @IsString()
  locale: string;
}

/**
 * DTO cho session response
 */
export class GooglePickerSessionResponseDto {
  @ApiProperty({ 
    description: 'Access token để gọi Google APIs',
    example: 'ya29.a0AfH6SMC...'
  })
  @IsString()
  accessToken: string;

  @ApiProperty({ 
    description: 'Thông tin user từ Google',
    type: GooglePickerUserInfoDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => GooglePickerUserInfoDto)
  userInfo: GooglePickerUserInfoDto;

  @ApiProperty({ 
    description: 'Trạng thái xác thực',
    example: true
  })
  @IsBoolean()
  isAuthenticated: boolean;

  @ApiProperty({ 
    description: 'Cấu hình Google Picker',
    type: GooglePickerConfigDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => GooglePickerConfigDto)
  config: GooglePickerConfigDto;

  @ApiProperty({ 
    description: 'Thời gian token hết hạn (timestamp)',
    example: *************,
    required: false
  })
  @IsOptional()
  @IsNumber()
  expiresAt?: number;

  @ApiProperty({ 
    description: 'Thời gian còn lại của token (seconds)',
    example: 3600,
    required: false
  })
  @IsOptional()
  @IsNumber()
  expiresIn?: number;
}

/**
 * DTO cho authorization URL response
 */
export class GooglePickerAuthUrlResponseDto {
  @ApiProperty({ 
    description: 'URL để redirect user đến Google OAuth',
    example: 'https://accounts.google.com/oauth/authorize?...'
  })
  @IsString()
  url: string;

  @ApiProperty({ 
    description: 'State token để xác thực callback',
    example: 'abc123-def456-ghi789'
  })
  @IsString()
  state: string;
}

/**
 * Helper functions
 */

/**
 * Type guard để kiểm tra GooglePickerSessionData hợp lệ
 */
export function isValidGooglePickerSession(
  session: any
): session is GooglePickerSessionData {
  return (
    session &&
    typeof session.accessToken === 'string' &&
    typeof session.refreshToken === 'string' &&
    typeof session.expiresAt === 'number' &&
    session.userInfo &&
    typeof session.userInfo.id === 'string' &&
    typeof session.userInfo.email === 'string' &&
    typeof session.userInfo.name === 'string' &&
    typeof session.state === 'string'
  );
}

/**
 * Helper function để check session expiry
 */
export function isSessionExpired(
  session: GooglePickerSessionData,
  bufferMinutes: number = 5
): boolean {
  const bufferTime = bufferMinutes * 60 * 1000;
  return Date.now() >= (session.expiresAt - bufferTime);
}

/**
 * Helper function để tạo session response
 */
export function createSessionResponse(
  accessToken: string,
  userInfo: GooglePickerUserInfoDto,
  config: GooglePickerConfigDto,
  expiresAt?: number
): GooglePickerSessionResponseDto {
  const response: GooglePickerSessionResponseDto = {
    accessToken,
    userInfo,
    isAuthenticated: true,
    config
  };

  if (expiresAt) {
    response.expiresAt = expiresAt;
    response.expiresIn = Math.max(0, Math.floor((expiresAt - Date.now()) / 1000));
  }

  return response;
}

/**
 * Factory function để tạo default config
 */
export function createDefaultGooglePickerConfig(): Partial<GooglePickerConfigDto> {
  return {
    scopes: [
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/drive.metadata.readonly'
    ],
    viewIds: {
      docs: 'DOCS',
      images: 'DOCS_IMAGES',
      videos: 'DOCS_VIDEOS',
      folders: 'FOLDERS'
    },
    features: {
      multiselect: true,
      nav_hidden: false,
      upload: false
    },
    locale: 'vi'
  };
}
