import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * Enum cho loại dữ liệu khách hàng tiềm năng
 */
export enum PotentialCustomersDataType {
  HIGH_VALUE = 'HIGH_VALUE',
  FREQUENT_BUYERS = 'FREQUENT_BUYERS',
  RECENT_INACTIVE = 'RECENT_INACTIVE',
}

/**
 * DTO cho query parameters của API khách hàng tiềm năng
 * Tương tự như DashboardChartQueryDto từ r-point module
 */
export class PotentialCustomersQueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp seconds)',
    example: 1704067200,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp seconds)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp seconds)',
    example: 1735689599,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return isNaN(num) ? undefined : num;
  })
  end?: number;

  /**
   * Loại dữ liệu khách hàng tiềm năng
   */
  @ApiProperty({
    description: 'Loại dữ liệu khách hàng tiềm năng',
    enum: PotentialCustomersDataType,
    example: PotentialCustomersDataType.HIGH_VALUE,
    required: false,
    default: PotentialCustomersDataType.HIGH_VALUE,
  })
  @IsOptional()
  @IsEnum(PotentialCustomersDataType)
  type?: PotentialCustomersDataType;

  /**
   * Số lượng khách hàng tối đa trả về
   */
  @ApiProperty({
    description: 'Số lượng khách hàng tối đa trả về',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Giá trị đơn hàng tối thiểu
   */
  @ApiProperty({
    description: 'Giá trị đơn hàng tối thiểu',
    example: 100000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giá trị đơn hàng tối thiểu phải là số' })
  @Min(0, { message: 'Giá trị đơn hàng tối thiểu phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  minOrderValue?: number;

  /**
   * Số đơn hàng tối thiểu
   */
  @ApiProperty({
    description: 'Số đơn hàng tối thiểu',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số đơn hàng tối thiểu phải là số' })
  @Min(1, { message: 'Số đơn hàng tối thiểu phải lớn hơn 0' })
  @Type(() => Number)
  minOrderCount?: number;

  /**
   * Số ngày từ đơn hàng cuối cùng
   */
  @ApiProperty({
    description: 'Số ngày từ đơn hàng cuối cùng',
    example: 30,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số ngày từ đơn hàng cuối phải là số' })
  @Min(1, { message: 'Số ngày từ đơn hàng cuối phải lớn hơn 0' })
  @Type(() => Number)
  lastOrderDays?: number;
}
