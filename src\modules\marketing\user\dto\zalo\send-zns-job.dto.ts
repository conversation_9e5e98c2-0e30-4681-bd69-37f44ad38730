import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho gửi ZNS đơn lẻ
 */
export class SendSingleZnsDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Số điện thoại nhận',
    example: '0912345678',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Tracking ID (tùy chọn)',
    example: 'tracking_12345',
  })
  @IsOptional()
  @IsString()
  trackingId?: string;
}

/**
 * DTO cho tin nhắn trong batch
 */
export class BatchZnsMessageDto {
  @ApiProperty({
    description: 'Số điện thoại nhận',
    example: '0912345678',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Tracking ID (tùy chọn)',
    example: 'tracking_12345',
  })
  @IsOptional()
  @IsString()
  trackingId?: string;
}

/**
 * DTO cho gửi batch ZNS
 */
export class SendBatchZnsDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Danh sách tin nhắn ZNS',
    type: [BatchZnsMessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchZnsMessageDto)
  messages: BatchZnsMessageDto[];

  @ApiPropertyOptional({
    description: 'Batch index (thứ tự batch)',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  batchIndex?: number;

  @ApiPropertyOptional({
    description: 'Tổng số batch',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  totalBatches?: number;
}

/**
 * DTO cho response job
 */
export class ZnsJobResponseDto {
  @ApiProperty({
    description: 'ID của job',
    example: '12345',
  })
  jobId: string;
}
