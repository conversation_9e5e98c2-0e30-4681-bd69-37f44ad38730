import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { NodeExecutionResponseDto, WorkflowExecutionResponseDto } from '../../dto/response/execution-response.dto';

/**
 * Service xử lý execution cho user
 */
@Injectable()
export class ExecutionUserService {
  private readonly logger = new Logger(ExecutionUserService.name);

  constructor(
    @Inject('BE_WORKER_SERVICE') private readonly client: ClientProxy,
  ) { }

  /**
   * Thực thi workflow
   * @param userId ID của user
   * @param workflowId ID của workflow
   * @returns Promise<WorkflowExecutionResponseDto>
   */
  async executeWorkflow(
    userId: number,
    workflowId: string,
  ): Promise<WorkflowExecutionResponseDto> {
    this.logger.log(`Executing workflow ${workflowId} for user ${userId}`);

    // TODO: Implement workflow execution logic
    // 1. Validate workflow exists and user has permission
    // 2. Create execution record
    // 3. Queue workflow execution job
    // 4. Return execution response

    throw new Error('Method not implemented');
  }

  /**
   * Thực thi node đơn lẻ
   * @param userId ID của user
   * @param workflowId ID của workflow
   * @param nodeId ID của node
   * @returns Promise<NodeExecutionResponseDto>
   */
  async executeNode(
    userId: number,
    workflowId: string,
    nodeId: string,
  ): Promise<NodeExecutionResponseDto> {
    this.logger.log(`Executing node ${nodeId} in workflow ${workflowId} for user ${userId}`);

    try {
      const response = await this.client
        .send<Record<string, any>>('user_execute_node', {
          userId,
          workflowId,
          nodeId,
        });

      return { result: response };
    } catch (error) {
      this.logger.error(`Error executing node ${nodeId} in workflow ${workflowId} for user ${userId}:`, error);
      throw error;
    }
  }
}
