import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { AppException, ErrorCode } from '@/common/exceptions';
import { PaginatedResult } from '@/common/response';
import { MARKETING_ERROR_CODES } from '../../errors/marketing-error.code';
import { ZaloZnsCampaignRepository } from '../repositories/zalo-zns-campaign.repository';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { ZaloOAIntegrationService } from '@/modules/integration/services/zalo-oa-integration.service';
import { ZaloOAMetadata } from '@/modules/integration/interfaces/zalo-oa-metadata.interface';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import {
  ZaloZnsCampaign,
  ZaloZnsCampaignStatus,
} from '../entities/zalo-zns-campaign.entity';
import {
  CreateZnsCampaignDto,
  CreatePersonalizedZnsCampaignDto,
  UpdateZnsCampaignDto,
  ZnsCampaignQueryDto,
  TargetAudienceType,
} from '../dto/zalo/zns-campaign.dto';
import { ZnsPersonalizationService } from './zns-personalization.service';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { QueueName, ZaloZnsJobName } from '@/shared/queue/queue.constants';
import {
  SendZnsCampaignJobData,
  SendZnsJobData,
  SendBatchZnsJobData,
} from '@/shared/queue/interfaces';

/**
 * Service xử lý chiến dịch ZNS
 */
@Injectable()
export class ZaloZnsCampaignService {
  private readonly logger = new Logger(ZaloZnsCampaignService.name);

  constructor(
    private readonly znsCampaignRepository: ZaloZnsCampaignRepository,
    private readonly zaloOfficialAccountRepository: ZaloOALegacyWrapperService,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userAudienceCustomFieldRepository: UserAudienceCustomFieldRepository,
    private readonly zaloOAIntegrationService: ZaloOAIntegrationService,
    private readonly znsPersonalizationService: ZnsPersonalizationService,
    @InjectQueue(QueueName.ZALO_ZNS) private readonly znsQueue: Queue,
  ) {}

  /**
   * Helper method để lấy oaId từ integrationId
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @returns oaId từ metadata của Integration
   */
  private async getOaIdFromIntegrationId(
    userId: number,
    integrationId: string,
  ): Promise<string> {
    try {
      const integration =
        await this.zaloOAIntegrationService.getZaloOAIntegrationById(
          integrationId,
        );

      // Kiểm tra quyền truy cập
      if (integration.userId !== userId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không có quyền truy cập Integration này',
        );
      }

      // Lấy oaId từ metadata
      const metadata = integration.metadata as ZaloOAMetadata;
      if (!metadata?.oaId) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không tìm thấy oaId trong metadata của Integration',
        );
      }

      return metadata.oaId;
    } catch (error) {
      this.logger.error(
        `Failed to get oaId from integrationId ${integrationId}: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
        'Không tìm thấy Integration hoặc Integration không hợp lệ',
      );
    }
  }

  /**
   * Helper method để lấy integrationId từ oaId
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns integrationId từ Integration entity
   */
  private async getIntegrationIdFromOaId(
    userId: number,
    oaId: string,
  ): Promise<string | undefined> {
    try {
      const integrations =
        await this.zaloOAIntegrationService.getZaloOAIntegrationsByUserId(
          userId,
        );

      for (const integration of integrations) {
        const metadata = integration.metadata as ZaloOAMetadata;
        if (metadata?.oaId === oaId) {
          return integration.id;
        }
      }

      return undefined;
    } catch (error) {
      this.logger.error(
        `Failed to get integrationId from oaId ${oaId}: ${error.message}`,
      );
      return undefined;
    }
  }

  /**
   * Tạo chiến dịch ZNS mới
   */
  async createCampaign(
    userId: number,
    integrationId: string,
    createDto: CreateZnsCampaignDto,
  ): Promise<ZaloZnsCampaign> {
    try {
      // Lấy oaId từ integrationId
      const oaId = await this.zaloOAIntegrationService.getOaIdFromIntegrationId(
        integrationId,
        userId,
        undefined, // employeeId
      );

      // Kiểm tra quyền truy cập Official Account
      const officialAccount =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      if (!officialAccount) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không tìm thấy Official Account',
        );
      }

      // Xử lý đối tượng đầu ra và lấy danh sách số điện thoại
      let phoneList: string[] = [];
      let totalMessages = 0;

      if (createDto.targetAudienceType === TargetAudienceType.PHONE_LIST) {
        if (!createDto.phoneList || createDto.phoneList.length === 0) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Danh sách số điện thoại không được để trống',
          );
        }
        phoneList = createDto.phoneList;
        totalMessages = phoneList.length;
      } else if (createDto.targetAudienceType === TargetAudienceType.SEGMENT) {
        if (!createDto.segmentId) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Segment ID không được để trống',
          );
        }
        // Kiểm tra segment tồn tại và thuộc về user
        const segment = await this.userSegmentRepository.findByIdAndUserId(
          createDto.segmentId,
          userId,
        );
        if (!segment) {
          throw new AppException(
            MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND,
            'Không tìm thấy segment',
          );
        }
        // Lấy audience từ segment (tạm thời lấy tất cả audience của user)
        const audiences =
          await this.userAudienceRepository.findBySegmentConditions(
            userId,
            segment.criteria || [],
          );
        phoneList = audiences
          .map((a) => a.phoneNumber)
          .filter((phone): phone is string => phone !== null);
        totalMessages = phoneList.length;
      } else if (
        createDto.targetAudienceType === TargetAudienceType.AUDIENCE_LIST
      ) {
        if (!createDto.audienceIds || createDto.audienceIds.length === 0) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Danh sách audience ID không được để trống',
          );
        }
        // Lấy audience theo IDs
        const audiences = await this.userAudienceRepository.findByIdsAndUserId(
          createDto.audienceIds,
          userId,
        );
        if (audiences.length !== createDto.audienceIds.length) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Một số audience không tồn tại hoặc không thuộc về user',
          );
        }
        phoneList = audiences
          .map((a) => a.phoneNumber)
          .filter((phone): phone is string => phone !== null);
        totalMessages = phoneList.length;
      }

      if (totalMessages === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Không có số điện thoại hợp lệ để gửi',
        );
      }

      const now = Date.now();

      // Tạo chiến dịch
      const campaign = await this.znsCampaignRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        templateId: createDto.templateId,
        templateData: createDto.templateData,
        phoneList:
          createDto.targetAudienceType === TargetAudienceType.PHONE_LIST
            ? createDto.phoneList
            : undefined,
        segmentId: createDto.segmentId,
        audienceIds: createDto.audienceIds,
        totalMessages,
        status: createDto.status,
        scheduledAt: createDto.scheduledAt,
        createdAt: now,
        updatedAt: now,
      });

      // Nếu trạng thái là SCHEDULED và có scheduledAt, tạo job cho queue
      if (
        createDto.status === ZaloZnsCampaignStatus.SCHEDULED &&
        createDto.scheduledAt
      ) {
        const jobData: SendZnsCampaignJobData = {
          campaignId: campaign.id,
          oaId: campaign.oaId,
          templateId: campaign.templateId,
          templateData: campaign.templateData,
          phoneList: phoneList,
          batchSize: 10,
          batchDelay: 1000,
        };

        const job = await this.znsQueue.add(
          ZaloZnsJobName.SEND_ZNS_CAMPAIGN,
          jobData,
          {
            delay: createDto.scheduledAt - Date.now(), // Delay đến thời gian lên lịch
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
            removeOnComplete: 10,
            removeOnFail: 50,
          },
        );

        // Lưu job ID
        await this.znsCampaignRepository.update(campaign.id, {
          jobIds: [job.id.toString()],
          updatedAt: Date.now(),
        });

        this.logger.log(
          `Scheduled ZNS campaign ${campaign.id} with job ${job.id} for ${new Date(createDto.scheduledAt)}`,
        );
      }

      this.logger.log(`Created ZNS campaign ${campaign.id} for user ${userId}`);
      return campaign;
    } catch (error) {
      this.logger.error(`Failed to create ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo chiến dịch ZNS',
      );
    }
  }

  /**
   * Tạo chiến dịch ZNS với cá nhân hóa template data
   * @param userId ID của người dùng
   * @param integrationId ID của Integration
   * @param createDto DTO tạo chiến dịch với cá nhân hóa
   * @returns Chiến dịch ZNS đã tạo
   */
  async createPersonalizedCampaign(
    userId: number,
    integrationId: string,
    createDto: CreatePersonalizedZnsCampaignDto,
  ): Promise<ZaloZnsCampaign> {
    try {
      // Lấy oaId từ integrationId
      const oaId = await this.zaloOAIntegrationService.getOaIdFromIntegrationId(
        integrationId,
        userId,
        undefined, // employeeId
      );

      this.logger.log(
        `Creating personalized ZNS campaign for user ${userId}, OA ${oaId}`,
      );

      // Validate cấu hình cá nhân hóa
      if (
        !this.znsPersonalizationService.validatePersonalizationConfig(
          createDto.personalizedTemplateData,
        )
      ) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Cấu hình cá nhân hóa không hợp lệ',
        );
      }

      // Kiểm tra Official Account tồn tại
      const oa = await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
        userId,
        oaId,
      );
      if (!oa) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không tìm thấy Official Account',
        );
      }

      // Lấy danh sách audience và tính tổng số tin nhắn
      let phoneList: string[] = [];
      let totalMessages = 0;
      let audiencesWithCustomFields: Array<{
        audience: any;
        customFields: any[];
      }> = [];

      if (createDto.targetAudienceType === TargetAudienceType.PHONE_LIST) {
        if (!createDto.phoneList || createDto.phoneList.length === 0) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Danh sách số điện thoại không được để trống',
          );
        }
        phoneList = createDto.phoneList;
        totalMessages = phoneList.length;
      } else if (createDto.targetAudienceType === TargetAudienceType.SEGMENT) {
        if (!createDto.segmentId) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Segment ID không được để trống',
          );
        }

        // Lấy audiences từ segment với custom fields
        const segment = await this.userSegmentRepository.findByIdAndUserId(
          createDto.segmentId,
          userId,
        );
        if (!segment) {
          throw new AppException(
            MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND,
            'Không tìm thấy segment',
          );
        }

        const audiences =
          await this.userAudienceRepository.findBySegmentConditions(
            userId,
            segment.criteria || [],
          );

        // Lấy custom fields cho từng audience
        for (const audience of audiences) {
          const customFields =
            await this.userAudienceCustomFieldRepository.find({
              where: { audienceId: audience.id },
            });
          audiencesWithCustomFields.push({ audience, customFields });
          if (audience.phoneNumber) {
            phoneList.push(audience.phoneNumber);
          }
        }
        totalMessages = phoneList.length;
      } else if (
        createDto.targetAudienceType === TargetAudienceType.AUDIENCE_LIST
      ) {
        if (!createDto.audienceIds || createDto.audienceIds.length === 0) {
          throw new AppException(
            MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
            'Danh sách audience ID không được để trống',
          );
        }

        const audiences = await this.userAudienceRepository.findByIdsAndUserId(
          createDto.audienceIds,
          userId,
        );

        // Lấy custom fields cho từng audience
        for (const audience of audiences) {
          const customFields =
            await this.userAudienceCustomFieldRepository.find({
              where: { audienceId: audience.id },
            });
          audiencesWithCustomFields.push({ audience, customFields });
          if (audience.phoneNumber) {
            phoneList.push(audience.phoneNumber);
          }
        }
        totalMessages = phoneList.length;
      }

      if (totalMessages === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Không có số điện thoại hợp lệ để gửi',
        );
      }

      const now = Date.now();

      // Tạo chiến dịch với cấu hình cá nhân hóa
      const campaign = await this.znsCampaignRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        templateId: createDto.templateId,
        templateData: createDto.personalizedTemplateData.commonData || {}, // Lưu common data
        personalizationConfig: createDto.personalizedTemplateData, // Lưu cấu hình cá nhân hóa
        phoneList:
          createDto.targetAudienceType === TargetAudienceType.PHONE_LIST
            ? createDto.phoneList
            : undefined,
        segmentId: createDto.segmentId,
        audienceIds: createDto.audienceIds,
        totalMessages,
        status: createDto.status || ZaloZnsCampaignStatus.DRAFT,
        scheduledAt: createDto.scheduledAt,
        createdAt: now,
        updatedAt: now,
      });

      this.logger.log(
        `Created personalized ZNS campaign ${campaign.id} for user ${userId}`,
      );
      return campaign;
    } catch (error) {
      this.logger.error(
        `Failed to create personalized ZNS campaign: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo chiến dịch ZNS cá nhân hóa',
      );
    }
  }

  /**
   * Preview template data cá nhân hóa cho một audience
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @param personalizationConfig Cấu hình cá nhân hóa
   * @returns Preview data
   */
  async previewPersonalizedTemplateData(
    userId: number,
    audienceId: number,
    personalizationConfig: any,
  ): Promise<{
    templateData: Record<string, any>;
    mappedFields: Record<string, any>;
    unmappedFields: string[];
  }> {
    try {
      // Lấy thông tin audience
      const audience = await this.userAudienceRepository.findOne({
        where: { id: audienceId, userId },
      });
      if (!audience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
          'Không tìm thấy audience',
        );
      }

      // Lấy custom fields của audience
      const customFields = await this.userAudienceCustomFieldRepository.find({
        where: { audienceId },
      });

      // Tạo preview
      const preview = this.znsPersonalizationService.createPreviewTemplateData(
        audience,
        customFields,
        personalizationConfig,
      );

      this.logger.log(`Created preview for audience ${audienceId}`);
      return preview;
    } catch (error) {
      this.logger.error(
        `Failed to create preview for audience ${audienceId}: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo preview template data',
      );
    }
  }

  /**
   * Lấy danh sách chiến dịch theo userId với phân trang
   */
  async getCampaignsByUserId(
    userId: number,
    queryDto: ZnsCampaignQueryDto,
  ): Promise<PaginatedResult<ZaloZnsCampaign>> {
    try {
      let oaId: string | undefined;

      // Nếu có integrationId, chuyển đổi sang oaId
      if (queryDto.integrationId) {
        oaId = await this.getOaIdFromIntegrationId(
          userId,
          queryDto.integrationId,
        );
      }

      const result = await this.znsCampaignRepository.findWithPagination(
        userId,
        oaId, // oaId có thể null để lấy tất cả
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.status,
      );

      // Thêm integrationId vào mỗi campaign trong response
      const itemsWithIntegrationId = await Promise.all(
        result.items.map(async (campaign) => {
          const integrationId = await this.getIntegrationIdFromOaId(
            userId,
            campaign.oaId,
          );
          return {
            ...campaign,
            integrationId,
          };
        }),
      );

      return {
        ...result,
        items: itemsWithIntegrationId,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get ZNS campaigns by userId: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_GET_LIST_FAILED,
        'Không thể lấy danh sách chiến dịch ZNS',
      );
    }
  }

  /**
   * Lấy danh sách chiến dịch với phân trang (deprecated - sử dụng getCampaignsByUserId)
   */
  async getCampaigns(
    userId: number,
    oaId: string,
    queryDto: ZnsCampaignQueryDto,
  ): Promise<PaginatedResult<ZaloZnsCampaign>> {
    try {
      // Kiểm tra quyền truy cập Official Account
      const officialAccount =
        await this.zaloOfficialAccountRepository.findByUserIdAndOaId(
          userId,
          oaId,
        );
      if (!officialAccount) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không tìm thấy Official Account',
        );
      }

      return this.znsCampaignRepository.findWithPagination(
        userId,
        oaId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.status,
      );
    } catch (error) {
      this.logger.error(`Failed to get ZNS campaigns: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_GET_LIST_FAILED,
        'Không thể lấy danh sách chiến dịch ZNS',
      );
    }
  }

  /**
   * Lấy chi tiết chiến dịch
   */
  async getCampaignDetail(
    userId: number,
    campaignId: number,
  ): Promise<ZaloZnsCampaign & { integrationId?: string }> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch ZNS',
        );
      }

      // Thêm integrationId vào response
      const integrationId = await this.getIntegrationIdFromOaId(
        userId,
        campaign.oaId,
      );

      return {
        ...campaign,
        integrationId,
      };
    } catch (error) {
      this.logger.error(`Failed to get ZNS campaign detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
        'Không thể lấy chi tiết chiến dịch ZNS',
      );
    }
  }

  /**
   * Cập nhật chiến dịch
   */
  async updateCampaign(
    userId: number,
    campaignId: number,
    updateDto: UpdateZnsCampaignDto,
  ): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch ZNS',
        );
      }

      // Chỉ cho phép cập nhật chiến dịch ở trạng thái DRAFT hoặc SCHEDULED
      if (
        ![
          ZaloZnsCampaignStatus.DRAFT,
          ZaloZnsCampaignStatus.SCHEDULED,
        ].includes(campaign.status)
      ) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_UPDATE,
          'Không thể cập nhật chiến dịch đang chạy hoặc đã hoàn thành',
        );
      }

      const updateData: Partial<ZaloZnsCampaign> = {
        ...updateDto,
        updatedAt: Date.now(),
      };

      // Cập nhật tổng số tin nhắn nếu danh sách số điện thoại thay đổi
      if (updateDto.phoneList) {
        updateData.totalMessages = updateDto.phoneList.length;
      }

      // Cập nhật trạng thái nếu có scheduledAt
      if (updateDto.scheduledAt !== undefined) {
        updateData.status = updateDto.scheduledAt
          ? ZaloZnsCampaignStatus.SCHEDULED
          : ZaloZnsCampaignStatus.DRAFT;
      }

      const updatedCampaign = await this.znsCampaignRepository.update(
        campaignId,
        updateData,
      );
      if (!updatedCampaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED,
          'Không thể cập nhật chiến dịch ZNS',
        );
      }

      this.logger.log(`Updated ZNS campaign ${campaignId} for user ${userId}`);
      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to update ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED,
        'Không thể cập nhật chiến dịch ZNS',
      );
    }
  }

  /**
   * Xóa chiến dịch
   */
  async deleteCampaign(userId: number, campaignId: number): Promise<boolean> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch ZNS',
        );
      }

      // Chỉ cho phép xóa chiến dịch ở trạng thái DRAFT, SENT, FAILED
      if (campaign.status === ZaloZnsCampaignStatus.SCHEDULED) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_DELETE,
          'Không thể xóa chiến dịch đã lên lịch',
        );
      }

      const deleted = await this.znsCampaignRepository.delete(campaignId);
      if (!deleted) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED,
          'Không thể xóa chiến dịch ZNS',
        );
      }

      this.logger.log(`Deleted ZNS campaign ${campaignId} for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED,
        'Không thể xóa chiến dịch ZNS',
      );
    }
  }

  /**
   * Xóa nhiều chiến dịch ZNS
   */
  async bulkDeleteCampaigns(
    userId: number,
    campaignIds: number[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: number[];
    failedDeletes: number[];
    results: Array<{
      id: number;
      name: string;
      success: boolean;
      reason?: string;
    }>;
    message: string;
  }> {
    this.logger.log(
      `Bulk deleting ${campaignIds.length} ZNS campaigns for user ${userId}`,
    );

    if (!campaignIds || campaignIds.length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách ID chiến dịch không được để trống',
      );
    }

    // Lấy thông tin các chiến dịch cần xóa
    const campaigns = await this.znsCampaignRepository.findByIdsAndUserId(
      campaignIds,
      userId,
    );

    const foundIds = campaigns.map((campaign) => campaign.id);
    const notFoundIds = campaignIds.filter((id) => !foundIds.includes(id));

    const results: Array<{
      id: number;
      name: string;
      success: boolean;
      reason?: string;
    }> = [];

    const successfulDeletes: number[] = [];
    const failedDeletes: number[] = [];

    // Xử lý các chiến dịch không tìm thấy
    for (const id of notFoundIds) {
      results.push({
        id,
        name: 'Không xác định',
        success: false,
        reason: 'Không tìm thấy chiến dịch',
      });
      failedDeletes.push(id);
    }

    // Xử lý từng chiến dịch tìm thấy
    for (const campaign of campaigns) {
      try {
        // Kiểm tra trạng thái có thể xóa không
        if (campaign.status === ZaloZnsCampaignStatus.SCHEDULED) {
          results.push({
            id: campaign.id,
            name: campaign.name,
            success: false,
            reason: 'Không thể xóa chiến dịch đã lên lịch',
          });
          failedDeletes.push(campaign.id);
          continue;
        }

        // Không có status SENDING trong enum, chỉ kiểm tra SCHEDULED
        // Có thể thêm logic khác nếu cần thiết

        // Thực hiện xóa
        const deleted = await this.znsCampaignRepository.delete(campaign.id);
        if (deleted) {
          results.push({
            id: campaign.id,
            name: campaign.name,
            success: true,
          });
          successfulDeletes.push(campaign.id);
          this.logger.log(
            `Deleted ZNS campaign ${campaign.id}: ${campaign.name}`,
          );
        } else {
          results.push({
            id: campaign.id,
            name: campaign.name,
            success: false,
            reason: 'Lỗi khi xóa chiến dịch',
          });
          failedDeletes.push(campaign.id);
        }
      } catch (error) {
        this.logger.error(
          `Failed to delete ZNS campaign ${campaign.id}: ${error.message}`,
        );
        results.push({
          id: campaign.id,
          name: campaign.name,
          success: false,
          reason: error.message || 'Lỗi không xác định',
        });
        failedDeletes.push(campaign.id);
      }
    }

    const successCount = successfulDeletes.length;
    const failureCount = failedDeletes.length;
    const totalRequested = campaignIds.length;

    const message =
      failureCount > 0
        ? `Đã xóa ${successCount}/${totalRequested} chiến dịch. ${failureCount} chiến dịch không thể xóa.`
        : `Đã xóa thành công ${successCount} chiến dịch.`;

    this.logger.log(
      `Bulk delete completed: ${successCount} success, ${failureCount} failed`,
    );

    return {
      totalRequested,
      successCount,
      failureCount,
      successfulDeletes,
      failedDeletes,
      results,
      message,
    };
  }

  /**
   * Chạy chiến dịch ngay lập tức
   */
  async runCampaign(
    userId: number,
    campaignId: number,
  ): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch ZNS',
        );
      }

      // Chỉ cho phép chạy chiến dịch ở trạng thái DRAFT hoặc SCHEDULED
      if (
        ![
          ZaloZnsCampaignStatus.DRAFT,
          ZaloZnsCampaignStatus.SCHEDULED,
        ].includes(campaign.status)
      ) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_RUN,
          'Chiến dịch không ở trạng thái có thể chạy',
        );
      }

      // Lấy danh sách số điện thoại từ campaign và chuẩn bị dữ liệu cá nhân hóa
      let phoneList: string[] = [];
      let personalizedDataMap: Map<string, Record<string, any>> = new Map();

      if (campaign.phoneList) {
        phoneList = campaign.phoneList;
        // Với phone list, không có cá nhân hóa, sử dụng template data chung
        for (const phone of phoneList) {
          personalizedDataMap.set(phone, campaign.templateData);
        }
      } else if (campaign.segmentId) {
        const segment = await this.userSegmentRepository.findByIdAndUserId(
          campaign.segmentId,
          campaign.userId,
        );
        if (segment) {
          const audiences =
            await this.userAudienceRepository.findBySegmentConditions(
              campaign.userId,
              segment.criteria || [],
            );

          // Nếu có cấu hình cá nhân hóa, tạo template data cho từng audience
          if (campaign.personalizationConfig?.usePersonalization) {
            for (const audience of audiences) {
              if (audience.phoneNumber) {
                const customFields =
                  await this.userAudienceCustomFieldRepository.find({
                    where: { audienceId: audience.id },
                  });
                const personalizedData =
                  this.znsPersonalizationService.createPersonalizedTemplateData(
                    audience,
                    customFields,
                    campaign.personalizationConfig,
                  );
                personalizedDataMap.set(audience.phoneNumber, personalizedData);
                phoneList.push(audience.phoneNumber);
              }
            }
          } else {
            // Không cá nhân hóa, sử dụng template data chung
            phoneList = audiences
              .map((a) => a.phoneNumber)
              .filter((phone): phone is string => phone !== null);
            for (const phone of phoneList) {
              personalizedDataMap.set(phone, campaign.templateData);
            }
          }
        }
      } else if (campaign.audienceIds) {
        const audiences = await this.userAudienceRepository.findByIdsAndUserId(
          campaign.audienceIds,
          campaign.userId,
        );

        // Nếu có cấu hình cá nhân hóa, tạo template data cho từng audience
        if (campaign.personalizationConfig?.usePersonalization) {
          for (const audience of audiences) {
            if (audience.phoneNumber) {
              const customFields =
                await this.userAudienceCustomFieldRepository.find({
                  where: { audienceId: audience.id },
                });
              const personalizedData =
                this.znsPersonalizationService.createPersonalizedTemplateData(
                  audience,
                  customFields,
                  campaign.personalizationConfig,
                );
              personalizedDataMap.set(audience.phoneNumber, personalizedData);
              phoneList.push(audience.phoneNumber);
            }
          }
        } else {
          // Không cá nhân hóa, sử dụng template data chung
          phoneList = audiences
            .map((a) => a.phoneNumber)
            .filter((phone): phone is string => phone !== null);
          for (const phone of phoneList) {
            personalizedDataMap.set(phone, campaign.templateData);
          }
        }
      }

      if (phoneList.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Không có số điện thoại hợp lệ để gửi',
        );
      }

      // Cập nhật trạng thái thành SCHEDULED (sẽ được worker xử lý)
      const now = Date.now();
      await this.znsCampaignRepository.update(campaignId, {
        status: ZaloZnsCampaignStatus.SCHEDULED,
        startedAt: now,
        updatedAt: now,
      });

      // Tạo job để gửi ZNS - đẩy vào queue để worker bên khác xử lý
      // Chuyển đổi Map thành object để serialize
      const personalizedDataObject: Record<string, Record<string, any>> = {};
      personalizedDataMap.forEach((data, phone) => {
        personalizedDataObject[phone] = data;
      });

      const jobData: SendZnsCampaignJobData = {
        campaignId,
        oaId: campaign.oaId,
        templateId: campaign.templateId,
        templateData: campaign.templateData, // Fallback data
        personalizedDataMap: personalizedDataObject, // Personalized data cho từng số điện thoại
        phoneList: phoneList,
        batchSize: 10, // Gửi 10 tin nhắn mỗi batch
        batchDelay: 1000, // Delay 1 giây giữa các batch
      };

      const job = await this.znsQueue.add(
        ZaloZnsJobName.SEND_ZNS_CAMPAIGN,
        jobData,
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
        },
      );

      // Lưu job ID
      await this.znsCampaignRepository.update(campaignId, {
        jobIds: [job.id.toString()],
        updatedAt: Date.now(),
      });

      this.logger.log(`Started ZNS campaign ${campaignId} with job ${job.id}`);

      const updatedCampaign =
        await this.znsCampaignRepository.findById(campaignId);
      if (!updatedCampaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch sau khi chạy',
        );
      }

      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to run ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_RUN_FAILED,
        'Không thể chạy chiến dịch ZNS',
      );
    }
  }

  /**
   * Hủy chiến dịch
   */
  async cancelCampaign(
    userId: number,
    campaignId: number,
  ): Promise<ZaloZnsCampaign> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch ZNS',
        );
      }

      // Chỉ cho phép hủy chiến dịch ở trạng thái SCHEDULED
      if (campaign.status !== ZaloZnsCampaignStatus.SCHEDULED) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_INVALID_STATUS_FOR_CANCEL,
          'Chỉ có thể hủy chiến dịch đã lên lịch',
        );
      }

      // Hủy các job đang chạy
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        for (const jobId of campaign.jobIds) {
          try {
            const job = await this.znsQueue.getJob(jobId);
            if (job) {
              await job.remove();
            }
          } catch (error) {
            this.logger.warn(`Failed to remove job ${jobId}: ${error.message}`);
          }
        }
      }

      // Cập nhật trạng thái thành FAILED (hủy bỏ)
      const updatedCampaign = await this.znsCampaignRepository.update(
        campaignId,
        {
          status: ZaloZnsCampaignStatus.FAILED,
          errorMessage: 'Chiến dịch đã bị hủy bởi người dùng',
          updatedAt: Date.now(),
        },
      );

      if (!updatedCampaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch sau khi hủy',
        );
      }

      this.logger.log(
        `Cancelled ZNS campaign ${campaignId} for user ${userId}`,
      );
      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Failed to cancel ZNS campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CANCEL_FAILED,
        'Không thể hủy chiến dịch ZNS',
      );
    }
  }

  /**
   * Tạo job gửi ZNS đơn lẻ
   */
  async createSingleZnsJob(
    userId: number,
    integrationId: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    campaignId?: number,
    trackingId?: string,
  ): Promise<string> {
    try {
      // Lấy oaId từ integrationId
      const oaId = await this.zaloOAIntegrationService.getOaIdFromIntegrationId(
        integrationId,
        userId,
        undefined, // employeeId
      );

      const jobData: SendZnsJobData = {
        oaId,
        phone,
        templateId,
        templateData,
        campaignId,
        trackingId:
          trackingId ||
          `single_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      };

      const job = await this.znsQueue.add(ZaloZnsJobName.SEND_ZNS, jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      });

      this.logger.log(`Created single ZNS job ${job.id} for phone ${phone}`);
      return job.id.toString();
    } catch (error) {
      this.logger.error(`Failed to create single ZNS job: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_JOB_CREATION_FAILED,
        'Không thể tạo job gửi ZNS',
      );
    }
  }

  /**
   * Tạo job gửi batch ZNS
   */
  async createBatchZnsJob(
    userId: number,
    integrationId: string,
    messages: Array<{
      phone: string;
      templateId: string;
      templateData: Record<string, any>;
      trackingId?: string;
    }>,
    campaignId?: number,
    batchIndex: number = 0,
    totalBatches: number = 1,
  ): Promise<string> {
    try {
      // Lấy oaId từ integrationId
      const oaId = await this.zaloOAIntegrationService.getOaIdFromIntegrationId(
        integrationId,
        userId,
        undefined, // employeeId
      );

      const jobData: SendBatchZnsJobData = {
        oaId,
        messages,
        campaignId,
        batchIndex,
        totalBatches,
      };

      const job = await this.znsQueue.add(
        ZaloZnsJobName.SEND_BATCH_ZNS,
        jobData,
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 50,
        },
      );

      this.logger.log(
        `Created batch ZNS job ${job.id} with ${messages.length} messages`,
      );
      return job.id.toString();
    } catch (error) {
      this.logger.error(`Failed to create batch ZNS job: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_JOB_CREATION_FAILED,
        'Không thể tạo job gửi batch ZNS',
      );
    }
  }

  /**
   * Cập nhật trạng thái campaign theo templateId và oaId
   * @param oaId OA ID
   * @param templateId Template ID
   * @param status Trạng thái mới
   */
  async updateCampaignStatusByTemplateIdAndOaId(
    oaId: string,
    templateId: string,
    status: ZaloZnsCampaignStatus,
  ): Promise<number> {
    const affected =
      await this.znsCampaignRepository.updateStatusByTemplateIdAndOaId(
        oaId,
        templateId,
        status,
      );
    this.logger.log(
      `Đã cập nhật trạng thái ${status} cho ${affected} campaign với oaId=${oaId}, templateId=${templateId}`,
    );
    return affected;
  }
}
