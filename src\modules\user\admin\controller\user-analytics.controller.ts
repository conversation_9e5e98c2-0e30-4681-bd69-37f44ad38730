import {
  Controller,
  Get,
  Query,
  UseGuards,
  Param,
  ParseIntPipe,
  Delete,
  Patch,
  Body,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { UserAnalyticsService } from '../services/user-analytics.service';
import {
  LoginHistoryQueryDto,
  DeviceAnalyticsQueryDto,
  LoginHistoryResponseDto,
  DeviceInfoResponseDto,
  UserAnalyticsOverviewDto,
  LoginTimeSeriesDataDto,
  DeviceTimeSeriesDataDto,
  AuthMethodStatsDto,
  BrowserStatsDto,
  OperatingSystemStatsDto,
  LoginChartQueryDto,
  LoginChartResponseDto,
  PlatformEnum,
  UserTypeStatsDto,
} from '../dto/user-analytics.dto';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý API thống kê người dùng cho admin
 */
@Controller('admin/user-analytics')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_USER_ANALYTICS)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  LoginHistoryResponseDto,
  DeviceInfoResponseDto,
  UserAnalyticsOverviewDto,
  LoginTimeSeriesDataDto,
  DeviceTimeSeriesDataDto,
  AuthMethodStatsDto,
  BrowserStatsDto,
  OperatingSystemStatsDto,
  LoginChartQueryDto,
  LoginChartResponseDto,
  UserTypeStatsDto,
)
export class UserAnalyticsController {
  constructor(private readonly userAnalyticsService: UserAnalyticsService) {}

  /**
   * Lấy lịch sử đăng nhập với phân trang
   */
  @Get('login-history')
  @ApiOperation({
    summary: 'Lấy lịch sử đăng nhập của người dùng',
    description:
      'API cho admin xem lịch sử đăng nhập của tất cả người dùng với các bộ lọc và phân trang',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử đăng nhập thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(LoginHistoryResponseDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getLoginHistory(
    @Query() query: LoginHistoryQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<LoginHistoryResponseDto>>> {
    const result = await this.userAnalyticsService.getLoginHistory(query);
    return ApiResponseDto.success(result, 'Lấy lịch sử đăng nhập thành công');
  }

  /**
   * Lấy thông tin thiết bị với phân trang
   */
  @Get('devices')
  @ApiOperation({
    summary: 'Lấy thông tin thiết bị của người dùng',
    description:
      'API cho admin xem thông tin thiết bị của tất cả người dùng với các bộ lọc và phân trang',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(DeviceInfoResponseDto) },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getDeviceAnalytics(
    @Query() query: DeviceAnalyticsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<DeviceInfoResponseDto>>> {
    const result = await this.userAnalyticsService.getDeviceAnalytics(query);
    return ApiResponseDto.success(result, 'Lấy thông tin thiết bị thành công');
  }

  /**
   * Lấy thống kê tổng quan
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan về người dùng',
    description:
      'API cho admin xem các số liệu thống kê tổng quan về thiết bị và đăng nhập',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê tổng quan thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(UserAnalyticsOverviewDto) },
          },
        },
      ],
    },
  })
  async getOverviewStats(): Promise<ApiResponseDto<UserAnalyticsOverviewDto>> {
    const result = await this.userAnalyticsService.getOverviewStats();
    return ApiResponseDto.success(result, 'Lấy thống kê tổng quan thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ đăng nhập theo thời gian
   */
  @Get('login-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ đăng nhập theo thời gian',
    description:
      'API cho admin xem biểu đồ số lượng đăng nhập với tự động tính toán đơn vị thời gian phù hợp (giờ/ngày/tuần/tháng/năm)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ đăng nhập thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(LoginChartResponseDto) },
          },
        },
      ],
    },
  })
  async getLoginTimeSeries(
    @Query() query: LoginChartQueryDto,
  ): Promise<ApiResponseDto<LoginChartResponseDto>> {
    const result = await this.userAnalyticsService.getLoginTimeSeries(query);
    return ApiResponseDto.success(
      result,
      'Lấy dữ liệu biểu đồ đăng nhập thành công',
    );
  }

  /**
   * Lấy dữ liệu biểu đồ thiết bị theo thời gian
   */
  @Get('device-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ thiết bị theo thời gian',
    description:
      'API cho admin xem biểu đồ số lượng thiết bị mới theo ngày trong khoảng thời gian',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(DeviceTimeSeriesDataDto) },
            },
          },
        },
      ],
    },
  })
  async getDeviceTimeSeries(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ): Promise<ApiResponseDto<DeviceTimeSeriesDataDto[]>> {
    const result = await this.userAnalyticsService.getDeviceTimeSeries(
      startDate,
      endDate,
    );
    return ApiResponseDto.success(
      result,
      'Lấy dữ liệu biểu đồ thiết bị thành công',
    );
  }

  /**
   * Lấy thống kê theo phương thức xác thực
   */
  @Get('auth-method-stats')
  @ApiOperation({
    summary: 'Lấy thống kê theo phương thức xác thực',
    description:
      'API cho admin xem thống kê sử dụng các phương thức xác thực khác nhau',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê phương thức xác thực thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(AuthMethodStatsDto) },
            },
          },
        },
      ],
    },
  })
  async getAuthMethodStats(): Promise<ApiResponseDto<AuthMethodStatsDto[]>> {
    const result = await this.userAnalyticsService.getAuthMethodStats();
    return ApiResponseDto.success(
      result,
      'Lấy thống kê phương thức xác thực thành công',
    );
  }

  /**
   * Lấy thống kê theo trình duyệt
   */
  @Get('browser-stats')
  @ApiOperation({
    summary: 'Lấy thống kê theo trình duyệt',
    description: 'API cho admin xem thống kê phân bố thiết bị theo trình duyệt',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê trình duyệt thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(BrowserStatsDto) },
            },
          },
        },
      ],
    },
  })
  async getBrowserStats(): Promise<ApiResponseDto<BrowserStatsDto[]>> {
    const result = await this.userAnalyticsService.getBrowserStats();
    return ApiResponseDto.success(
      result,
      'Lấy thống kê trình duyệt thành công',
    );
  }

  /**
   * Lấy thống kê theo hệ điều hành
   */
  @Get('os-stats')
  @ApiOperation({
    summary: 'Lấy thống kê theo hệ điều hành',
    description:
      'API cho admin xem thống kê phân bố thiết bị theo hệ điều hành',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê hệ điều hành thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(OperatingSystemStatsDto) },
            },
          },
        },
      ],
    },
  })
  async getOperatingSystemStats(): Promise<
    ApiResponseDto<OperatingSystemStatsDto[]>
  > {
    const result = await this.userAnalyticsService.getOperatingSystemStats();
    return ApiResponseDto.success(
      result,
      'Lấy thống kê hệ điều hành thành công',
    );
  }

  /**
   * Lấy thống kê loại tài khoản người dùng (biểu đồ tròn)
   */
  @Get('user-type-stats')
  @ApiOperation({
    summary: 'Lấy thống kê loại tài khoản người dùng',
    description:
      'API cho admin xem thống kê tỷ lệ người dùng cá nhân và doanh nghiệp dưới dạng biểu đồ tròn',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê loại tài khoản thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(UserTypeStatsDto) },
            },
          },
        },
      ],
    },
  })
  async getUserTypeStats(): Promise<ApiResponseDto<UserTypeStatsDto[]>> {
    const result = await this.userAnalyticsService.getUserTypeStats();
    return ApiResponseDto.success(
      result,
      'Lấy thống kê loại tài khoản thành công',
    );
  }
}
