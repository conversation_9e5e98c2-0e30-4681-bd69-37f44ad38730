import {
  Controller,
  Post,
  Param,
  UseGuards,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ExecutionAdminService } from '../services/execution-admin.service';
import { WorkflowExecutionResponseDto, NodeExecutionResponseDto } from '../../dto/response/execution-response.dto';

/**
 * Controller xử lý các API execution cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  WorkflowExecutionResponseDto,
  NodeExecutionResponseDto,
  ApiErrorResponseDto,
)
@ApiBearerAuth()
@UseGuards(JwtEmployeeGuard)
@Controller('admin/workflow')
export class ExecutionAdminController {
  constructor(private readonly executionAdminService: ExecutionAdminService) {}

  /**
   * Thực thi workflow (admin)
   */
  @Post(':workflowId/execute')
  @ApiOperation({ 
    summary: 'Thực thi workflow (Admin)',
    description: 'Admin thực thi một workflow bất kỳ. API này sẽ tạo một execution record và queue job để xử lý workflow.'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần thực thi',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution đã được khởi tạo thành công',
    schema: ApiResponseDto.getSchema(WorkflowExecutionResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy workflow',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Workflow không thể thực thi (invalid configuration)',
    type: ApiErrorResponseDto,
  })
  async executeWorkflow(
    @CurrentEmployee('id') employeeId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
  ): Promise<ApiResponseDto<WorkflowExecutionResponseDto>> {
    const result = await this.executionAdminService.executeWorkflow(employeeId, workflowId);
    return ApiResponseDto.success(result, 'Workflow execution đã được khởi tạo thành công');
  }

  /**
   * Thực thi node đơn lẻ (admin)
   */
  @Post(':workflowId/node/:nodeId/execute')
  @ApiOperation({ 
    summary: 'Thực thi node đơn lẻ (Admin)',
    description: 'Admin thực thi một node cụ thể trong workflow. Thường được sử dụng để test hoặc debug node.'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow chứa node',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'nodeId',
    description: 'ID của node cần thực thi',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node execution đã được khởi tạo thành công',
    schema: ApiResponseDto.getSchema(NodeExecutionResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy workflow hoặc node',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Node không thể thực thi (invalid configuration)',
    type: ApiErrorResponseDto,
  })
  async executeNode(
    @CurrentEmployee('id') employeeId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
  ): Promise<ApiResponseDto<NodeExecutionResponseDto>> {
    const result = await this.executionAdminService.executeNode(employeeId, workflowId, nodeId);
    return ApiResponseDto.success(result, 'Node execution đã được khởi tạo thành công');
  }
}
