import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsObject, ValidateNested, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { TNodePosition } from '../../interfaces';

/**
 * DTO cho việc tạo node mới trong workflow
 */
export class CreateNodeDto {
  @ApiProperty({
    description: 'Tên hiển thị của node',
    example: 'HTTP Request to API',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Vị trí của node trên canvas',
    example: { x: 100, y: 200 },
  })
  @IsObject()
  @ValidateNested()
  @Type(() => NodePositionDto)
  position: TNodePosition;

  @ApiProperty({
    description: 'ID của node definition template',
    example: 'uuid-node-definition',
  })
  @IsUUID()
  @IsNotEmpty()
  nodeDefinitionId: string;

  @ApiProperty({
    description: 'Ghi chú cho node',
    example: 'Node này gọi API để lấy dữ liệu user',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

/**
 * DTO cho vị trí node
 */
export class NodePositionDto implements TNodePosition {
  @ApiProperty({
    description: 'Tọa độ X trên canvas',
    example: 100,
  })
  @IsNotEmpty()
  x: number;

  @ApiProperty({
    description: 'Tọa độ Y trên canvas', 
    example: 200,
  })
  @IsNotEmpty()
  y: number;
}
